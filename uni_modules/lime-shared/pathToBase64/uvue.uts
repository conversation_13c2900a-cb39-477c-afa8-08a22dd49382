// @ts-nocheck
// import { processFile, ProcessFileOptions } from '@/uni_modules/lime-file-utils'
export function pathToBase64(path : string) : Promise<string> {
	console.error('pathToBase64: 当前环境不支持，请使用 【lime-file-utils】')
	// return new Promise((resolve, reject) => {
	// 	processFile({
	// 		type: 'toDataURL',
	// 		path,
	// 		success(res : string) {
	// 			resolve(res)
	// 		},
	// 		fail(err: any){
	// 			reject(err)
	// 		}
	// 	} as ProcessFileOptions)
	// })
}