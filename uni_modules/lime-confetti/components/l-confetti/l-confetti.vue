<template>
	<!-- #ifdef APP-VUE -->
	<view ref="confettiRef" id="confettiRef" class="l-confetti" 
		:option="option" 
		:destroy="destroy" 
		:change:destroy="confetti._remove"
		:change:option="confetti.play">
		<canvas class="l-confetti__canvas" :canvas-id="canvasId" :id="canvasId" type="2d" v-if="show && !isDestroy"></canvas>
	</view>
	<!-- #endif -->
	<!-- #ifndef APP-VUE -->
	<view ref="confettiRef" id="confettiRef" class="l-confetti">
		<canvas class="l-confetti__canvas" :canvas-id="canvasId" :id="canvasId" type="2d" v-if="show && !isDestroy"></canvas>
	</view>
	<!-- #endif -->
</template>
<!-- #ifdef APP-VUE -->
<script module="confetti" lang="renderjs">
	import {Confetti} from './confetti';
	export default {
		data() {
			return {
				_confetti: null,
				_canvas: null,
				_id: -1
			}
		},
		methods: {
			// #ifdef APP-VUE 
			play(option) {
				if(!this._confetti){
					this.$nextTick(()=>{
						this._init();
						this._play(option)
					})
				} else {
					this._play(option)
				}
			},
			_play(option) {
				if(option && this._confetti) {
					const {options} = JSON.parse(option);
					this._confetti.play(options);
				}
			},
			_remove(value) {
				if(value == -1 || isNaN(value)) return
				this._id = value
				const el = document.getElementById('confettiRef') || this.$ownerInstance.$el;
				if(!el) return
				let list = el.querySelectorAll('canvas');
				list.forEach((canvas)=>{
					canvas.remove()
				})
				this._confetti = null
				this._canvas = null
			},
			_done() {
				if(this._canvas){
					this._canvas.remove();
					this._remove(this._id)
				}
				this.$ownerInstance.callMethod('done')
			},
			_init(){
				const el = document.getElementById('confettiRef') || this.$ownerInstance.$el;
				this._remove(this._id)
				this._canvas = document.createElement('canvas');
				this._canvas.id = `confetti_${this._id}`
				this._canvas.style = 'width: 100%; height: 100%;'
				el.appendChild(this._canvas);
				let __id = this._id
				this._confetti = new Confetti(this._canvas, {resize: true, done: ()=> {
					if(this._id != __id) return
					this._done()
				}})
			}
			// #endif 
		},
		mounted() {
			
		},
	}
</script>
<!-- #endif -->
<script lang="ts">
	// @ts-nocheck
	import {getCurrentInstance, watch, onUnmounted, defineComponent, ref, computed, onMounted, nextTick} from '@/uni_modules/lime-shared/vue';
	// import {useCanvas} from './useCanvas';
	import {getCanvas} from './getCanvas';
	import {Confetti} from './confetti';
	import type {Options} from './type'
	export default defineComponent({
		name: 'l-confetti',
		emits: ['done'],
		props: {
			renderjs:{
				type: Boolean,
				 default: false
			}
		},
		setup(props, {expose, emit}) {
			const app = getCurrentInstance();
			const canvasId = ref(`l-confetti-${app.uid}`);
			let isDestroy = ref(false)
			let destroy = ref(-1)
			let confetti2 = null
			const option = ref(null);
			const count = ref(0)
			const show = computed(() => {
				// #ifdef APP-VUE
				if(props.renderjs) {
					if(destroy.value == -1){
						destroy.value = 0
					}
					return false
				}
				// #endif
				return true
			})
			//播放
			const __play = (options) =>{
				// #ifdef APP-VUE
				if(props.renderjs) {
					count.value++
					option.value = JSON.stringify({options, count: count.value})
					return
				} 
				// #endif
				if(!confetti2) return
				confetti2?.play(options)
			}
			const play = (options: Options) => {
				if(isDestroy.value) {
					// #ifndef APP-VUE
					canvasId.value = `l-confetti-${app.uid + Date.now()}`
					isDestroy.value = false;
					// #endif
					
					// #ifdef APP-VUE
					if(!props.renderjs) {
						canvasId.value = `l-confetti-${app.uid + Date.now()}`
						isDestroy.value = false;
					} else {
						__play(options)
						return
					}
					// #endif
					nextTick(()=>{
						createConfetti()
						setTimeout(()=>{
							__play(options)
						},50)
					})
				} else {
					__play(options)
				}
			}
			// 播放完成
			const done = () => {
				emit('done')
			}
			// 销毁
			const destroyCanvas = () => {
				isDestroy.value = true;
				destroy.value++
				confetti2 = null;
				emit('destroy');
			}
			
			const createConfetti = ()=>{
				getCanvas(`#${canvasId.value}` ,{context: app.proxy}).then((res)=>{
					confetti2 = new Confetti(res, {resize: true, done})
					return Promise.resolve()
				})
				
			}
			onMounted(()=>{
				nextTick(createConfetti)
			})
			onUnmounted(() => {
				isDestroy.value = true
				confetti2 = null
			})
			// #ifdef VUE3
			expose({
				play,
				destroyCanvas
			})
			// #endif
			return {
				isDestroy,
				canvasId,
				destroy,
				show,
				option,
				count,
				done,
				// #ifndef VUE3
				// 由于VUE2 是通过返回暴露方法，故使用返回
				destroyCanvas,
				play
				// #endif
			}
		}
	})
</script>
<style lang="scss">
	.l-confetti {
		width: 100%;
		height: 100%;
		&__canvas {
			width: 100%;
			height: 100%;
		}
	}
</style>
