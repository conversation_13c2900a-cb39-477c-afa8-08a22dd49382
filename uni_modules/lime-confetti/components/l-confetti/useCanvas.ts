// @ts-nocheck
import {computed, onMounted, ref} from '@/uni_modules/lime-shared/vue';
import {getRect} from '@/uni_modules/lime-shared/getRect'
import {canIUseCanvas2d} from '@/uni_modules/lime-shared/canIUseCanvas2d'
import {Canvas} from './type'


export function useCanvas(selector: string, options) {
	const {context} = options
	const isCanvas2d = canIUseCanvas2d()
	const canvas = ref<Canvas>()
	onMounted(() => {
		if(isCanvas2d) {
			getRect(selector, context, true).then((res)=> {
				if(res) {
					const {pixelRatio} = uni.getSystemInfoSync()
					const {bottom, width, height, left, node} = res
					node.width = width * pixelRatio
					node.height = height * pixelRatio
					
					const context = node.getContext('2d')
					context.scale(pixelRatio, pixelRatio)
					canvas.value = {
						bottom, width, height, left, 
						getContext: () => context, 
						requestAnimationFrame: node.requestAnimationFrame,
						cancelAnimationFrame: node.cancelAnimationFrame,
					}
				}
			})
		} else {
			getRect(selector, context).then(res => {
				console.log('?89', res)
				if(res) {
					const ctx = uni.createCanvasContext(selector.replace('#', ''), context)
					const {bottom, width, height, left} = res
					console.log('?890', res)
					canvas.value = {
						bottom, width, height, left, 
						getContext: () => ctx//uni.createCanvasContext(selector.replace('#', ''), context), 
					}
				}
			})
		}
	})
	return computed(() => canvas.value) 
}