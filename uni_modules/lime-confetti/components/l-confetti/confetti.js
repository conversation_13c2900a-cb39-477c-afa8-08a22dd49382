function t(t,e){for(var n=0;e.length>n;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}var e=0;function n(t){return"__private_"+e+++"_"+t}var a="undefined"!=typeof window,i=n("defaults"),o=function(){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Object.defineProperty(this,i,{writable:!0,value:{particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1}}),this.canvas=void 0,this.play=void 0,this.raf=void 0,this.done=void 0,this.options=void 0,this.canvas=t,this.options=n,this.done=function(){return n.done&&n.done()},this._requestAnimationFrame(),this.play=this.confettiCannon(t,n)}var n,o,r;return n=e,o=[{key:"_requestAnimationFrame",value:function(){var t,e,n={},i=0,o=a?window.requestAnimationFrame:this.canvas.requestAnimationFrame,r=a?window.cancelAnimationFrame:this.canvas.cancelAnimationFrame;"function"==typeof o&&"function"==typeof r?(t=function(t){var e=Math.random();return n[e]=o((function a(r){i===r||r>i+16-1?(i=r,delete n[e],t()):n[e]=o(a)})),e},e=function(t){n[t]&&r(n[t])}):(t=function(t){return setTimeout(t,16)},e=function(t){return clearTimeout(t)}),this.raf={frame:t,cancel:e}}},{key:"convert",value:function(t,e){return e?e(t):t}},{key:"isOk",value:function(t){return!(null==t)}},{key:"prop",value:function(t,e,n){return this.convert(t&&this.isOk(t[e])?t[e]:function(t,e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new TypeError("attempted to use private field on non-instance");return t}(this,i)[i][e],n)}},{key:"onlyPositiveInt",value:function(t){return 0>t?0:Math.floor(t)}},{key:"randomInt",value:function(t,e){return Math.floor(Math.random()*(e-t))+t}},{key:"toDecimal",value:function(t){return parseInt(t,16)}},{key:"colorsToRgb",value:function(t){return t.map(this.hexToRgb.bind(this))}},{key:"hexToRgb",value:function(t){var e=(t+"").replace(/[^0-9a-f]/gi,"");return 6>e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),{r:this.toDecimal(e.substring(0,2)),g:this.toDecimal(e.substring(2,4)),b:this.toDecimal(e.substring(4,6))}}},{key:"getOrigin",value:function(t){var e=this.prop(t,"origin",Object);return e.x=this.prop(e,"x",Number),e.y=this.prop(e,"y",Number),e}},{key:"setCanvasWindowSize",value:function(t){a&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight)}},{key:"setCanvasRectSize",value:function(t){if(a&&t.getBoundingClientRect){var e=t.getBoundingClientRect();t.width=e.width,t.height=e.height}}},{key:"getCanvas",value:function(t){if(a){var e=document.createElement("canvas");return e.style.position="fixed",e.style.top="0px",e.style.left="0px",e.style.pointerEvents="none",e.style.zIndex=t,e}}},{key:"_draw",value:function(t){t.draw&&t.draw(!0)}},{key:"ellipse",value:function(t,e,n,a,i,o,r,l,s){t.save(),t.translate(e,n),t.rotate(o),t.scale(a,i),t.arc(0,0,1,r,l,s),t.fill(),t.restore()}},{key:"randomPhysics",value:function(t){var e=t.angle*(Math.PI/180),n=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*t.startVelocity+Math.random()*t.startVelocity,angle2D:-e+(.5*n-Math.random()*n),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*t.gravity,ovalScalar:.6,scalar:t.scalar}}},{key:"updateFetti",value:function(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift,e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity,e.wobble+=e.wobbleSpeed,e.velocity*=e.decay,e.tiltAngle+=.1,e.tiltSin=Math.sin(e.tiltAngle),e.tiltCos=Math.cos(e.tiltAngle),e.random=Math.random()+2,e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble),e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble);var n=e.tick++/e.totalTicks,a=e.x+e.random*e.tiltCos,i=e.y+e.random*e.tiltSin,o=e.wobbleX+e.random*e.tiltCos,r=e.wobbleY+e.random*e.tiltSin;if(t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-n)+")",t.beginPath(),"circle"===e.shape)t.ellipse?t.ellipse(e.x,e.y,Math.abs(o-a)*e.ovalScalar,Math.abs(r-i)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):this.ellipse(t,e.x,e.y,Math.abs(o-a)*e.ovalScalar,Math.abs(r-i)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI);else if("star"===e.shape)for(var l=Math.PI/2*3,s=4*e.scalar,c=8*e.scalar,h=5,u=Math.PI/h;h--;)t.lineTo(e.x+Math.cos(l)*c,e.y+Math.sin(l)*c),t.lineTo(e.x+Math.cos(l+=u)*s,e.y+Math.sin(l)*s),l+=u;else t.moveTo(Math.floor(e.x),Math.floor(e.y)),t.lineTo(Math.floor(e.wobbleX),Math.floor(i)),t.lineTo(Math.floor(o),Math.floor(r)),t.lineTo(Math.floor(a),Math.floor(e.wobbleY));return t.closePath(),t.fill(),this._draw(t),e.totalTicks>e.tick}},{key:"animate",value:function(t,e,n,a,i){var o,r,l=this,s=e.slice(),c=t.getContext("2d"),h=this.raf,u=new Promise((function(e){var u=function(){o=r=null,null==c||c.clearRect(0,0,a.width,a.height),i(),e("")};o=h.frame((function e(){a.width||a.height||(n(t),a.width=t.width,a.height=t.height),c&&(c.clearRect(0,0,a.width,a.height),(s=s.filter((function(t){if(c)return l.updateFetti(c,t)}))).length?o=h.frame(e):u())})),r=u}));return{addFettis:function(t){return s=s.concat(t),u},canvas:t,promise:u,reset:function(){o&&h.cancel(o),r&&r()}}}},{key:"confettiCannon",value:function(t,e){var n,i=this,o=!t,r=!!this.prop(e||{},"resize"),l=this.prop(e,"disableForReducedMotion",Boolean),s=o?this.setCanvasWindowSize:this.setCanvasRectSize,c=!1,h="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches,u=function(e){var u=l||i.prop(e,"disableForReducedMotion",Boolean),d=i.prop(e,"zIndex",Number);if(u&&h)return new Promise((function(t){t("")}));o&&n?t=n.canvas:o&&!t&&(t=i.getCanvas(d),a&&document.body.appendChild(t)),r&&!c&&s(t);var f={width:t.width,height:t.height};function p(){f.width=f.height=0}return r&&a&&window.addEventListener("resize",p,!1),function(e,a,o){for(var r=i.prop(e,"particleCount",i.onlyPositiveInt.bind(i)),l=i.prop(e,"angle",Number),c=i.prop(e,"spread",Number),h=i.prop(e,"startVelocity",Number),u=i.prop(e,"decay",Number),d=i.prop(e,"gravity",Number),f=i.prop(e,"drift",Number),p=i.prop(e,"colors",i.colorsToRgb.bind(i)),v=i.prop(e,"ticks",Number),y=i.prop(e,"shapes"),m=i.prop(e,"scalar"),b=i.getOrigin(e),g=r,w=[],M=t.width*b.x,k=t.height*b.y;g--;)w.push(i.randomPhysics({x:M,y:k,angle:l,spread:c,startVelocity:h,color:p[g%p.length],shape:y[i.randomInt(0,y.length)],ticks:v,decay:u,gravity:d,drift:f,scalar:m}));return n?n.addFettis(w):(n=i.animate(t,w,s,a,o)).promise}(e,f,(function(){i.done(),n=null,r&&a&&window.removeEventListener("resize",p),o&&t&&(a&&document.body.removeChild(t),t=null,c=!1)}))};return u.reset=function(){n&&n.reset()},u}}],o&&t(n.prototype,o),r&&t(n,r),Object.defineProperty(n,"prototype",{writable:!1}),e}();export{o as Confetti,a as isWindow};
