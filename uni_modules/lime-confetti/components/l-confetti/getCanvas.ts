// @ts-nocheck
// import {computed, onMounted, ref} from '@/uni_modules/lime-shared/vue';
import {getRect} from '@/uni_modules/lime-shared/getRect'
import {canIUseCanvas2d} from '@/uni_modules/lime-shared/canIUseCanvas2d'
// import {Canvas} from './type'


export function getCanvas(selector: string, options) {
	const {context} = options
	const isCanvas2d = canIUseCanvas2d()
	return new Promise((resolve)=>{
		getRect(selector, context, isCanvas2d).then((res)=> {
			if(res.node) {
				const {pixelRatio} = uni.getSystemInfoSync()
				const {bottom, width, height, left, node} = res
				node.width = width * pixelRatio
				node.height = height * pixelRatio
				
				const context = node.getContext('2d')
				context.scale(pixelRatio, pixelRatio)
				const canvas = {
					bottom, width, height, left, 
					getContext: () => context, 
					requestAnimationFrame: node.requestAnimationFrame,
					cancelAnimationFrame: node.cancelAnimationFrame,
				}
				resolve(canvas)
			} else {
				const ctx = uni.createCanvasContext(selector.replace('#', ''), context)
				console.log('ctx', ctx)
				const {bottom, width, height, left} = res
				const canvas = {
					bottom, width, height, left, 
					getContext: () => ctx//uni.createCanvasContext(selector.replace('#', ''), context), 
				}
				resolve(canvas)
			}
		})
	})
}