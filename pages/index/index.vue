<template>
	<view class="index-pages topBg" :style="{ paddingTop: navBarHeight + 'px' }">
		<!-- <navbar titleText="这是标题"></navbar> -->
		<view class="user w-full flex-col">
			<view class="user-name">
				{{ greeting }}{{ user.name }}
			</view>
			<view class="user-value">
				{{ value }}
			</view>
		</view>

		<view class="plancard flex-col">
			<view class="plancard-time" v-if="adviceSleep">
				<view class="plancard-time-label flex-row">
					<image src="https://cbti.zhisongkeji.com/uniapp-static/icon1.png" mode=""></image>
					<view class="label">
						睡眠计划
					</view>
					<view class="nextlabel">
						根据你的睡眠日记动态调整
					</view>
				</view>
				<view class="plancard-time-content flex-row">
					<view class="timeitem flex-col">
						<view class="timeitem-label">
							上床时间
						</view>
						<view class="timeitem-value">
							{{ adviceSleep.sleepTime }}
						</view>
					</view>
					<view class="timeinterval">
						{{ adviceSleep.sleepTimeLong }}
					</view>
					<view class="timeitem flex-col">
						<view class="timeitem-label">
							起床时间
						</view>
						<view class="timeitem-value">
							{{ adviceSleep.getUpTime }}
						</view>
					</view>
				</view>
			</view>
			<view class="plancard-tab" v-if="createsleepDiary">
				<view class="plancard-tab-item flex-row" @click="goPage('diaryCreate')">
					<image src="https://cbti.zhisongkeji.com/uniapp-static/icon2.png" mode=""></image>
					<view class="content flex-col">
						<view class="content-label">
							写睡眠日记
						</view>
						<view class="content-value">
							每日记录睡眠
						</view>
					</view>
				</view>
				<view class="plancard-tab-item flex-row" @click="goPage('weekly')">
					<image src="https://cbti.zhisongkeji.com/uniapp-static/icon3.png" mode=""></image>
					<view class="content flex-col">
						<view class="content-label">
							睡眠统计
						</view>
						<view class="content-value">
							睡眠质量分析
						</view>
					</view>
				</view>
			</view>
			<view class="plancard-board flex-col" v-else>
				<view class="plancard-board-date flex-row">
					<view class="changeicon " @click="changeBoardDate('left')">
						<image src="/static/img/left.png" alt="" />
					</view>
					{{ boardDate }}
					<view class="changeicon">
						<image v-if="boardDate != toadyformat" @click="changeBoardDate('right')"
							src="/static/img/right.png" alt="" />
					</view>
				</view>
				<view class="plancard-board-content flex-row" v-if="Object.keys(boardData).length != 0">
					<view class="bcontent flex-col">
						<view class="btime flex-row">
							<image class="btime-icon" src="https://cbti.zhisongkeji.com/uniapp-static/icon4.png"
								mode=""></image>
							<view class="btime-item flex-col">
								<view class="btime-item-label">
									睡眠时长
								</view>
								<view class="btime-item-value">
									{{ boardData.sleep_length }}
								</view>
							</view>
							<view class="line">
							</view>
							<view class="btime-item">
								<view class="btime-item-label">
									卧床时长
								</view>
								<view class="btime-item-value">
									{{ boardData.bedridden_length }}
								</view>
							</view>
						</view>
						<view class="btime flex-row">
							<image class="btime-icon" src="https://cbti.zhisongkeji.com/uniapp-static/icon5.png"
								mode=""></image>
							<view class="btime-item flex-col">
								<view class="btime-item-label">
									入睡用时
								</view>
								<view class="btime-item-value">
									{{ boardData.fall_asleep }}
								</view>
							</view>
						</view>
						<view class="btime flex-row">
							<image class="btime-icon" src="https://cbti.zhisongkeji.com/uniapp-static/icon6.png"
								mode=""></image>
							<view class="btime-item flex-col">
								<view class="btime-item-label">
									起床
								</view>
								<view class="btime-item-value">
									{{ boardData.weakup_style }}
								</view>
							</view>
						</view>
						<view class="bbtn flex-row">
							<view class="w-btn diary" @click="goPage('diary')">
								查看日记
							</view>
							<view class="w-btn" @click="goPage('weekly')">
								睡眠统计
							</view>
						</view>
					</view>
					<view class="chart">
						<Pie v-if="boardData.sleep_efficiency != null" 
							:efficiency="boardData.sleep_efficiency" />
					</view>
				</view>
				<view class="plancard-board-content flex-row" v-else>
					<view class="empty w-full flex-col">
						<image class="empty-icon" src="https://cbti.zhisongkeji.com/uniapp-static/diaryempty.png"
							mode=""></image>
						<view class="empty-text">
							今日暂未记录睡眠
						</view>
						<view class="empty-btn" @click="goPage('diaryCreate')">
							<img src="https://cbti.zhisongkeji.com/uniapp-static/edit.png" alt="" />
							填写睡眠日记
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="planCalendar flex-col" v-if="endDate">
			<title label="睡眠方案" next="我的方案" path="/pages/course/course" />
			<view class="planCalendar-content">
				<calendar showType="week" :endDate="endDate" :process="process" @start="goPage('course')"
					:currentSchemeDayFinish='currentSchemeDayFinish' diaryDates="[]" />
			</view>
		</view>
		<view class="task w-full flex-col" v-if="medicalOrdemasksTotal > 0 || patientSchemeTasksTotal > 0">
			<view class="task-tab flex-row">
				<view :class="'task-tab-item ' + (taskCurrent == index ? 'currenttab' : '')" :key="index"
					v-for="(item, index) of tasktabs" @click="taskCurrent = index">
					{{ item }}
				</view>
			</view>
			<view class="task-content flex-col" v-if="taskCurrent == 0">
				<view class="taskitem flex-col" :key="item.id" v-for="item of medicalOrdemasks">
					<view :class="'taskitem-time ' + (item.status == 0 ? 'unfinish' : 'finish')">
						<view class="tlabel">
							<image v-if="item.status == 0" class="iconImg"
								src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
							</image>
							<image v-else class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png"
								mode="widthFix">
							</image>
							请在{{item.limitDate}}之前完成
						</view>
						<image v-if="item.status == 0" class="tstatus"
							src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
						</image>
						<image v-else class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png"
							mode="widthFix">
						</image>
					</view>
					<view class="taskitem-label">
						{{ item.label }}
					</view>
					<view class="taskitem-value">
						{{ item.showAll ? item.valueAll : item.value }}
					</view>
					<view class="taskitem-btn flex-row">
						<view class="w-btn expand" v-if="item.value" @click="item.showAll = !item.showAll ">
							{{ item.showAll ? '收起' : '展开全部' }}
						</view>
						<view class="w-btn finish" @click="finishMedicalOrder(item.id)" v-if="item.status == 0">
							我已完成
						</view>
					</view>
				</view>
				<view v-if="medicalOrdemasksTotal > 2" class="taskitem tasknext flex-row"
					@click="goPage('medicalOrde')">
					查看全部医嘱
					<image class="tasknext-icon" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
					</image>
				</view>
			</view>
			<view class="task-content flex-col" v-else>
				<view class="ptaskitem flex-row" :key="item.id" v-for="item of patientSchemeTasks"
					@click="handleMeasureClick(item)">
					<view class="pcontent">
						<view :class="'ptaskitem-time ' + (item.status != 2 ? 'unfinish' : 'finish')">
							<view class="tlabel">
								<image v-if="item.status != 2" class="iconImg"
									src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
								</image>
								<image v-else class="iconImg"
									src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png" mode="widthFix">
								</image>
								请在{{item.limitDate}}之前完成
							</view>
							<image v-if="item.status != 2" class="tstatus"
								src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
							</image>
							<image v-else class="tstatus"
								src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png" mode="widthFix">
							</image>
						</view>
						<view class="ptaskitem-label">
							<image class="ptaskitem-label-icon"
								src="https://cbti.zhisongkeji.com/uniapp-static/taskicon.png" mode="">
							</image>
							{{ item.measureName }}
						</view>
					</view>
					<image class="picon iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
					</image>
				</view>
				<view v-if="patientSchemeTasksTotal > 2" class="taskitem tasknext flex-row"
					@click="goPage('patientScheme')">
					查看全部量表
					<image class="tasknext-icon icon" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
					</image>
				</view>
			</view>

		</view>
		<view class="recordMood flex-col">
			<title label="心情记录" next="更多" path="/pages/myFeeling/myFeeling" />
			<view class="recordMood-content">
				<view class="plancard-tab">
					<view class="plancard-tab-item flex-row worried" @click="goPage('addworried')">
						<image src="https://cbti.zhisongkeji.com/uniapp-static/icon7.png" mode=""></image>
						<view class="content flex-col">
							<view class="content-label">
								添加忧虑
							</view>
							<view class="content-value">
								让睡眠变容易
							</view>
						</view>
					</view>
					<view class="plancard-tab-item flex-row diary" @click="goPage('feeling')">
						<image src="https://cbti.zhisongkeji.com/uniapp-static/icon8.png" mode=""></image>
						<view class="content flex-col">
							<view class="content-label">
								心情日记
							</view>
							<view class="content-value">
								保持正面信念
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="read flex-col">
			<title label="阅读推荐" next="更多" path="/pages/reads/reads" />
			<view class="read-content flex-col">
				<view class="readitem flex-row" :key="item.id" v-for="item of reads" @click="goReadInfo(item.id)">
					<view class="readitem-cover">
						<image v-if="item.titleImg"
							:src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${item.titleImg}`"
							mode="widthFix"></image>
					</view>
					<view class="readitem-content flex-col">
						<label class="label">
							{{ item.title }}
						</label>
						<view class="author">
							作者：{{ item.author }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		formatDate,
		isObjEmpty,
		getDay
	} from '../../utils/index.js'
	// import navbar from '@/components/navbar.vue'
	import calendar from '@/components/calendar/calendar.vue'
	import Pie from '@/components/pie.vue'
	import title from '@/components/title.vue'
	import {
		getPlanInfo,
		getMedicalOrderTask,
		getPatientSchemeTask,
		finishMedicalOrderTask,
		getInfo,
		getExecuteDetail,
		getSleepDiary,
		getCurrentSchemeGroupInfo,
		getCompelStatus
	} from '@/assets/api/index.js'

	import {
		useStore
	} from 'vuex'

	import {
		getGreeting
	}
	from '@/assets/utils/index.js'

	export default {
		components: {
			calendar,
			Pie,
			title
			// navbar
		},
		data() {
			const store = useStore();

			return {
				endDate: '', //睡眠方案的最后一天
				currentSchemeDay: [],
				currentSchemeDayFinish: [],
				plan: [],
				greeting: getGreeting(),
				value: '拥抱好睡眠，今天继续加油!',
				user: store.state.user,
				token: store.state.token,
				navBarHeight: store.state.navBarHeight || 87,
				adviceSleep: {}, // 睡眠计划
				createsleepDiary: true, // 写睡眠日记
				boardDate: formatDate(new Date(), 'MM月dd日'),
				boardDateParams: formatDate(new Date(), 'YYYY-MM-dd'),
				toadyformat: '',
				boardData: {},
				process: 40,
				tasktabs: ['医嘱任务', '量表任务'],
				taskCurrent: 0,
				medicalOrdemasksTotal: 0,
				medicalOrdemasks: [],
				patientSchemeTasksTotal: 0,
				patientSchemeTasks: [],
				reads: [],
				currentDay: 0,
				// showPie:false
			}
		},
		onShow() {
			console.log('如果已经登录', this.token)
			if (this.token) {
				this.initData()
			} else {
				if (uni.getStorageSync('doctorId')) {
					uni.reLaunch({
						url: '/pages/register/personal'
					})
				} else {
					uni.reLaunch({
						url: '/pages/login/login'
					})
				}
			}
		},
		methods: {
			initData() {
				this.getMedicalOrder()
				this.getInfoMsg()
				this.getCompelStatus()
				this.getPlanInfo()
				this.getPatientScheme()
				this.toadyformat = formatDate(new Date(), 'MM月dd日')
			},
			changeBoardDate(type) {
				type == 'left' ? this.currentDay-- : this.currentDay++
				this.boardDateParams = getDay(this.currentDay)
				this.boardDate = formatDate(this.boardDateParams, 'MM月dd日')
				// this.showPie= false
				this.getSleepDiary()
			},
			// 获取用户当前的方案计划详情
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					let {
						currentSchemeDayMap,
						groupSchemeProcess,
						currentSchemeProcess
					} = result
					let currentSchemeDay = [] //当前方案的日期
					let currentSchemeDayFinish = [] //当前方案的已完成的日期

					if (typeof currentSchemeDayMap !== 'undefined') {
						for (const [key, value] of Object.entries(currentSchemeDayMap)) {
							// console.log(key, value)
							currentSchemeDay.push(key.split(' ')[0])
							if (value) {
								currentSchemeDayFinish.push(key.split(' ')[0])
							}
						}
					}
					this.currentSchemeDay = currentSchemeDay
					console.log('currentSchemeDay', currentSchemeDay)
					this.currentSchemeDayFinish = currentSchemeDayFinish
					this.endDate = this.checkDate(getDay(0), currentSchemeDay)
					this.process = currentSchemeProcess
				})
			},
			checkDate(date, dates) {
				console.log('checkDate--p', date, dates)
				// 将传入的日期字符串转换为日期对象
				const targetDate = new Date(date);

				// 检查目标日期是否在数组中
				if (dates.includes(date)) {
					return date;
				} else {
					// 将数组中的日期字符串转换为日期对象
					const datesObj = dates.map(d => new Date(d));

					// 比较目标日期与数组中第一个和最后一个日期对象
					if (targetDate < datesObj[0]) {
						// 如果目标日期早于数组中第一个元素，返回数组中第一个元素的日期字符串
						return dates[0];
					} else if (targetDate > datesObj[datesObj.length - 1]) {
						// 如果目标日期晚于数组中最后一个元素，返回数组中最后一个元素的日期字符串
						return dates[dates.length - 1];
					} else {
						// 如果目标日期在数组中日期之间，返回目标日期
						return date;
					}
				}
			},
			getSleepDiary(status) {
				let params = {
					recordTime: this.boardDateParams
				}
				getSleepDiary(params).then(result => {
					this.createsleepDiary = (isObjEmpty(result) && getDay(0) == this.boardDateParams) ? true :
						false
					this.boardData = result ? result : {}
					//如果今日无睡眠日记，并且是强制模式
					if(this.createsleepDiary && (uni.getStorageSync("CompulsoryPlan") == "1")) {
						uni.reLaunch({
							url: '/pages/diaryCreate/diary?timeCreate=' + this.boardDateParams,
						})
					}
				})
			},
			//获取是否是强制模式1为强制
			getCompelStatus() {
				let that = this
				getCompelStatus().then(result => {
					if (result == 1) {
						uni.setStorageSync('CompulsoryPlan','1')
						//获取强制模式有未完成的方案
						that.getExecuteInfo()
					} else {
						//获取睡眠日记
						this.getSleepDiary()
						uni.removeStorageSync('CompulsoryPlan')
					}
				})
			},
			getExecuteInfo() {
				getExecuteDetail().then(result => {
					if (result.length > 0) {
						uni.reLaunch({
							url: '/pages/course/course'
						})
					} else {
						//获取睡眠日记
						this.getSleepDiary()
					}
				})
			},
			getInfoMsg() {
				getInfo().then(result => {
					let {
						articles,
						adviceSleep,
						paSleepDiary
					} = result
					this.reads = articles
					this.adviceSleep = adviceSleep
				})
			},
			finishMedicalOrder(id) {
				finishMedicalOrderTask({
					'id': id
				}).then(result => {
					this.getMedicalOrder()
				})
			},
			getMedicalOrder() { //医嘱任务
				getMedicalOrderTask({
					pageNo: 1,
					pageSize: 2,
					status: 0
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records.map(item => {
						let value = ''
						let valueAll = item.content
						if (item.content.length > 50) {
							value = item.content.slice(0, 50) + '...'
						}
						return {
							id: item.id,
							label: item.title,
							value: value,
							valueAll: item.content,
							showAll: value.length > 0 ? false : true,
							limitDate: item.limitDate,
							status: item.status
						}
					})
					this.medicalOrdemasks = list
					this.medicalOrdemasksTotal = total
				})
			},
			getPatientScheme() { //量表任务
				getPatientSchemeTask({
					pageNo: 1,
					pageSize: 2
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records
					this.patientSchemeTasks = list
					this.patientSchemeTasksTotal = total
				})
			},
			bindTimeChange: function(e) {
				this.time = e.detail.value
			},
			goReadInfo(id) {
				uni.navigateTo({
					url: '/pages/reads/detail?id=' + id
				})
			},
			goPage(type) {
				let pathHash = {
					'course': '/pages/course/course',
					'diaryCreate': '/pages/diaryCreate/diary?timeCreate=' + this.boardDateParams,
					'diary': '/pages/diary/diary?time=' + this.boardDateParams,
					'feeling': '/pages/feeling/feeling',
					'review': '/pages/review/review',
					'medicalOrde': '/pages/medicalOrde/medicalOrde',
					'patientScheme': '/pages/patientScheme/patientScheme',
					'addworried': '/pages/addworried/addworried',
					'weekly': '/pages/users/weekly/weekly'
				}
				uni.navigateTo({
					url: pathHash[type]
				})
			},
			handleMeasureClick(item) {
				uni.navigateTo({
					url: '/pages/beginMeasure/index?reportId=' + item.resultId + '&measureTaskId=' + item.id +
						'&status=' + item.status + '&type=2'
				})
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>