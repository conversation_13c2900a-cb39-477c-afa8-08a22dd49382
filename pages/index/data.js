// 提交:https://cbti.zhisongkeji.com/zhisong-cbti/api/patient/paSleepDiary/add
{
	"success": true,
	"message": "添加成功！",
	"code": 200,
	"result": {
		"id": "1833503800357203970",
		"recordTime": "2024-09-10",
		"patientId": "1637654645090254850",
		"patientName": "张扬",
		"nightPrepareSleepDate": "2024-09-09 22:00:00",
		"nightAsleepDate": "2024-09-09 22:00:00",
		"wakeDate": "2024-09-10 07:00:00",
		"getUpDate": "2024-09-10 07:00:00",
		"nightWakeTimes": 1,
		"nightWakeTotalTime": 6,
		"isNap": null,
		"mornFeelType": 4,
		"remark": "",
		"delFlag": null,
		"createBy": "18312345678",
		"createTime": "2024-09-10 21:52:25",
		"updateBy": null,
		"updateTime": null,
		"tenantId": null,
		"sysOrgCode": "A01A01",
		"doctorId": "1637058476681113601",
		"doctorName": "医院测试医生",
		"startDate": null,
		"endDate": null,
		"naps": null,
		"medicines": null,
		"sleepEfficiency": null,
		"sleepLength": null,
		"adviceSleep": null,
		"bedriddenLength": null,
		"napLength": null
	},
	"timestamp": 1725976345526
}

//小睡记录 https://cbti.zhisongkeji.com/zhisong-cbti/api/patient/paSleepDiaryTrain/addList
[{
	"startDate": "2024-09-09 19:10:00",
	"endDate": "2024-09-09 19:20:00",
	"recordDate": "2024-09-10",
	"diaryId": "1833503800357203970"
}]
//服药记录 https://cbti.zhisongkeji.com/zhisong-cbti/api/patient/paSleepDiaryMedicine/add
[{"recordDate":"2024-09-10","diaryId":"1833503800357203970"},{"recordDate":"2024-09-10","diaryId":"1833503800357203970"}]
//饮品记录https://cbti.zhisongkeji.com/zhisong-cbti/pa/paSleepDiaryDrink/add
//放松训练 https://cbti.zhisongkeji.com/zhisong-cbti/api/patient/paSleepDiaryTrain/addList
[{"startDate":"2024-09-09 11:00:00","endDate":"2024-09-09 15:00:00","totalNum":240,"recordDate":"2024-09-10","diaryId":"1833503800357203970"}]

//最困时间 https://cbti.zhisongkeji.com/zhisong-cbti/pa/paSleepDiaryTired/add