.index-pages {
	padding: 0 16px;

	.user {
		color: #ffffff;
		&-name {
			font-weight: bold;
			line-height: 20px;
			font-size: 20px;
		}
		&-value {
			font-weight: 400;
			font-size: 14px;
			line-height: 16px;
			padding-top: 12px;
		}
	}

	.plancard {
		margin: 20px 0;
		&-time {
			width: 100%;
			height: 124px;
			background: rgba(255, 255, 255, 0.8);
			box-shadow: 0px 4px 12px 0px rgba(145, 160, 255, 0.3);
			border-radius: 12px;
			backdrop-filter: blur(10px);
			padding: 0 4px;
			margin-bottom: 16px;
			&-label {
				width: 100%;
				height: 40px;
				background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
				border-radius: 12px 12px 0px 0px;
				padding: 0 8px;
				> image {
					width: 16px;
					height: 16px;
				}
				.label {
					font-weight: bold;
					font-size: 14px;
					color: #1c1c1e;
					padding-left: 4px;
					padding-right: 8px;
				}
				.nextlabel {
					font-weight: 400;
					font-size: 11px;
					color: #aeaeb2;
				}
			}
			&-content {
				width: 100%;
				height: 80px;
				background: linear-gradient(270deg, #fffcef 0%, #fafaff 100%);
				border-radius: 10px;

				.timeitem {
					flex: 1;
					align-items: center;
					&-label {
						font-weight: 400;
						font-size: 12px;
						color: #8e8e93;
						line-height: 12px;
						padding-bottom: 8px;
					}
					&-value {
						font-weight: bold;
						font-size: 28px;
						color: #48484a;
						line-height: 28px;
					}
				}
				.timeinterval {
					font-weight: 400;
					font-size: 12px;
					color: #918ff8;
					line-height: 12px;
					padding-bottom: 6px;
					border-bottom: 1px solid #918ff8;
				}
			}
		}
		&-tab {
			width: 100%;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: 1fr;
			grid-column-gap: 20px;
			&-item {
				height: 84px;
				background: rgba(255, 255, 255, 0.8);
				box-shadow: 0px 2px 6px 0px rgba(145, 160, 255, 0.2);
				border-radius: 12px;
				backdrop-filter: blur(10px);
				justify-content: center;
				> image {
					width: 40px;
					height: 40px;
					margin-right: 14px;
				}
				.content {
					&-label {
						font-weight: bold;
						font-size: 14px;
						color: #1c1c1e;
					}
					&-value {
						font-weight: 400;
						font-size: 12px;
						color: #999999;
					}
				}
			}
		}
		&-board {
			width: 100%;
			min-height: 448rpx;
			height: 448rpx;
			background: #f9f9fc;
			box-shadow: 0px 2px 6px 0px rgba(145, 160, 255, 0.2);
			border-radius: 12px;
			backdrop-filter: blur(10px);
			padding: 0 4px;
			overflow: hidden;
			&-date {
				width: 100%;
				height: 80rpx;
				min-height: 80rpx;
				background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
				font-weight: bold;
				font-size: 14px;
				color: #1c1c1e;
				line-height: 16px;
				justify-content: space-between;
				padding: 0 12px;
				.changeicon {
					> image {
						width: 16px;
						height: 16px;
					}
				}
			}
			&-content {
				flex: 1;
				background: #ffffff;
				border-radius: 10px;
				padding: 12px;

				.empty {
					height: 100%;
					align-items: center;
					&-icon {
						width: 96rpx;
						height: 96rpx;
						margin: 64rpx 0 16rpx;
					}
					&-text {
						font-weight: 400;
						font-size: 24rpx;
						color: #b3b3d8;
						margin-bottom: 36rpx;
					}
					&-btn {
						width: 264rpx;
						height: 64rpx;
						min-height: 64rpx;
						background: #ffffff;
						border-radius: 32rpx;
						border: 2rpx solid #d1d1d6;
						> image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 5rpx;
						}
						font-weight: 400;
						font-size: 24rpx;
						color: #636366;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

				.bcontent {
					flex: 1;
					height: 100%;
					justify-content: space-between;
					.btime {
						width: 100%;
						&-icon {
							width: 24px;
							height: 24px;
							margin-right: 8px;
						}
						&-item {
							flex: 1;
							&-label {
								font-weight: 400;
								font-size: 10px;
								color: #999999;
								line-height: 10px;
								padding-bottom: 2px;
							}
							&-value {
								font-weight: 400;
								font-size: 13px;
								color: #3a3a3c;
								line-height: 16px;
							}
						}
						.line {
							width: 1px;
							height: 26px;
							background-color: #e5e5ea;
							margin: 0 12px;
						}
					}

					.bbtn {
						.diary {
							margin-right: 12px;
						}
					}
				}
				.chart {
					width: 108px;
					height: 100%;
				}
			}
		}
	}

	.planCalendar {
		margin: 12px 0;

		&-content {
			flex: 1;
		}
	}

	.task {
		&-tab {
			padding: 30rpx 0;
			font-weight: 400;
			font-size: 28rpx;
			color: #1c1c1e;

			&-item {
				margin-right: 40rpx;
			}
			.currenttab {
				font-weight: bold;
				font-size: 32rpx;
				color: #5e5ce6;
				position: relative;
			}
			.currenttab::after {
				content: '';
				position: absolute;
				width: 40rpx;
				height: 6rpx;
				background: #5e5ce6;
				border-radius: 4rpx;
				bottom: -8px;
				left: calc(50% - 20rpx);
			}
		}
		&-content {
			.taskitem {
				padding: 24rpx;
				background: rgba(235, 235, 245, 0.3);
				border-radius: 24rpx;
				margin-bottom: 16rpx;
				&-time {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					margin-bottom: 16rpx;
					.tlabel {
						flex: 1;
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						.iconImg {
							width: 25rpx;
						}
					}
					.tstatus {
						width: 107rpx;
						height: 28rpx;
					}
				}
				.unfinish {
					.tlabel {
						color: #ff9500;
					}
				}
				.finish {
					.tlabel {
						color: #00c826;
					}
				}
				&-label {
					font-weight: bold;
					font-size: 28rpx;
					color: #1c1c1e;
					margin-bottom: 8rpx;
					display: flex;
					align-items: center;
				}
				&-value {
					font-size: 24rpx;
					color: #636366;
					line-height: 32rpx;
					margin-bottom: 16rpx;
				}
				&-btn {
					justify-content: flex-end;
					.finish {
						color: #ffffff;
						background: #5e5ce6;
						margin-left: 24rpx;
					}
				}
			}
			.tasknext {
				justify-content: center;
				margin: 16rpx 0;
				font-weight: 400;
				font-size: 28rpx;
				color: #8e8e93;
				&-icon {
					width: 24rpx;
					height: 24rpx;
					margin-left: 8rpx;
				}
			}

			.ptaskitem {
				padding: 24rpx;
				background: rgba(235, 235, 245, 0.3);
				border-radius: 24rpx;
				margin-bottom: 16rpx;
				.pcontent {
					flex: 1;
					.ptaskitem-time {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						margin-bottom: 16rpx;
						.tlabel {
							flex: 1;
							display: flex;
							align-items: center;
							font-weight: 400;
							font-size: 24rpx;
							.iconImg {
								width: 25rpx;
							}
						}
						.tstatus {
							width: 107rpx;
							height: 28rpx;
						}
					}
					.unfinish {
						.tlabel {
							color: #ff9500;
						}
					}
					.finish {
						.tlabel {
							color: #00c826;
						}
					}
					.ptaskitem-label {
						font-weight: bold;
						font-size: 28rpx;
						color: #1c1c1e;
						margin-bottom: 8rpx;
						display: flex;
						align-items: center;
						&-icon {
							width: 32rpx;
							height: 32rpx;
							margin-right: 8rpx;
						}
					}
				}
			}
		}
	}

	.recordMood {
		&-content {
			.worried {
				background: #fff5f7;
			}
			.diary {
				background: #fff8eb;
			}
		}
	}
	.read {
		&-content {
			.readitem {
				padding: 26rpx;
				border-bottom: 1rpx solid #e5e5ea;
				&-cover {
					width: 180rpx;
					height: 120rpx;
					overflow: hidden;
					margin-right: 24rpx;
					border-radius: 24rpx;
					> image {
						width: 100%;
						object-fit: cover;
					}
				}
				&-content {
					flex: 1;
					height: 120rpx;
					justify-content: space-between;
					.label {
						font-weight: 400;
						font-size: 28rpx;
						color: #1c1c1e;
					}
					.author {
						font-weight: 400;
						font-size: 24rpx;
						color: #8e8e93;
					}
				}
			}
		}
	}
}
