<template>
	<view class="review-pages">
<!-- 		<view class="review-audio" v-if="mute">
			自动发音
			<image class="review-audio-icon" src="https://cbti.zhisongkeji.com/uniapp-static/mute.png" mode=""></image>
		</view>
		<view class="review-audio mute" v-else>
			自动发音
			<image class="review-audio-icon" src="https://cbti.zhisongkeji.com/uniapp-static/nomute.png" mode=""></image>
		</view> -->
		<view class="problems">
			<view class="problem-item">
				1. 我需要睡足8小时，白天才能够精力充
			</view>
		</view>
		<view class="answer">
			<view :class="'answer-item ' + (answer == item ? 'selected' : '')" :key="index" v-for="(item, index) of answers">
				{{ item }}
			</view>
		</view>
		<view class="review-btn">
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dback.png" mode="" @click="back()"></image>
			<label>{{ currentPage }}/7</label>
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dnext.png" v-if="currentPage < 7" mode="" @click="next()">
			</image>
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dfinish.png" v-else mode="" @click="next()"></image>
		</view>
	</view>
</template>

<script>
export default {
	data () {
		return {
			mute: true,
			currentPage: 1,
			answers: ['非常不同意', '不同意', '一般', '同意', '非常同意'],
			answer: '非常不同意'
		}
	},
	methods: {

	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>