.review-pages {
	padding: 0 32rpx;
	display: flex;
	flex-direction: column;
	
	.review-audio {
		width: 100%;
		font-size: 24rpx;
		color: #5e5ce6;
		padding: 32rpx 0;
		display: flex;
		justify-content: flex-end;
		align-items: center;

		&-icon {
			width: 45rpx;
			height: 45rpx;
			margin-left: 12rpx;
		}
	}
	.mute {
		font-size: 24rpx;
		color: #8e8e93;
	}

	.problems {
		min-height: 500rpx;
		font-weight: bold;
		font-size: 32rpx;
		color: #1c1c1e;
	}
	.answer{

		&-item{
			width: 100%;
			min-height: 80rpx;
			background: rgba(235,235,245,0.3);
			border-radius: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #2C2C2E;
			margin-bottom: 32rpx;
		}
		.selected{
			background: #5E5CE6;
			color: #FFFFFF;
		}
	}
	.review-btn {
		position: fixed;
		bottom: 0;
		padding-bottom: 64rpx;
		width: 100%;
		height: 176rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fff;
		> image {
			width: 112rpx;
			height: 112rpx;
		}
		> label {
			padding: 0 48rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #636366;
		}
	}
}
