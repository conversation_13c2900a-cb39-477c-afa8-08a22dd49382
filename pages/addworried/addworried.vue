<template>
	<view class="addworried-page">
		<view class="feel-tip-item flex-col">
			<view class="topbar flex-row">
				描述你的忧虑：
			</view>
			<view class="desc">
				<textarea @input="((e)=>{input(e,'content')})" :value="dataForm.content" placeholder="请写下你烦恼的事情" placeholder-class="place" />
			</view>
		</view>
		<view class="feel flex-col">
			<view class="feel-label">
				1. 你是否认为这件事是难题？?
			</view>
			<view class="feel-content w-full flex-row">
				<view class="fitem flex-col" :key="index" v-for="(item,index) of yesno" @click="changeYesNo('isProblem',item.key)">
					<img class="fitem-icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${dataForm.isProblem == item.key ? item.ingicon :item.icon}`" alt="" />
					{{item.label}}
				</view>
			</view>
		</view>
		<view class="feel flex-col">
			<view class="feel-label">
				2. 是否已有详细计划解决忧虑？？
			</view>
			<view class="feel-content w-full flex-row">
				<view class="fitem flex-col" :key="index" v-for="(item,index) of yesno" @click="changeYesNo('isPlan',item.key)">
					<img class="fitem-icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${dataForm.isPlan == item.key ? item.ingicon :item.icon}`" alt="" />
					{{item.label}}
				</view>
			</view>
		</view>
		<view class="feel-tip-item flex-col">
			<view class="desc">
				<textarea  @input="((e)=>{input(e,'detailPlan')})" :value="dataForm.detailPlan" placeholder="请输入解决忧虑的计划" placeholder-class="place" />
			</view>
		</view>
<!-- 		<view class="awitem switch flex-row">
			<view class="switch-label">
				设置闹钟提醒自己执行计划
			</view>
			<view class="switch-value">
				<switch color="#5E5CE6" :checked="checked" @click="checked = !checked"/>
			</view>
		</view>
		<view class="awitem addate flex-row" v-if="checked">
			<view class="awitem-label">
				提醒时间
			</view>
			<view class="awitem-value flex-row">
				<picker class="awitem-value-picker flex-row"  style="margin-right: 10rpx;" @change="changeDate" mode="date">
					<view class="picker">
						{{date ? date :'请选择日期'}}
					</view>
				</picker>
				<picker class="awitem-value-picker flex-row" @change="changeTime" mode="time">
					<view class="picker">
						{{time ? time :'请选择时间'}}
					</view>
				</picker>
			</view>
		</view>
		<view class="text" v-if="checked">
			那么到了设定的时间再去想吧，现在我们可以清空内心，忘记这些
		</view> -->
		<view class="finishbtn" @click="submit()">
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dfinish.png" mode=""></image>
		</view>
	</view>
</template>

<script>
	import {
		addworried,
		getworried,
		editworried
	} from '@/assets/api/index.js'
	import {
		formatDate,
		mergeAndIntersectObjects
	} from '../../utils/index.js'
	export default {
		data() {
			return {
				yesno: [{
					label: '有',
					icon: 'yes.png',
					ingicon: 'yesing.png',
					key:'1'
				}, {
					label: '无',
					icon: 'no.png',
					ingicon: 'noing.png',
					key:'0'
				}],
				checked: false,
				date: null,
				time: null,
				dataForm:{
					content: '',
					isPlan:'0',
					isProblem:'0',
					detailPlan: '',
					// reminderTime: ''
				},
				rule:{
					content:'请描述你的忧虑',
					isProblem:'你是否认为这件事是难题？',
					isPlan: '是否已有详细计划解决忧虑？',
					detailPlan: '请输入解决忧虑的计划',
				},
				id:'',
				detail:{}
			}
		},
		onLoad({id}) {
			if(id){
				uni.setNavigationBarTitle({
					title:'编辑忧虑'
				})
				this.id = id
				this.getDetail(id)
			}
		},
		methods: {
			getDetail(id){
				getworried({'id':id}).then(result =>{
					this.detail = result
					this.dataForm = mergeAndIntersectObjects(this.dataForm,result)
					this.checked = result.reminderTime != null ? true :false
					if(!this.checked) return
					this.date = formatDate(result.reminderTime,'YYYY-MM-dd')
					this.time = formatDate(result.reminderTime,'hh:mm')
				})
			},
			changeDate(e) {
				this.date = e.detail.value
			},
			changeTime(e){
				this.time = e.detail.value
			},
			changeYesNo(type,key){
				this.dataForm[type] = key
			},
			input(e,type){				
				this.dataForm[type] = e.detail.value
			},
			submit(){
				let dataForm = Object.assign({},this.dataForm)
				for(let item in this.rule){
					if(dataForm[item] === ''){
						wx.showToast({
							title: this.rule[item],
							duration: 2000,
							icon:'none'
						});
						return
					}
				}
				if(this.checked){
					if(!this.date || !this.time){
						wx.showToast({
							title: '请设置提醒时间',
							duration: 2000,
							icon:'none'
						});
						return
					}
					dataForm.reminderTime = this.date + ' ' +this.time +':00'
				}else{
					dataForm.reminderTime = ''
					this.date = ''
					this.time = ''
				}
			
				if(this.id){
					dataForm.id = this.id
					editworried(dataForm).then(result =>{
						wx.showToast({
							title: '提交成功',
							duration: 2000,
							icon:'none'
						});
						setTimeout(()=>{
							uni.navigateBack()
						},2000)
					})
				}else{
					addworried(dataForm).then(result =>{
						wx.showToast({
							title: '提交成功',
							duration: 2000,
							icon:'none'
						});
						setTimeout(()=>{
							uni.navigateBack()
						},2000)
					})
				}
		
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>