.addworried-page {
  width: 100%;
  height: 100%;
  padding: 0 32rpx;
  .feel-tip-item {
    width: 100%;
    .topbar {
      margin: 48rpx 0 24rpx;
      &-status {
        font-weight: 400;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 48rpx;
        height: 48rpx;
        background: #5e5ce6;
        border-radius: 12rpx;
        padding: 0 16rpx;
        margin-right: 16rpx;
      }
      font-weight: bold;
      font-size: 32rpx;
      color: #1c1c1e;
    }
    .desc {
      width: 100%;
      height: 276rpx;
      background: rgba(235, 235, 245, 0.3);
      border-radius: 24rpx;
      padding: 16rpx 24rpx;
      .place {
        font-weight: 400;
        font-size: 28rpx;
        color: #aeaeb2;
        line-height: 48rpx;
      }
    }
  }

  .feel {
    align-items: center;
    padding-bottom: 28rpx;
    &-label {
	  width: 100%;
      font-weight: bold;
      font-size: 32rpx;
      color: #1c1c1e;
      margin: 48rpx auto;
    }

    &-content {
      flex: 1;
      align-items: center;
      justify-content: space-between;
      .fitem {
        flex: 1;
        align-items: center;
        justify-content: center;
        &-icon {
          width: 64rpx;
          height: 64rpx;
          margin-bottom: 16rpx;
        }
        font-weight: 400;
        font-size: 20rpx;
        color: #2c2c2e;
      }
    }
  }

  .awitem {
    width: 100%;
    height: 104rpx;
    align-items: center;
    justify-content: space-between;
    border-top: 1rpx solid #e5e5ea;
    border-bottom: 1rpx solid #e5e5ea;

    &-value {
      flex: 1;
      height: 104rpx;
      justify-content: flex-end;
      &-picker {
        height: 104rpx;
        align-items: center;
        color: #8e8e93;
      }
    }
  }
  .switch {
    margin-top: 40rpx;
  }
  .addate {
    border-top: none;
  }
  .text {
    padding-top: 24rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #5e5ce6;
    line-height: 40rpx;
  }

  .finishbtn {
    padding: 24rpx 0 64rpx;
    width: 100%;
    height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    > image {
      width: 112rpx;
      height: 112rpx;
    }
  }
}
