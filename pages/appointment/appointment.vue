<template>
	<view class="appointment-pages">
		<view class="appointment-date">
			<view @click="changeDate(index, item.format)"
						:class="'appointment-date-item flex-col ' + (currentDate == index ? 'currentDate' : '')" :key="index"
						v-for="(item, index) of dates">
				<view class="date">
					{{ item.date }}
				</view>
				<view class="label">
					{{ item.label }}
				</view>
				<view class="isappointment'">
					有号
				</view>
				<!-- <view :class="item.isappointment ? 'isappointment' :'noappointment'">
					{{item.isappointment ? '有号' :'无号'}}
				</view> -->
			</view>
		</view>
		<view class="appointment-time">
			<view :class="'appointment-time-item ' + (currentTime.indexOf(item) > -1 ? 'currentTime' : '')" :key="index"
						v-for="(item, index) of times" @click="addappointment(item)">
				{{ item }}
			</view>
		</view>
	</view>
</template>

<script>
import {
	formatDate,
} from '../../utils/index.js'

import {
	getappointment,
	addappointment,
	getUser
} from '@/assets/api/index.js'

import {
	useStore
} from 'vuex'
export default {
	data () {
		const store = useStore();
		return {
			visitDate: '',
			user: store.state.user,
			currentDate: 0,
			dates: [],
			isappointment: '',
			currentTime: [],
			listToConfirmed:[],
			times: ['08:00', '08:15', '08:30', '08:45', '09:00', '09:15', '09:30', '09:45', '10:00', '10:15', '10:30',
				'10:45', '11:00', '11:15', '11:30', '14:00', '14:15', '14:30', '14:45', '15:00', '15:15', '15:30',
				'15:45',
				'16:00', '16:15', '16:30'
			],
		}
	},
	onLoad () {
		this.initData()
	},

	methods: {
		initData () {
			this.dates = this.getLastFiveDaysDates()
			this.getcurrent()
			this.getUser()
		},
		getUser () {
			getUser().then(result => {
				uni.setStorageSync('user', result)
				this.$store.commit('setUser', result)
			})
		},
		addappointment (time) {
			if (this.currentTime.indexOf(time) > -1) return
			if (this.listToConfirmed.indexOf(time) > -1) {
				uni.showToast({
					title: '已预约等待医生确认中!',
					duration: 2000,
					icon: 'none'
				})
				return
			}
			let that = this
			uni.showModal({
				title: '提示',
				content: '确认预约',
				success: function (res) {
					if (res.confirm) {
						that.listToConfirmed.push(time)
						let {
							doctorId,
							doctorName
						} = that.user
						let params = {
							doctorId: doctorId,
							doctorName: doctorName,
							visitDate: that.visitDate,
							visitTime: time, //就诊日期时间,
							patientNote: ''
						}
						addappointment(params).then(result => {
							uni.showToast({
								title: '预约成功!',
								duration: 2000,
								icon: 'none'
							})
					
						})
								
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		
		},
		getcurrent () {
			getappointment({
				pageNo: 1,
				pageSize: 8,
				visitDate: this.visitDate,
			}).then(result => {
				//待确认
				let listToConfirmed = []
				//已确认
				let list = []

				for (let i = 0; i < result.length; i++) {
					let item = result[i]
					if(item.status === '2'){
						list.push(item.visitTime)
					}else if(item.status === '1'){
						listToConfirmed.push(item.visitTime)
					}
				}
				this.currentTime = list
				this.listToConfirmed = listToConfirmed

			})
		},
		getLastFiveDaysDates () {
			let dates = [];
			for (let i = 0; i <= 4; i++) {
				let date = new Date();
				date.setDate(date.getDate() + i);

				const dayOfWeekNumber = date.getDay(); // 获取周几的数字表示（0-6）
				const dayArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				const dayOfWeek = dayArr[dayOfWeekNumber]
				dates.push({
					date: formatDate(date, 'dd日'),
					label: dayOfWeek,
					format: formatDate(date, 'YYYY-MM-dd')
				});
			}
			this.visitDate = dates[0].format
			return dates;
		},
		changeDate (index, format) {
			this.currentDate = index
			this.visitDate = format
			this.getcurrent()
		}
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>