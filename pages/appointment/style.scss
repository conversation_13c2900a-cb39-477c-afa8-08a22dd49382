.appointment-pages {
  background-color: #f9f9fc;
  padding: 0 32rpx;
  .appointment-date {
    height: 224rpx;
    padding: 32rpx 0;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 24rpx;
    &-item {
      height: 160rpx;
      background: #ffffff;
      border-radius: 16rpx;
      justify-content: center;
      align-items: center;
      color: #2c2c2e;
      .date {
        font-weight: bold;
        font-size: 32rpx;
      }
      .label {
        font-weight: 400;
        font-size: 24rpx;
        padding: 8rpx 0 16rpx;
      }
      .isappointment {
        font-weight: 400;
        font-size: 24rpx;
      }
      .noappointment {
        font-weight: 400;
        font-size: 24rpx;
        color: rgba(44, 44, 46, 0.2);
      }
    }
    .currentDate {
      background: #9694ff;
      color: #ffffff;
      position: relative;
      .isappointment {
        color: rgba(255, 255, 255, 0.7);
      }
      .noappointment {
        color: rgba(255, 255, 255, 0.7);
      }
    }
    .currentDate::after {
      content: "";
      display: block;
      position: absolute;
      left: calc(50% - 18rpx);
      bottom: -36rpx;
      border-left: 18rpx solid transparent;
      border-right: 18rpx solid transparent;
      border-bottom: 14rpx solid #fff;
    }
  }

  .appointment-time {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 24rpx;
    grid-row-gap: 24rpx;
    &-item {
      height: 70rpx;
      background: rgba(235, 235, 245, 0.3);
      border-radius: 16rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #2c2c2e;
      line-height: 70rpx;
      text-align: center;
    }
    .currentTime {
      background: #9694ff;
      color: #ffffff;
    }
  }
}
