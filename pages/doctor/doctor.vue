<template>
	<view class="doctor-pages page-deal" :style="{ paddingTop: navBarHeight + 'px' }">
		<view class="user w-full flex-col">
			<view class="user-name">
				我的专属医生
			</view>
		</view>
		<view class="doctorcard">

	
			<view class="doctorcard-content flex-col">
				<view class="name flex-row">
					{{ doctor.name }}
					<!-- <img class="next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" alt="" /> -->
				</view>
				<view class="info flex-row">
					<view class="info-position" v-if="doctor.title">
						{{ doctor.title }}
					</view>
					<view class="info-item">
						{{ doctor.section }}
					</view>
				</view>
				<view class="intro">
					{{ doctor.skill }}
				</view>
			</view>
			<image class="doctorcard-avatar" v-if="doctor.avatar"
						 :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${doctor.avatar}`" mode="widthFix">
			</image>
			<image v-else class="doctorcard-avatar" src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png" mode="">
			</image>
		</view>
		<view class="chat flex-col">
			<title label="留言问答" next="聊天历史" path="/pages/history/history" />
			<view class="chat-content">
				<view class="dialogue">
					<items :list="list" :doctor="doctor"/>
				</view>
				<view class="message">
					<!-- <view class="message-box">
						点击留言按钮输入留言…
					</view> -->
					<view class="message-box message-send" @click="goPage('chat')">
						留言
					</view>
				</view>
			</view>
		</view>
		<view class="appointmentCenter flex-row" @click="goPage('appointment')">
			<image class="appointmentCenter-icon" src="https://cbti.zhisongkeji.com/uniapp-static/icon9.png" mode="widthFix">
			</image>
			<view class="appointmentCenter-content flex-col">
				<view class="label">
					预约中心
				</view>
				<view class="value">
					在线预约高效便捷
				</view>
			</view>
			<image class="appointmentCenter-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="widthFix">
			</image>
		</view>
	</view>
</template>

<script>
import title from '@/components/title.vue'
import items from '@/components/chatitem/index.vue'
import {
	getleavemessage,
	getDoctor
} from '@/assets/api/index.js'
import {
	useStore
} from 'vuex'
export default {
	components: {
		title,
		items
	},
	data () {
		const store = useStore();
		return {
			navBarHeight: store.state.navBarHeight || 87,
			doctor: {
				name: '王争勇',
				position: '副主任医师',
				department: '神经内科',
				detailDepartment: '睡眠医学科',
				intro: '副主任医师，专长睡眠医学，12年丰富经验。精通失眠、睡眠呼吸暂停等诊治，患者好评如潮，学术造诣深厚，热心教学与科研。'
			},
			list: []
		}
	},
	onLoad () {
		this.initData()
	},
	onShow () {
		this.getLeavemessage()
	},
	methods: {
		initData () {
			this.getDoctor()
		},
		getLeavemessage () {
			getleavemessage({
				pageNo: 1,
				pageSize: 4,
			}).then(result => {
				//type1患者 2医生
				let {
					records,
					total
				} = result
				if (total == 0) {
					return
				}
				let list = records.map(item => {
					return {
						role: item.type == '1' ? 'query' : 'answer',
						message: item.content
					}
				})
				this.list = list.reverse();
			})
		},
		getDoctor () {
			getDoctor({
				pageNo: 1,
				pageSize: 1
			}).then(result => {
				this.doctor = result[0]
			})
		},
		goPage (type) {
			let pathHash = {
				'chat': '/pages/chat/chat',
				'appointment': '/pages/appointment/appointment'
			}
			uni.navigateTo({
				url: pathHash[type]
			})
		}
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>