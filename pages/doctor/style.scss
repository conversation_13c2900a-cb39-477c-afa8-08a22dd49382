.doctor-pages {
	padding: 0 16px;
	background: linear-gradient(180deg, #4e68ff 0%, rgba(219, 240, 255, 0) 40%);
	.user {
		color: #ffffff;
		&-name {
			font-weight: bold;
			line-height: 20px;
			font-size: 20px;
		}
		&-value {
			font-weight: 400;
			font-size: 14px;
			line-height: 16px;
			padding-top: 12px;
		}
	}

	.doctorcard {
		margin: 32rpx 0 16rpx;
		width: 100%;
		height: 272rpx;
		background: rgba(255, 255, 255, 0.8);
		box-shadow: 0px 4 12rpx 0rpx rgba(145, 160, 255, 0.2);
		border-radius: 24rpx;
		backdrop-filter: blur(10px);
		padding: 22rpx 32rpx;
		display: flex;
		align-items: center;
		&-content {
			flex: 1;
			.name {
				font-weight: bold;
				font-size: 40rpx;
				color: #1c1c1e;
				align-items: center;
				.next {
					width: 24rpx;
					height: 24rpx;
					margin-left: 8rpx;
				}
			}

			.info {
				margin: 16rpx 0 22rpx;
				&-position {
					height: 32rpx;
					background: #7775ec;
					border-radius: 8rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #ffffff;
					line-height: 32rpx;
					padding: 0 12rpx;
					margin-right: 24rpx;
				}
				&-item {
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
					margin-right: 10rpx;
				}
			}

			.intro {
				max-width: 480rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #636366;
				line-height: 36rpx;
			}
		}
		&-avatar {
			width: 189rpx;
			height: 189rpx;
			object-fit: contain;
			margin-left: 5rpx;
		}
	}

	.chat {
		margin: 12px 0;

		&-content {
			flex: 1;

			.dialogue {
				width: 100%;
				height: 568rpx;
				padding: 24rpx;
				background: rgba(235, 235, 245, 0.3);
				border-radius: 24rpx 24rpx 0px 0px;
				flex: 1;
				overflow-x: hidden;
				overflow-y: scroll;
				position: relative;
				overscroll-behavior: none;
				display: flex;
				flex-direction: column;
			}

			.message {
				width: 100%;
				height: 128rpx;
				background: rgba(235, 235, 245, 0.6);
				border-radius: 0px 0px 24rpx 24rpx;
				display: flex;
				padding: 24rpx 32rpx;

				&-box {
					flex: 1;
					height: 80rpx;
					background: #ffffff;
					border-radius: 40rpx;
					// border: 2rpx solid #d1d1d6;
					// font-weight: 400;
					// font-size: 28rpx;
					// color: #c7c7cc;
					// line-height: 80rpx;
					// padding-left: 32rpx;
				}
				&-send {
					// margin-left: 16rpx;
					// width: 120rpx;
					height: 80rpx;
					background: #5e5ce6;
					border-radius: 40rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #ffffff;
					line-height: 80rpx;
					text-align: center;
				}
			}
		}
	}

	.appointmentCenter {
		width: 100%;
		height: 128rpx;
		background: rgba(235, 235, 245, 0.3);
		border-radius: 24rpx;
		padding: 24rpx;
		&-icon {
			width: 80rpx;
			margin-right: 24rpx;
		}
		&-content {
			flex: 1;
			height: 80rpx;
			justify-content: space-between;
			.label {
				font-weight: bold;
				font-size: 28rpx;
				color: #1c1c1e;
			}
			.value {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}
		}
		&-next {
			width: 24rpx;
		}
	}
}
