<template>
	<view class="chat-page">
		<z-paging ref="paging" v-model="list" use-chat-record-mode @query="queryList" :default-page-size="20">
			<!-- for循环渲染聊天记录列表 -->
			<view v-for="(item, index) of list" :key="index" class="item" style="position: relative;">
				<view style="transform: scaleY(-1);">
					<view class="item-query" v-if="item.role == 'query'">
						
						<image  class="item-query-avatar" v-if="user.avatarUrl"  :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${user.avatarUrl}`"  mode="widthFix"></image>
						<image class="item-query-avatar" v-else src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png"
							mode=""></image>
						<view class="item-query-content">
							<view class="query">
								{{ item.message }}
							</view>
						</view>
					</view>
					<view class="item-answer" v-if="item.role == 'answer'">
						<image  class="item-answer-avatar" v-if="doctor.avatar"  :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${doctor.avatar}`"  mode="widthFix"></image>
						<image class="item-answer-avatar" v-else src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png"
							mode=""></image>
						<view class="item-answer-content">
							<view class="answer">
								{{ item.message }}
							</view>
						</view>
					</view>
				</view>
				<!-- </view> -->
			</view>
			<!-- 底部聊天输入框 -->
			<template #bottom>
				<view class="message" ref="inputBar">
					<view class="message-box">
						<input class="input" @input="input" :value="message" confirm-type="send" placeholder="请输入留言…"
						 :adjust-position="false"  cursor-spacing="20" :cursor="message.length"
							:show-confirm-bar="false" />
					</view>
					<view class="message-send" @click="send()">
						留言
					</view>
				</view>
			</template>
		</z-paging>
	</view>

</template>

<script>
	import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'
	import {
		getleavemessage,
		addleavemessage,
		getDoctor
	} from '@/assets/api/index.js'
	import items from '@/components/chatitem/index.vue'
	import {
		useStore
	} from 'vuex'
	export default {
		mixins: [ZPMixin],
		components: {
			items
		},
		data() {
			const store = useStore();
			return {
				user: store.state.user,
				list: [],
				message: '',
				pageNo: 1,
				scrollTop: 0,
				dialogueHeight: 0,
				containerHeight: 0,
				lastContainerHeight: 0,
				dataList: [],
				doctor: []
			}
		},
		onLoad() {
			// this.initDialogueHeight()
			// this.getHistory()
			this.getDoctor()
		},
		methods: {
			// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用this.$refs.paging.reload()即可
			queryList(pageNo, pageSize) {
				// 此处请求仅为演示，请替换为自己项目中的请求
				getleavemessage({
					pageNo: pageNo,
					pageSize: pageSize,
				}).then(result => {
					//type1患者 2医生
					let {
						records,
						total
					} = result
					if (total === 0 || records.length === 0) {
						this.$refs.paging.complete([]);
						return
					}
					let list = records.map(item => {
						return {
							role: item.type == '1' ? 'query' : 'answer',
							message: item.content,
							id: item.id
						}
					})
					this.$refs.paging.complete(list);

				}).catch(res => {
					// 	// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
					// 	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
					// 	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
					this.$refs.paging.complete(false);
				})
			},
			getDoctor() {
				getDoctor({
					pageNo: 1,
					pageSize: 1
				}).then(result => {
					this.doctor = result[0]
				})
			},
			send() {
				if (!this.message) return
				let msg = this.message
				this.message = ''
				addleavemessage({
					'content': msg
				}).then(result => {
					setTimeout(() => {
						this.$refs.paging.addChatRecordData({
							role: 'query',
							message: msg,
						});
					}, 500)



					wx.showToast({
						title: '留言成功',
						duration: 2000,
						icon: 'none'
					});
				})
			},
			input(e) {
				this.message = e.detail.value
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>