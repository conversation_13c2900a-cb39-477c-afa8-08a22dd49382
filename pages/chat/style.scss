.chat-page {
	width: 100%;
	height: 100vh;
	background: rgba(255, 255, 255, 0.95);
	display: flex;
	flex-direction: column;

		.item {
			// width: calc(100% - 60rpx);
			display: flex;
			flex-direction: column;
			padding: 0 30rpx;
			
			&-query {
				display: flex;
				flex-direction: row-reverse;
				justify-content: end;
				margin-bottom: 32rpx;
				&-avatar {
					width: 64rpx;
					min-width: 64rpx;
					height: 58rpx;
					border-radius: 50%;
					margin-left: 16rpx;
				}
				&-content {
					.query {
						color: #ffffff;
						background: #9694ff;
						border-radius: 24rpx  0px 24rpx 24rpx;
						padding: 16rpx 24rpx;
						border: 2rpx solid #9694ff;
					}
				}
			}
		
			&-answer {
				display: flex;
				margin-bottom: 32rpx;
				&-avatar {
					width: 64rpx;
					min-width: 64rpx;
					height: 58rpx;
					border-radius: 50%;
					margin-right: 16rpx;
				}
				&-content {
					
					.answer {
						padding: 16rpx 24rpx;
						color: #181e26;
						background: #fff;
						border-radius: 24rpx 24rpx  24rpx 0px;
						border: 2rpx solid #e5e5ea;
					}
				}
			}
		}
		
	// }

	.message {
		width: 100%;
		height: 128rpx;
		background: rgba(235, 235, 245, 0.6);
		border-radius: 0px 0px 24rpx 24rpx;
		display: flex;
		padding: 24rpx 32rpx;

		&-box {
			flex: 1;
			height: 80rpx;
			background: #ffffff;
			border-radius: 40rpx;
			border: 2rpx solid #d1d1d6;
			font-weight: 400;
			font-size: 28rpx;
			color: #c7c7cc;
			line-height: 80rpx;
			padding: 0 32rpx;
			// background: #5e5ce6;
			.input{
				width: 100%;
				height: 100%;
				font-size: 28rpx;
				color: #1C1C1E;
				display: flex;
				align-items: center;
			}
		
		}
		&-send {
			margin-left: 16rpx;
			width: 120rpx;
			height: 80rpx;
			background: #5e5ce6;
			border-radius: 40rpx;
			font-weight: bold;
			font-size: 28rpx;
			color: #ffffff;
			line-height: 80rpx;
			text-align: center;
		}
	}
}
