.feeling-pages {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
  .feel {
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 38rpx;
    &-label {
      font-weight: bold;
      font-size: 32rpx;
      color: #1c1c1e;
      margin: 48rpx auto;
    }

    &-content {
      align-items: center;
      justify-content: space-between;
      .fitem {
        flex: 1;
        align-items: center;
        justify-content: center;
        &-icon {
          width: 64rpx;
          height: 64rpx;
          margin-bottom: 16rpx;
        }
        font-weight: 400;
        font-size: 20rpx;
        color: #2c2c2e;
      }
      .current {
        position: relative;
      }
      .current:after {
        content: "";
        display: block;
        position: absolute;
        bottom: -27rpx;
        left: 42rpx;
        border-left: 24rpx solid transparent;
        border-right: 24rpx solid transparent;
        border-bottom: 20rpx solid rgba(235, 235, 245, 0.3);
      }
    }
    &-tab {
      width: 100%;
      background: rgba(235, 235, 245, 0.3);
      border-radius: 24rpx;
      padding: 24rpx 24rpx 0;
      // display: grid;
      // grid-template-columns: repeat(5, 1fr);
      // grid-column-gap: 24rpx;
      // grid-row-gap: 24rpx;
      display: flex;
      flex-wrap: wrap;
      margin-top: 28rpx;
      &-item {
        // width: 100%;
        height: 64rpx;
        padding: 0 24rpx;
        line-height: 64rpx;
        background: #ffffff;
        border-radius: 12rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #2c2c2e;
        margin-right: 24rpx;
        margin-bottom: 24rpx;
      }
      .selected {
        color: #ffffff;
        background-color: #5e5ce6;
      }
    }
    &-tip {
      width: 100%;
      &-item {
        .topbar {
          margin: 48rpx 0 24rpx;
          &-status {
            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 48rpx;
            height: 48rpx;
            background: #5e5ce6;
            border-radius: 12rpx;
            padding: 0 16rpx;
            margin-right: 16rpx;
          }
          font-weight: bold;
          font-size: 32rpx;
          color: #1c1c1e;
        }
        .desc {
          width: 100%;
          height: 276rpx;
          background: rgba(235, 235, 245, 0.3);
          border-radius: 24rpx;
          padding: 16rpx 24rpx;
          .place {
            font-weight: 400;
            font-size: 28rpx;
            color: #aeaeb2;
            line-height: 48rpx;
          }
        }
      }
    }
  }
  .diary-changebtn {
    padding-bottom: 64rpx;
    width: 100%;
    height: 176rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    > image {
      width: 112rpx;
      height: 112rpx;
    }
  }
}
