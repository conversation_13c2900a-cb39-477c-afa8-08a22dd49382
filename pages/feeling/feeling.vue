<template>
	<view class="feeling-pages">
		<view class="feel flex-col">
			<view class="feel-label">
				此刻想记录的心情？
			</view>
			<view class="feel-content w-full flex-row">
				<view :class="'fitem flex-col ' + (current == item.label ? 'current' : '')" :key="index"
							v-for="(item, index) of feels" @click="changeCurrent(item.label)">
					<img class="fitem-icon"
							 :src="`https://cbti.zhisongkeji.com/uniapp-static/${current == item.label ? item.ingicon : item.icon}`"
							 alt="" />
					{{ item.label }}
				</view>
			</view>
			<view class="feel-tab" v-if="current">
				<view :class="'feel-tab-item ' + (selected == item.label ? 'selected' : '')" :key="index"
							v-for="(item, index) of feelHash[current]" @click="selected = item.label">
					{{ item.label }}
				</view>
			</view>
			<view class="feel-tip flex-col" v-if="selected">
				<!-- :key="index" v-for="(item,index) of selectedlist" -->
				<view class="feel-tip-item flex-col">
					<view class="topbar flex-row">
						<view class="topbar-status">
							{{ selected }}
						</view>
						是因为什么事情影响了你的情绪？
					</view>
					<view class="desc">
						<textarea @input="input" :value="content" placeholder="请写下你的情绪，例：我躺在床上一直不能睡" placeholder-class="place" />
					</view>
				</view>
			</view>
		</view>
		<view class="diary-changebtn" v-if="selected" @click="submit()">
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dfinish.png" mode=""></image>
		</view>
	</view>
</template>

<script>
import {
	addfeeling,
	getfeeling,
	editfeeling
} from '@/assets/api/index.js'
import {
	formatDate,
	mergeAndIntersectObjects
} from '../../utils/index.js'
export default {
	data () {
		return {
			feels: [{
				label: '生气',
				icon: 'hc.png',
				ingicon: 'hcing.png'
			}, {
				label: '不开心',
				icon: 'bth.png',
				ingicon: 'bthing.png'
			}, {
				label: '平淡',
				icon: 'yb.png',
				ingicon: 'ybing.png'
			}, {
				label: '开心',
				icon: 'hbc.png',
				ingicon: 'hbcing.png'
			}, {
				label: '兴奋',
				icon: 'fch.png',
				ingicon: 'fching.png'
			}],
			current: null,
			feelHash: {
				'生气': [{
					label: '嫉妒',
					value: '1',
					selected: false
				}, {
					label: '厌恶',
					value: '2',
					selected: false
				}, {
					label: '鄙夷',
					value: '3',
					selected: false
				}, {
					label: '愤怒',
					value: '4',
					selected: false
				}, {
					label: '怨恨',
					value: '5',
					selected: false
				}],
				'不开心': [{
					label: '价值感低',
					value: '1',
					selected: false
				}, {
					label: '无助',
					value: '2',
					selected: false
				}, {
					label: '羞愧',
					value: '3',
					selected: false
				}, {
					label: '悲伤',
					value: '4',
					selected: false
				}, {
					label: '担忧',
					value: '5',
					selected: false
				}, {
					label: '害怕',
					value: '6',
					selected: false
				}, {
					label: '恐惧',
					value: '7',
					selected: false
				}, {
					label: '绝望',
					value: '8',
					selected: false
				}],
				'平淡': [{
					label: '接受',
					value: '1',
					selected: false
				}, {
					label: '平静',
					value: '2',
					selected: false
				}, {
					label: '轻松',
					value: '3',
					selected: false
				}, {
					label: '幸福',
					value: '4',
					selected: false
				}],
				'开心': [{
					label: '自信',
					value: '1',
					selected: false
				}, {
					label: '充满希望',
					value: '2',
					selected: false
				}, {
					label: '幸福',
					value: '3',
					selected: false
				}, {
					label: '喜悦',
					value: '4',
					selected: false
				}, {
					label: '高兴',
					value: '5',
					selected: false
				}],
				'兴奋': [{
					label: '积极',
					value: '1',
					selected: false
				}, {
					label: '惊喜',
					value: '2',
					selected: false
				}, {
					label: '狂喜',
					value: '3',
					selected: false
				}, {
					label: '激动',
					value: '4',
					selected: false
				}]
			},
			selected: null,
			content: '',
			id: '',
		}
	},
	onLoad ({
		id
	}) {
		if (id) {
			uni.setNavigationBarTitle({
				title: '编辑心情日记'
			})
			this.id = id
			this.getDetail(id)
		}
	},
	methods: {
		getDetail (id) {
			getfeeling({
				'id': id
			}).then(result => {
				let {
					moodType,
					moodSubType,
					reason
				} = result
				this.current = moodType
				this.selected = moodSubType
				this.content = reason
			})
		},
		changeCurrent (label) {
			this.current = label
			this.selected = null
			this.content = ''
		},
		input (e) {
			this.content = e.detail.value
		},
		submit () {
			if (!this.content) {
				wx.showToast({
					title: '请写下你的情绪',
					duration: 2000,
					icon: 'none'
				});
				return
			}
			let dataForm = {
				moodType: this.current,
				moodSubType: this.selected,
				reason: this.content
			}
			if (this.id) {
				dataForm.id = this.id
				editfeeling(dataForm).then(result => {
					wx.showToast({
						title: '提交成功',
						duration: 2000,
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack({})
					}, 2000)
				})
			} else {
				addfeeling(dataForm).then(result => {
					wx.showToast({
						title: '提交成功',
						duration: 2000,
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack({})
					}, 2000)
				})
			}

		}
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>