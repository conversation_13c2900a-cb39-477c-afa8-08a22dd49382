<template>
	<view class="pointTable">
		<template v-for="(item, key) in contentData" :key="key">
			<view class="columnContainer">
				<template v-for="(i, k) in item" :key="k" >
					<view class="column" :style="'min-width:'+columnWidth[k]+'px'">
						{{i}}
					</view>
				</template>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		props: ["content"],
		data() {
			return {
				contentData: [],
				columnWidth:[]
			};
		},
		onLoad() {
		},
		mounted() {
			let {
				content
			} = this.content;
			let columnWidth = [];
			for (let item of content) {
				let length = 0;
				for (let e of item) {
					if(e.length > length){
						length = e.length
					}
				}
				columnWidth.push(length * 15)
			}
			this.columnWidth = columnWidth;
			this.contentData = content;
		},
	}
</script>

<style lang="scss">
	.pointTable {
		margin-top: 20upx;
		display: flex;
		width: 100%;
		overflow-x: auto;
		margin-left: -40rpx;
		padding-left: 40rpx;
		.columnContainer {
			flex: 1;
			text-align: center;

			&:first-child {
				.column {
					position: relative;

					&::after {
						content: "";
						position: absolute;
						left: -30upx;
						top: 50%;
						border-radius: 50%;
						width: 25upx;
						height: 25upx;
						margin-top: -17.5upx;
						background-color: #15b2f5;
					}

					&:first-child {
						&::after {
							display: none;
						}
					}
				}
			}

			.column {
				height: 90upx;
				line-height: 90upx;
				border-bottom: 2upx solid rgb(236, 236, 236);

				&:first-child {
					border-top: 2upx solid rgb(236, 236, 236);
				}
			}
		}
	}
</style>
