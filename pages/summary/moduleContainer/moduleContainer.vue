<template>
	<view class="moduleContainer">
		<view class="title">{{title}}</view>
		<view class="content" v-if="contentData">
			<template v-for="(item, key) in contentData" :key="key">
				<showInfo v-if="item.type == 'showInfo'" :content="item" />
				<mp-html v-if="item.type == 'text'" :content="item.content" />
				<image v-if="item.type == 'image'" :src="item.content"></image>
				<pointTable v-if="item.type == 'pointTable'" :content="item" />
				<pointContent v-if="item.type == 'pointContent'" :content="item" style="width: 100%;"/>
			</template>
		</view>
	</view>
</template>

<script>
	import showInfo from "./showInfo.vue";
	import pointTable from "./pointTable.vue";
	import pointContent from "./pointContent.vue";
	import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
	export default {
		props: ["contentContainerData"],
		components: {
			showInfo,
			pointTable,
			pointContent,
			mpHtml
		},
		data() {
			return {
				title: "",
				contentData: [],
			};
		},
		mounted() {
			let {
				title,
				content
			} = this.contentContainerData;
			this.title = title;
			this.contentData = content;
		},
	}
</script>

<style lang="scss">
	.moduleContainer {
		padding: 40upx 0;
		background: #fff;

		.title {
			border-left: 16upx solid rgb(21, 178, 245);
			padding-left: 20upx;
			font-size: 1rem;
		}

		.content {
			padding: 40upx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			color: #666;

			point-table,
			>image{
				width: 100%;
			}

			show-Info {
				width: 48%;
				height: 110upx;
			}
		}
	}
</style>
