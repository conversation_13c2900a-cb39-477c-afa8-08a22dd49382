<template>
	<view class="pointContent">
		<view class="title">{{contentData.title}}</view>
		<mp-html class="content" :content="contentData.content" />
	</view>
</template>

<script>
	import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
	export default {
		props: ["content"],
		components: {
			mpHtml
		},
		data() {
			return {
				contentData: {}
			};
		},
		onLoad() {},
		mounted() {
			this.contentData = this.content;
		},
	}
</script>

<style lang="scss">
	.pointContent {
		margin-top: 20upx;
		width: 100%;
		text-align: left;
		position: relative;
		padding-left: 40upx;

		.title {
			font-size: 30upx;
			&::before {
				content: "";
				border: 11upx solid #15b2f5;
				position: absolute;
				left: -10upx;
				top: 18upx;
				border-radius: 50%;
			}
		}
		.content{
			color: #888888;
			width: 90%;
		}
	}
</style>
