<template>
	<view class="childBox">
		<text class="lable">{{title}}</text>
		<text class="text">{{contentData}}</text>
	</view>
</template>

<script>
	export default {
		props: ["content"],
		data() {
			return {
				title: "",
				contentData: ""
			};
		},
		mounted() {
			let {
				title,
				content
			} = this.content;
			this.title = title;
			this.contentData = content;
		},
	}
</script>

<style lang="scss">
	.childBox {
		border-bottom: 1px solid #EEEEEE;
		margin-top: 20upx;
		width: 48%;

		.lable {
			color: #888888;
		}

		.text {
			display: block;
		}
	}
</style>
