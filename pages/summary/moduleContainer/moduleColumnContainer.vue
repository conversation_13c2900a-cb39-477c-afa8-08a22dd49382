<template>
	<view class="moduleColumnContainer">
		<view class="title">{{title}}</view>
		<view class="content" v-if="contentData">
			<template v-for="(item, key) in contentData">
				<pointList v-if="item.type == 'pointList'" :content="item" :key="key"/>
			</template>
		</view>
	</view>
</template>
<script>
	import pointList from "./pointList.vue";
	export default {
		props: ["contentContainerData"],
		components: {
			pointList
		},
		data() {
			return {
				title: "",
				contentData: [],
			};
		},
		mounted() {
			let {
				title,
				content
			} = this.contentContainerData;
			this.title = title;
			this.contentData = content;
		},
	}
</script>

<style lang="scss">
	.moduleColumnContainer {
		padding: 40upx 0;
		border-bottom: 20upx solid #eee;
		background: #fff;

		.title {
			border-left: 16upx solid rgb(21, 178, 245);
			padding-left: 20upx;
			font-size: 1rem;
		}

		.content {
			padding: 0 0 40upx 0;
			margin: 40upx 40upx 0;
			border-left: 2upx solid #15b2f5;
			point-table,
			>image {
				width: 100%;
			}

			show-Info {
				width: 48%;
				height: 110upx;
			}
		}
	}
</style>
