<template>
	<view class="pointList">
		<view class="title">{{contentData.title}}</view>
		<mp-html class="content" :content="contentData.content" />
	</view>
</template>

<script>
	import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
	export default {
		components: {
			mpHtml
		},
		props: ["content"],
		data() {
			return {
				contentData: {}
			};
		},
		onLoad() {},
		mounted() {
			this.contentData = this.content;
		},
	}
</script>

<style lang="scss">
	.pointList {
		margin-top: 20upx;
		width: 100%;
		text-align: left;
		position: relative;
		padding-left: 40upx;

		.title {
			font-size: 30upx;

			&::before {
				content: "";
				border: 11upx solid #15b2f5;
				position: absolute;
				left: -12upx;
				top: 18upx;
				border-radius: 50%;
			}
		}

		.content {
			color: #666;
		}
	}
</style>
