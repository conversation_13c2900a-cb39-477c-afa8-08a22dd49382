<template>
	<view class="summary">
		<view class="institutionsName">
			<image class="institutionsImg" src="https://cbti.zhisongkeji.com/uniapp-static/logo.png" mode=""></image>
			<text class="institutionsNameText">{{departmentName}}</text>
		</view>
		<template v-for="(item, key) in templateModules" :key="key">
			<!-- <moduleContainer :contentContainerData="item" :key="key" /> -->
			<moduleContainer v-if="item.type == 'moduleContainer'" :contentContainerData="item" />
			<moduleColumnContainer v-if="item.type == 'moduleColumnContainer'" :contentContainerData="item" />
		</template>
		<view class="msg">
			<view class="icon"></view>
			本报告只作为临床参考
		</view>
	</view>
</template>

<script>
	import moduleContainer from "./moduleContainer/moduleContainer.vue";
	import moduleColumnContainer from "./moduleContainer/moduleColumnContainer.vue";
	import {
		getAssembleDataById
	} from '@/assets/api/index.js'
	function _handleShareChannels(provider) {
		let channels = [];
		for (let i = 0, len = provider.length; i < len; i++) {
			switch (provider[i]) {
				case 'weixin':
					channels.push({
						text: '分享到微信好友',
						id: 'weixin',
						sort: 0
					});
					channels.push({
						text: '分享到微信朋友圈',
						id: 'weixin',
						sort: 1
					});
					break;
				default:
					break;
			}
		}
		channels.sort(function(x, y) {
			return x.sort - y.sort;
		});
		return channels;
	}
	export default {
		components: {
			moduleContainer,
			moduleColumnContainer
		},
		data() {
			return {
				templateModules: [],
				departmentName: '',
				url: {
					getAssemble: '/result/getAssembleDataById',
				},
			}
		},
		onLoad(event) {
			let {
				reportId,
			} = event;
			let that = this;
			uni.showLoading({
				title: '加载中...',
				mask: true
			});
			uni.getStorage({
				key: 'departmentName',
				success: function(res) {
					that.departmentName = res.data;
				}
			});
			this.getDetail(reportId);
		},
		onShareAppMessage() {
			return {
				title: this.banner.title,
				path: DETAIL_PAGE_PATH + '?detailDate=' + JSON.stringify(this.banner)
			}
		},
		onNavigationBarButtonTap(event) {
			const buttonIndex = event.index;
			if (buttonIndex === 0) {
				// 分享 H5 的页面
				const shareProviders = [];
				uni.getProvider({
					service: 'share',
					success: (result) => {
						// 目前仅考虑分享到微信
						if (result.provider && result.provider.length && ~result.provider.indexOf('weixin')) {
							const channels = _handleShareChannels(result.provider);
							uni.showActionSheet({
								itemList: channels.map(channel => {
									return channel.text;
								}),
								success: (result) => {
									const tapIndex = result.tapIndex;
									uni.share({
										provider: 'weixin',
										type: 0,
										title: this.banner.title,
										scene: tapIndex === 0 ? 'WXSceneSession' :
											'WXSenceTimeline',
										href: 'https://uniapp.dcloud.io/h5' +
											DETAIL_PAGE_PATH + '?detailDate=' + JSON
											.stringify(this.banner),
										imageUrl: 'https://img-cdn-qiniu.dcloud.net.cn/uniapp/app/<EMAIL>'
									});
								}
							});
						} else {
							uni.showToast({
								title: '未检测到可用的微信分享服务'
							});
						}
					},
					fail: (error) => {
						uni.showToast({
							title: '获取分享服务失败'
						});
					}
				});
			}
		},
		methods: {
			getDetail(id) {
				let _this = this;
				getAssembleDataById({id: id}).then(result => {
					if (result) {
						_this.templateModules = result;
					}
					uni.hideLoading();
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #E5E5E5;
	}

	.summary {
		width: 100%;
		height: 100%;

		.grade,
		.userInfo,
		.institutionsName {
			border-bottom: 20upx solid #eee;
			background: #fff;
		}

		.institutionsName {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 320upx;
		}

		.institutionsNameText {
			font-size: 17pt;
		}

		.institutionsImg {
			width: 180upx*2;
			height: 180upx*2;
			margin-bottom: 20upx;
			border-radius: 50%;
		}

		.msg {
			min-height: 100upx;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			background: #E5E5E5;
			color: #999;
			box-sizing: border-box;
			margin-right: 15upx;
		}

		.msg .icon {
			width: 10upx;
			height: 10upx;
			border-radius: 50%;
			background: #15b2f5;
			margin-right: 30upx;
		}
	}
</style>
