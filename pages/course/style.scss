.course-pages {
	background: #fafcf7;
	&-container {
		width: 100%;
		height: 100%;
		background-image: url('https://oss.ihaier.vip/minipro/test/coursebg.png');
		background-position: left top;
		background-repeat: no-repeat;
		background-size: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		.course-label {
			padding: 32rpx 0 88rpx;
			align-items: center;
			.label {
				width: 80%;
				font-weight: bold;
				font-size: 48rpx;
				color: #ffffff;
				padding-bottom: 18rpx;
			}
			.value {
				font-weight: bold;
				font-size: 32rpx;
				color: #ffffff;
			}
		}
		.course-content {
			width: calc(100% - 64rpx);
			margin: 0 auto;
			background: #ffffff;
			border-radius: 24rpx;
			padding-bottom: 200rpx;
			.course-process {
				height: 96rpx;
				border-bottom: 1rpx solid #e5e5ea;
				padding: 0 32rpx;
				align-items: center;
				font-weight: bold;
				font-size: 28rpx;
				color: #52c41a;
				&-content {
					flex: 1;
					margin: 0 32rpx;
					height: 12rpx;
					background: #edf6ef;
					border-radius: 6rpx;
					padding: 1rpx;
					.currentprocess {
						height: 8rpx;
						background: #52c41a;
						border-radius: 4rpx;
					}
				}
				&-num {
					font-weight: 400;
					font-size: 28rpx;
					color: #636366;
				}
			}

			.course-item {
				padding: 16rpx 32rpx;

				&-title {
					font-weight: bold;
					font-size: 28rpx;
					color: #52c41a;
					position: relative;
					padding-left: 24rpx;
					margin-bottom: 13rpx;
				}
				&-title::before {
					content: '';
					width: 8rpx;
					height: 24rpx;
					background: #52c41a;
					border-radius: 4rpx;
					position: absolute;
					left: 0;
					top: 8rpx;
				}
				&-content {
					font-weight: 400;
					font-size: 28rpx;
					color: #636366;
					line-height: 48rpx;
					.list {
						&-label {
							font-weight: bold;
							font-size: 28rpx;
							color: #1c1c1e;
							margin-bottom: 13rpx;
						}
						&-value {
							&-item {
								align-items: center;

								.unit {
									font-weight: 400;
									font-size: 28rpx;
									color: #636366;
									margin-right: 28rpx;
								}
								.name {
									flex: 1;
									font-weight: 400;
									font-size: 28rpx;
									color: #636366;
								}
								.process {
									width: 30rpx;
									height: 30rpx;
									&-icon{
										width: 100%;
										height: 100%;
									}
									// background: #E5E5EA;
									// border-radius: 50%;
									// display: flex;
									// justify-content: center;
									// align-items: center;
									// position:relative;
									// overflow: hidden;
									// &-center{
									// 	width: 20rpx;
									// 	height: 20rpx;
									// 	background: #ffffff;
									// 	border-radius: 50%;
									// 	position: absolute;
									// 	z-index: 1;
									// }
									// &-bg{
									// 	width: 30rpx;
									// 	height: 30rpx;
									// }
								}
							}
						}
					}
				}
			}
		}
		.cbtn {
			width: calc(100% - 64rpx);
			height: 96rpx;
			background: #52c41a;
			border-radius: 48rpx;
			position: fixed;
			bottom: 32rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #ffffff;
			line-height: 96rpx;
			text-align: center;
			z-index: 1;
		}
	}
}
