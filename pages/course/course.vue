<template>
	<view class="course-pages">
		<view class="course-pages-container" :style="{ paddingTop: navBarHeight + 'px' }">
			<!-- <navbar /> -->
			<view class="course-label flex-col">
				<span class="label">{{ course.paPatientSchemeGroupName }}</span>
				<!-- <span class="value">{{course.value}}</span> -->
			</view>
			<view class="course-content flex-col">
				<view class="course-process flex-row">
					计划进度：
					<view class="course-process-content">
						<view class="currentprocess" :style="{ width: `${(course.currentSchemeProcess)}%` }">

						</view>
					</view>
					<view class="course-process-num">
						已完成 {{ course.currentSchemeFinishNum }}/{{ course.currentSchemeTotalNum }}
					</view>
				</view>
				<view class="course-item flex-col" v-if="course.remark">
					<view class="course-item-title">
						课程介绍
					</view>
					<view class="course-item-content">
						{{ course.remark }}
					</view>
				</view>
				<view class="course-item flex-col">
					<view class="course-item-title">
						课程目录
					</view>
					<view class="course-item-content flex-col">
						<view class="list-label">
							{{ currentPaPatientScheme.schemeName }}
						</view>
						<view class="list flex-col" :key="_item.id" v-for="(_item, _index) of schemeList">
							<view class="list-label">
								第{{ indexToChinese(_index + 1) }}天
							</view>
							<view class="list-value flex-col">
								<view class="list-value-item flex-row" :key="item.id" v-for="(item, index) of _item">
									<span class="unit">{{ item.unit }}</span>
									<span class="name">{{courseType[item.type]}}：{{ item.elementName }}</span>
									<!-- <span class="process">{{item.process}}
									</span> -->
									<view class="process" v-if="item.type !== 4 && item.status == 1">
										<image class="process-icon"
											src="https://cbti.zhisongkeji.com/uniapp-static/finish.png" mode="">
										</image>
									</view>
									<view class="process" v-else-if="item.type == 4 && item.status == 2">
										<image class="process-icon"
											src="https://cbti.zhisongkeji.com/uniapp-static/finish.png" mode="">
										</image>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cbtn" @click="goPage()">
				开始学习
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCurrentSchemeGroupInfo,
		getAction,
		getExecuteDetail,
		getSleepDiary
	} from '@/assets/api/index.js'
	import {
		isObjEmpty,
		getDay
	} from '../../utils/index.js'
	import {
		useStore
	} from 'vuex'

	import navbar from '@/components/navbar.vue'
	import store from '../../store'
	export default {
		components: {
			navbar
		},
		data() {
			const store = useStore();
			return {
				navBarHeight: store.state.navBarHeight || 87,
				currentSchemeDay: [],
				currentPaPatientScheme: {},
				unfinish: [], // 未完成的
				schemeList: [],
				course: {},
				courseType:{
					// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
					1:'文章',
					2:'视频',
					3:'放松训练',
					4:'量表',
					5:'问卷',
					6:'引导页',
				}
			}
		},
		onShow() {
			this.initData()
			//强制模式获取未完成方案
			if(uni.getStorageSync("CompulsoryPlan") == "1") {
				this.getExecuteInfo()
			}
			uni.hideHomeButton();
		},
		methods: {
			initData() {
				this.getPlanInfo()
			},
			indexToChinese(index) {
				// 中文数字映射
				const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
				// 处理个位数
				if (index < 10) {
					return chineseNumbers[index];
				}
				// 处理两位数及以上
				let result = '';
				// 十位
				const ten = Math.floor(index / 10);
				// 个位
				const one = index % 10;
				// 特殊情况：十位为零时，不需要添加“十”字
				if (ten > 0) {
					result += chineseNumbers[ten] + '十';
				}
				// 添加个位，除非是“十”（即10）
				if (one > 0 || index === 10) {
					result += chineseNumbers[one];
				}
				return result;
			},
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					this.course = result
					let {
						currentPaPatientScheme,
						schemeList
					} = result
					let currentSchemeDay = [] //当前方案的日期
					this.currentSchemeDay = currentSchemeDay
					this.currentPaPatientScheme = currentPaPatientScheme

					this.schemeList = this.dealCourse(currentPaPatientScheme.elementList)

					this.unfinish = currentPaPatientScheme.elementList.filter(item => {
						return !item.locked && ((item.type !== 4 &&item.status != 1) || (item.type == 4 &&item.status != 2)) //未锁且未完成的
						// return !item.locked && item.status != 1 && item.type != 2 //未锁且未完成的且没有视频的
					})

				})
			},
			getExecuteInfo() {
				getExecuteDetail().then(result => {
					if (result.length <= 0) {
						this.getSleepDiary()
					}
				})
			},
			getSleepDiary(status) {
				let params = {
					recordTime: this.boardDateParams
				}
				getSleepDiary(params).then(result => {
					let createsleepDiary = (isObjEmpty(result) && getDay(0) == this.boardDateParams) ? true : false
					//如果今日无睡眠日记，并且是强制模式
					if(createsleepDiary && (uni.getStorageSync("CompulsoryPlan") == "1")) {
						uni.showModal({
							title: '提示',
							content: '今日课程已完成,请填写睡眠日记',
							confirmText: '确定',
							showCancel: false,
							success: function(res) {
								if (res.confirm) {
									uni.reLaunch({
										url: '/pages/diaryCreate/diary?timeCreate=' + this.boardDateParams,
									})
									console.log('用户点击确定');
								}
							}
						})
					}
				})
			},
			dealCourse(dataArray) {
				// 分类对象
				let classifiedObj = {};

				// 遍历数组，按照 recordTime 分类
				dataArray.forEach((item, index) => {
					item.unit = this.padWithZeros(index + 1)
					const date = item.day; // 或者使用 item.recordTime.getDate() 等方法获取日期的特定部分
					if (!classifiedObj[date]) {
						classifiedObj[date] = [];
					}
					classifiedObj[date].push(item);
				});

				// 将分类对象转换为数组，并按照日期排序
				let classifiedArray = Object.values(classifiedObj).sort((a, b) => {
					return new Date(a[0].day) - new Date(b[0].day);
				});
				return classifiedArray
			},
			goPage() {
				// let unfinish = [{
				// 	elementId: "1780148456411500546",
				// 	id: "1844536400500654081",
				// 	paPatientSchemeId: "1844536400479682561",
				// 	type: 1
				// },
				// {
					// elementId: "1714116570694729730",
					// id: "1844536400211247105",
					// paPatientSchemeId: "1844536400089612290",
					// type: 3,
				 // }
				// ,{
				// 	elementId: "62b8df599c5a11efbef800163e08bbc9",
				// 	id: "1854411622313828354",
				// 	paPatientSchemeId:"1854411622003449858",
				// 	type: 5,
				// },{
				// 	elementId: "1842902638477705217",
				// 	id: "1865769840000581634",
				// 	paPatientSchemeId: "1865769839874752514",
				// 	type: 6
				// }]
				// let item = unfinish[0]
				let item = this.unfinish[0]
				// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
				let url = ''
				switch (item.type) {
					case 1:
						url = '/pages/courseInfo/article/article?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 2:
						url = '/pages/iframe?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 3:
						url = '/pages/courseInfo/audio/audio?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 4:
						url = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item
							.id +
							"&type=2&status=" + item.dxResultStatus
						break;
					case 5:
						url = '/pages/courseInfo/questionnaire/questionnaire?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 6:
						url = '/pages/courseInfo/guide/index?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
				}
				if (url) {
					uni.navigateTo({
						url: url
					})
				}
			},
			padWithZeros(number) {
				return number.toString().padStart(2, '0');
			}
		}
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>