<template>
	<view class="patientScheme-pages">
		<scroll-view scroll-y="true" class="patientScheme flex-col" @scrollend="scroll()">
			<view class="ptaskitem flex-row" :key="item.id" v-for="item of patientSchemeTasks" @click="selectTable(item)">
				<view class="pcontent">
					<view :class="'ptaskitem-time ' + (item.status != 2 ? 'unfinish' : 'finish')">
						<view class="tlabel">
							<image v-if="item.status != 2" class="iconImg"
								src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
							</image>
							<image v-else class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png"
								mode="widthFix">
							</image>
							请在{{item.limitDate}}之前完成
						</view>
						<image v-if="item.status != 2" class="tstatus"
							src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
						</image>
						<image v-else class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png"
							mode="widthFix">
						</image>
					</view>
					<view class="ptaskitem-label">
						<image class="ptaskitem-label-icon" src="https://cbti.zhisongkeji.com/uniapp-static/taskicon.png" mode="">
						</image>
						{{ item.measureName }}
					</view>
				</view>
				<image class="picon iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
				</image>
			</view>
			<view class="end" v-if="end">
				-已经到底了-
			</view>
		</scroll-view>
	</view>
</template>

<script>
import {
	getPatientSchemeTask
} from '@/assets/api/index.js'
export default {
	data () {
		return {
			pageNo: 1,
			end: false,
			patientSchemeTasks: []
		}
	},
	onShow () {
		this.pageNo = 1
		this.getPatientScheme()
	},
	methods: {
		getPatientScheme () { //量表任务
			getPatientSchemeTask({
				pageNo: this.pageNo,
				pageSize: 50,
			}).then(result => {
				let {
					records,
					total
				} = result
				let list = records
				if (this.pageNo == 1) {
					this.patientSchemeTasks = list
					if (total <= 50) {
						this.end = true
					}
				} else {
					if (this.pageNo < Math.ceil(total / 50) + 1) {
						this.patientSchemeTasks = [].concat(this.patientSchemeTasks, list)
					} else {
						this.end = true
					}
				}
				this.pageNo++
			})
		},
		selectTable (item) {
			uni.navigateTo({
				url: '/pages/beginMeasure/index?reportId=' + item.resultId + '&measureTaskId=' + item.id +
					'&status=' + item.status + '&type=2'
			})
		}
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>
