.patientScheme-pages {
	padding: 0 32rpx;
	.patientScheme {
		width: 100%;
		height: 100vh;
		.ptaskitem {
			padding: 24rpx;
			background: rgba(235, 235, 245, 0.3);
			border-radius: 24rpx;
			margin-bottom: 16rpx;
			.pcontent {
				flex: 1;
				.ptaskitem-time {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					margin-bottom: 16rpx;
					.tlabel {
						flex: 1;
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						.iconImg {
							width: 25rpx;
						}
					}
					.tstatus {
						width: 107rpx;
						height: 28rpx;
					}
				}
				.unfinish {
					.tlabel {
						color: #ff9500;
					}
				}
				.finish {
					.tlabel {
						color: #00c826;
					}
				}
				.ptaskitem-label {
					font-weight: bold;
					font-size: 28rpx;
					color: #1c1c1e;
					margin-bottom: 8rpx;
					display: flex;
					align-items: center;
					&-icon {
						width: 32rpx;
						height: 32rpx;
						margin-right: 8rpx;
					}
				}
			}
		}
	}
}
