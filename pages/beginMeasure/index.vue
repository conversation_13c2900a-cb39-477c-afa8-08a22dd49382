<template>
	<view class="beginMeasure-page">
		<view class="title">
			{{measureInfo.name}}
		</view>
		<view class="description">
			{{measureInfo.description}}
		</view>
		<view class="form">
			<view class="baseInfo">
				<view class="form-item">
					<view class="title">姓名</view>
					<input class="uni-input" name="name" placeholder="请输入您的姓名" v-model="userName" />
				</view>
				<view class="form-item ">
					<view class="title">性别</view>
					<view class="list-cell-db">
						<picker name="cultural" @change="bindSexLevelChange" :value="sexLevelIndex" :range="sexLevel"
							themeColor="rbg(21,178,245)">
							<view class="uni-input">{{sexLevel[sexLevelIndex]}}</view>
						</picker>
						<uni-icons class="icon" type="arrowright" size="9" color="#999999"></uni-icons>
					</view>
				</view>
				<view class="form-item">
					<view class="title">生日</view>
					<view class="list-cell-db">
						<picker mode="date" name="birthday" :value="date" :end="currentDate" @change="bindDateChange">
							<view class="uni-input">{{date}}</view>
						</picker>
						<uni-icons class="icon" type="arrowright" size="9" color="#999999"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		<view class="submit" @click="submit()">
			{{beginTitle}}
		</view>
	</view>
</template>

<script>
	import {
		getUser, getMeasureInfo, startQuestions, startAnswer
	} from '@/assets/api/index.js'
	export default {
		data() {
			const currentDate = this.getDate({
				format: true
			});
			return {
				currentDate,
				//1-量表答题，2-量表任务答题
				type: '1',
				measureId: "",
				measureTaskId: '',
				measureInfo: {},
				reportId: '',
				status: '',
				beginTitle: '开始答题',
				url: {
					getMeasureInfo: 'measureTask/getMeasureInfo',
					startAnswer: 'answer/startAnswer',
					startQuestions: 'measureTask/startQuestions',
					queryById: 'patient/queryById',
				},
				userInfo: {
					name: '',
					sex: '',
					birthday: ''
				},
				userName: '',
				sexLevelIndex: 0,
				date: '请选择日期',
				sexLevel: ["请选择", "男", "女"],
				sex: "0",
				measureInfoSuccess: false
			}
		},
		onLoad(parameter) {
			let {
				measureTaskId,
				measureId,
				reportId,
				status,
				type
			} = parameter;
			this.measureId = measureId;
			this.measureTaskId = measureTaskId;
			this.reportId = reportId;
			this.status = status;
			this.type = type;
			
			if(this.status == 0) {
				this.beginTitle = '开始答题'
			} else if (this.status == 1) {
				this.beginTitle = '继续答题'
			} else if (this.status == 2) {
				this.beginTitle = '查看报告'
			}
		},
		mounted() {
			this.getUserInfo()
			this.loadData()
		},
		methods: {
			getUserInfo() {
				getUser().then(result => {
					if (result) {
						this.userInfo = result
						if(this.userInfo) {
							this.userName = this.userInfo.name
							this.sex = this.userInfo.sex
							this.date = this.userInfo.birthday
							if(this.sex == 1) {
								this.sexLevelIndex = 1
							} else if (this.sex == 2) {
								this.sexLevelIndex = 2
							} else {
								this.sexLevelIndex = 0
							}
						}
					}
				})
			},
			submit() {
				let that = this;
				if (this.status == 1) {
					// 已开始
					uni.navigateTo({
						url: '/pages/answer/index?reportId=' + this.reportId + '&measureTaskId=' + this.measureTaskId,
					})
				} else if (this.status == 2) {
					// 已完成
					uni.navigateTo({
						url: "/pages/summary/summary?reportId=" + this.reportId,
					});
				} else if (this.status == 0) {
					if (!this.measureInfoSuccess) return
					// 未开始

					let that = this;
					if (this.measureTaskId) {
						uni.navigateTo({
							url: '/pages/answer/index?reportId=' +  this.reportId + '&measureTaskId=' + this.measureTaskId,
						})
					} else {
						startAnswer({ measureId: this.measureId }).then(result => {
							uni.navigateTo({
								url: '/pages/answer/index?reportId=' + result.id,
							})
						})
					}
				}
			},
			loadData() {
				let that = this;
				getMeasureInfo({
					id: this.type == '1' ? this.measureId : this.measureTaskId,
					type: this.type
				}).then(result => {
					if (result) {
						that.measureInfo = result;
						that.measureId = result.id;
						that.measureInfoSuccess = true
					} else {
						uni.showToast({
							title: result.message,
							icon: 'error'
						})
					}
				})
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 120;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			bindSexLevelChange(e) {
				this.sexLevelIndex = e.detail.value;
				this.sex = e.detail.value;
			},
			bindDateChange(e) {
				this.date = e.detail.value;
			},
			verify() {
				let verifyKey = ["userName", "sex", "date"];
				let verifyRules = {
					userName: (this.userName ? true : false),
					sex: this.sex == "0" ? false : true,
					date: this.date == '请选择日期' ? false : true,
				};
				let verifyMsg = {
					userName: "请输入你的姓名",
					sex: "请选择性别",
					date: "请选择你的生日"
				}
				let verityContent = true;
				for (let i = 0; i < verifyKey.length; i++) {
					let key = verifyKey[i];
					if (!verifyRules[key]) {
						uni.showToast({
							title: verifyMsg[key],
							icon: "none",
							duration: 2000
						});
						verityContent = false;
						break;
					}
				}
				return verityContent;
			},
		}
	}
</script>

<style lang="scss">
	@import "./style.scss";
</style>
