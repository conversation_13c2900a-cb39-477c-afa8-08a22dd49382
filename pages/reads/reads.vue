<template>
	<view class="reads-pages">
		<scroll-view scroll-y="true" class="reads flex-col" @scrollend="scroll()">
			<view class="readitem flex-row" :key="item.id" v-for="item of reads" @click="goInfo(item.id)">
				<view class="readitem-cover">
					<image :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${item.titleImg}`" mode="widthFix">
					</image>
				</view>
				<view class="readitem-content flex-col">
					<label class="label">
						{{ item.title }}
					</label>
					<view class="author">
						作者：{{ item.author }}
					</view>
				</view>
			</view>
			<view class="end" v-if="end">
				-已经到底了-
			</view>
		</scroll-view>
	</view>
</template>

<script>
import {
	getReads
} from '@/assets/api/index.js'
export default {
	data () {
		return {
			reads: [],
			pageNo: 1,
			end: false
		}
	},
	onLoad () {
		this.getList()
	},
	methods: {
		goInfo (id) {
			uni.navigateTo({
				url: '/pages/reads/detail?id=' + id
			})
		},
		scroll () {
			this.getList()
		},
		getList () {
			getReads({
				pageNo: this.pageNo,
				pageSize: 50,
			}).then(result => {
				let {
					records,
					total
				} = result

				if (this.pageNo == 1) {
					this.reads = records
					if (total <= 50) {
						this.end = true
					}
				} else {
					if (this.pageNo < Math.ceil(total / 50) + 1) {
						this.reads = [].concat(this.reads, records)
					} else {
						this.end = true
					}
				}
				this.pageNo++
			})
		},
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>
