<template>
	<view class="detail-page">
		<view class="name">
			{{ detail.title }}
		</view>
		<view class="msg">
			<label>作者：{{ detail.author }}</label>
			<label>浏览量：{{ detail.hits }}</label>
		</view>
		<view class="desc" v-html="detail.content">
		</view>
	</view>
</template>

<script>
import {
	getReadInfo,
	addBrowse
} from '@/assets/api/index.js'
export default {
	data () {
		return {
			detail: {}
		}
	},
	onLoad ({
		id
	}) {
		this.getInfo(id)

	},
	methods: {
		navigateBack () {
			uni.navigateBack();
		},
		getInfo (id) {
			getReadInfo({ "id": id }).then(result => {
				this.detail = result
			})
			addBrowse({ "id": id }).then(result => { })
		}
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>