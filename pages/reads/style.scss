.reads-pages {
  padding: 0 32rpx;
  .reads {
    width: 100%;
    height: 100vh;
    .readitem {
      padding: 26rpx;
      border-bottom: 1rpx solid #e5e5ea;
      &-cover {
        width: 180rpx;
        height: 120rpx;
        overflow: hidden;
        margin-right: 24rpx;
        border-radius: 24rpx;
        > image {
          width: 100%;
          object-fit: cover;
        }
      }
      &-content {
        flex: 1;
        height: 120rpx;
        justify-content: space-between;
        .label {
          font-weight: 400;
          font-size: 28rpx;
          color: #1c1c1e;
        }
        .author {
          font-weight: 400;
          font-size: 24rpx;
          color: #8e8e93;
        }
      }
    }
  }
}

.detail-page {
  width: 100%;
  height: 100%;
  padding: 16px 20px;
  .name {
    font-weight: 700;
    font-size: 17px;
    color: #2b323c;
    margin-bottom: 5px;
  }

  .msg {
    font-weight: 400;
    font-size: 12px;
    color: #5b5d6a;
    display: flex;
    margin-bottom: 5px;
    > label {
      flex: 1;
    }
  }

  .desc {
    color: #222;
    font-size: 15px;
    font-weight: 400;

    p {
      font-size: 15px;
      font-weight: 400;
    }
    img {
      width: 100% !important;
      margin: 10px 0;
    }
  }
}
