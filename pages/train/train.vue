<template>
	<view class="train-page"
		:style="{backgroundImage:'url(https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/'+detail.cover+')'}">
		<!-- @touchend="touchend" -->
		<view class="train" :style="{paddingTop:navBarHeight + 'px'}">
			<navbar :isCustom="true">
				<view class="titlenavbar" slot="left">放松训练</view>
			</navbar>
			<scroll-view scroll-x="true" class="songsheetScroll" :scroll-with-animation="true">
				<view class="songsheet">
					<view @click="changeSongsheet(item.id)"
						:class="'songsheet-item ' + (currentSongsheet  == item.id ? 'currentSongsheet' :'') "
						:key="index" v-for="(item,index) of songsheet">
						{{item.label}}
					</view>
				</view>
			</scroll-view>
			<scroll-view scroll-y="true" class="audiocontent flex-col"
				:style="{height:`calc(${screenHeight} - ${navBarHeight + 47}px);`}">
				<view class="audiotbox" :style="{ height: `calc(${screenHeight} - ${navBarHeight + 241}px)` }">
					<view>{{audioName}}</view>
				</view>
				<view class="audiombox flex-col">
					<view class="slider">
						<view class="slider-time">
							<view class="slider-time-container" v-if="durationAudio">
								{{Math.floor(currentTime / 60)}}:{{currentTime % 60}}
								/{{Math.floor(durationAudio / 60)}}:{{durationAudio % 60}}
							</view>
						</view>
						<slider :value="percentAudio" max="100" block-color="#5856D6" block-size="12"
							activeColor="#5856D6">
						</slider>
					</view>
					<view class="audiombox-content flex-row">
						<view class="audiombox-item flex-col" @click="showTimer = true">
							<image v-if="timer != null" class="audiombox-item-icon"
								src="https://cbti.zhisongkeji.com/uniapp-static/timeing.png" mode=""></image>
							<image v-else class="audiombox-item-icon"
								src="https://cbti.zhisongkeji.com/uniapp-static/ds.png" mode=""></image>

							{{timer != null ? formatSeconds(timer) : '定时'}}
						</view>
						<image class="audiombox-play" @click="pauseAudio()" v-if="isPlay"
							src="https://cbti.zhisongkeji.com/uniapp-static/palying.png" mode=""></image>
						<image class="audiombox-play" @click="playAudio()" v-else
							src="https://cbti.zhisongkeji.com/uniapp-static/stopPlay.png" mode="">
						</image>
						<view class="audiombox-item flex-col" @click="loopAudio()">
							<image class="audiombox-item-icon" src="https://cbti.zhisongkeji.com/uniapp-static/dq.png"
								mode="" v-if="isloop"></image>
							<image class="audiombox-item-icon"
								src="https://cbti.zhisongkeji.com/uniapp-static/noloop.png" mode="" v-else></image>
							循环播放
						</view>
					</view>
				</view>
				<view class="audiobbox">
					<view :class="'audiobbox-item flex-col ' + (current == index ? 'current':'')" :key="index"
						v-for="(item,index) of list" @click="changeItem(index)">
						<image class="audiobbox-item-cover"
							:src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${item.cover}`" mode="">
						</image>
						{{item.label}}
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
	<view class="timer flex-col" v-if="showTimer" @click.stop="showTimer = false">
		<view class="timer-label" @click.stop="showTimer = false">
			定时停止播放
		</view>
		<view class="titem" :key="index" v-for="(item,index) of timers" @click="confirmTimer(item.value)">
			<view :class="'titem-label ' + (durationSet == item.value ? 'check':'')">
				{{item.label}}
			</view>
			<image v-if="durationSet == item.value" class="titem-icon"
				src="https://cbti.zhisongkeji.com/uniapp-static/check.png" mode=""></image>
		</view>
		<view class="titem" @click.stop="showTimer=false">
			<view class="titem-label">
				收起
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCategoryTrain,
		getTrain
	} from '@/assets/api/index.js'
	import {
		useStore
	} from 'vuex'

	import navbar from '@/components/navbar.vue'
	import {
		List
	} from 'echarts';
	let innerAudioContext = uni.createInnerAudioContext(); //引用组件
	export default {
		components: {
			navbar
		},
		data() {
			const store = useStore();
			return {
				audiotboxheight:0,
				currentTime: 0,
				percentAudio: '',
				durationAudio: '',
				bgImg: '',
				user: store.state.user,
				navBarHeight: store.state.navBarHeight || 87,
				glideDirection: '下滑',
				isPlay: false, // 是否播放
				isloop: true,
				showTimer: false,
				timerset: null,
				timer: null,
				durationSet: 0,
				timers: [{
					label: '不开启',
					value: 0
				}, {
					label: '2分钟',
					value: 120000
				}, {
					label: '10分钟',
					value: 600000
				}, {
					label: '15分钟',
					value: 900000
				}, {
					label: '30分钟',
					value: 1800000
				}, {
					label: '60分钟',
					value: 3600000
				}],
				currentSongsheet: {},
				current: 0,
				songsheet: [],
				list: [],
				isInit: false, //音频初始化完成
				ready: false,
				srcChange: false,
				init: false,
				detail: {},
				audioName: '',
				screenHeight: ''
			}
		},
		onLoad() {
			this.getCategoryTrain()
			this.getScreenHeight()
		},
		onHide() {
			console.log('onHide--')
			if (innerAudioContext) {
				this.isPlay = false
				innerAudioContext.pause()
				// innerAudioContext.destroy(); // 销毁音频上下文
			}
			this.clearCountdown();
		},
		onUnload() {
			console.log('onUnload-')
		},
		methods: {
			getScreenHeight() {
				let that = this
				wx.getSystemInfo({
					success: function(res) {
						console.log(res.windowHeight); // 获取当前设备的可用窗口高度
						that.screenHeight = res.windowHeight + 'px'
						console.log('screenHeight', this.screenHeight)
					}
				});
			},
			formatSeconds(seconds) {
				var date = new Date(null);
				date.setSeconds(seconds / 1000);
				var m = date.getMinutes().toString().padStart(2, '0');
				var s = date.getSeconds().toString().padStart(2, '0');
				return m + ':' + s;
			},
			getCategoryTrain() {
				getCategoryTrain().then(result => {
					let list = result.map(item => {
						return {
							id: item.id,
							label: item.title,
							bg: item.titleImg,
						}
					})
					this.songsheet = list
					const id = list[0].id
					this.currentSongsheet = id
					this.getTrain(id)
				})
			},
			getTrain(categoryId) {
				getTrain({
					"categoryId": categoryId,
					pageSize: 100,
					pageNo: 1
				}).then(result => {
					let list = result.records.map(item => {
						return {
							id: item.id,
							label: item.title,
							cover: item.titleImg,
							url: item.voicePath
						}
					})
					this.list = list
					this.detail = list[0]
					this.audioName = this.detail.label
					this.audioInit()
				})
			},
			audioInit() {
				// console.log('audioInit', innerAudioContext)
				if (innerAudioContext) {
					innerAudioContext.stop();
					innerAudioContext.destroy(); // 销毁之前的实例
				}
				innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.src = this.detail.url

				innerAudioContext.autoplay = false
				innerAudioContext.loop = true
				// innerAudioContext.onCanplay(() => {
				// 	console.log('onCanplay')
				//    if (this.isPlay) {
				//     innerAudioContext.play();
				//    }
				// });
				//开始播放
				innerAudioContext.onPlay(() => {
					console.log('开始播放');
					this.isPlay = true
					// if (!this.isPlay) {
					// 	// this.playAudio()
					// 	this.isPlay = true
					// 	innerAudioContext.play();
					// }
				});

				//音频播放进度更新事件
				innerAudioContext.onTimeUpdate((res) => {
					console.log('音频播放进度更新事件');
					if (this.currentTime == 0) {
						uni.hideLoading();
					}
					this.durationAudio = Math.round(innerAudioContext.duration);
					this.currentTime = Math.round(innerAudioContext.currentTime);
					this.percentAudio = Math.round((innerAudioContext.currentTime / this.durationAudio) * 100)

				})
				//onSeeked
				innerAudioContext.onSeeked(() => {
					console.log('onSeeked---', innerAudioContext.currentTime)
				})
				//音频暂停事件
				innerAudioContext.onPause((e) => {
					console.log('播放暂停');
					if (this.isPlay) {
						console.log('播放暂停里面呢');
						this.pauseAudio();
					}
				});
				//音频停止事件
				innerAudioContext.onStop((e) => {
					console.info('播放停止');
				});
				// 监听错误事件
				innerAudioContext.onError((error) => {
					console.log(`播放出错：${error.errMsg}`)
					console.log(`完整报错：${JSON.stringify(error)}`)
					if (error.errCode === -1) {
						console.log('重新执行')
						this.playAudio();
					} else {
						// 比如文件不存在等情况，会进入到这个提示
						uni.showToast({
							title: "播放失败：" + error.errMsg,
							icon: "none",
						});
						if (this.isPlay) {
							this.pauseAudio();
						}
					}
				})
				innerAudioContext.onEnded(() => {
					console.log('onEnded----')
					if (this.isloop) {
						console.log('onEnded--继续播放--innerAudioContext')
						this.currentTime = 0
						this.durationAudio = 0
						this.percentAudio = 0
						innerAudioContext.seek(0)
					} else {
						console.log('onEnded--停止播放--')
						this.pauseAudio()
					}
				})

				this.init = true;

			},
			playAudio() {
				console.log('innerAudioContext.paused', innerAudioContext.paused)
				if (innerAudioContext.paused) {
					if (this.currentTime > 0) {
						innerAudioContext.seek(this.currentTime);
					} else {
						uni.showLoading({
							title: '音频文件加载中',
							mask: true,
						});
					}
					this.isPlay = true
					innerAudioContext.play();
				} else {
					this.isPlay = true

					console.log('音频已经在播放');
				}
			},
			pauseAudio() {
				if (innerAudioContext.paused) {
					console.log('音频已经暂停');
					return;
				}
				this.isPlay = false
				innerAudioContext.pause();
			},
			loopAudio() {
				this.isloop = !this.isloop
				innerAudioContext.loop = this.isloop
			},
			startCountdown() {
				this.countdownTick(); // 调用递归的 setTimeout 方法
			},
			countdownTick() {
				console.log('this.timer:', this.timer);
				if (this.timer > 0) {
					this.timer -= 1000;
					this.timerset = setTimeout(() => { // 保存 setTimeout 的 ID
						this.countdownTick(); // 递归调用
					}, 1000);
				} else {
					clearTimeout(this.timerset); // 清除当前的 setTimeout
					this.pauseAudio(); // 倒计时结束，播放音频
					this.clearCountdown(); // 清除计时器并重置状态
				}
			},
			clearCountdown() {
				console.log('clearCountdown');
				clearTimeout(this.timerset); // 清除旧的 setTimeout
				this.timer = null; // 将 timer 重置为 null
				this.durationSet = 0; // 将 durationSet 重置为 0
				this.showTimer = false; // 隐藏倒计时显示
				this.timerset = null; // 重置 timerset
			},
			confirmTimer(value) {
				if (!this.isPlay) return; // 如果未播放，直接返回
				this.clearCountdown(); // 清除旧的倒计时
				this.timer = value; // 设置新的倒计时时间
				this.durationSet = value; // 设置新的倒计时时间
				if (this.isPlay) {
					this.startCountdown(); // 开始新的倒计时
				}
			},
			changeSongsheet(id) {
				this.currentSongsheet = id
				this.current = 0
				this.isPlay = false
				innerAudioContext.pause()
				innerAudioContext.seek(0)
				this.currentTime = 0
				this.durationAudio = 0
				this.percentAudio = 0
				this.getTrain(id)

			},
			changeItem(index) {
				this.current = index
				this.isPlay = false
				innerAudioContext.pause()
				innerAudioContext.seek(0)
				this.currentTime = 0
				this.durationAudio = 0
				this.percentAudio = 0
				this.detail = this.list[index]
				this.audioName = this.detail.label
				console.log('audioName', this.audioName)
				this.audioInit()
			}
		},
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>