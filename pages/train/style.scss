.train-page {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	.train {
		background-color: RGBA(0, 0, 0, 0.1);
	}
	.titlenavbar {
		font-weight: bold;
		font-size: 40rpx;
		color: #ffffff;
		padding-left: 32rpx;
	}
	.songsheetScroll {
		width: 100%;
		height: 94rpx;
		white-space: nowrap;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.4);
		overflow: visible;
		.songsheet {
			padding: 32rpx 0;
			display: flex;
			&-item {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.4);
				display: block;
				padding: 0 24rpx;
				position: relative;
			}
			&-item:after {
				content: '';
				position: absolute;
				width: 2rpx;
				height: 12rpx;
				bottom: -32rpx;
				left: 50%;
				background: rgba(255, 255, 255, 0.4);
			}
			.currentSongsheet {
				font-weight: bold;
				font-size: 32rpx;
				color: #ffffff;
			}
			.currentSongsheet:after {
				font-weight: bold;
				font-size: 32rpx;
				color: #ffffff;
				content: '';
				position: absolute;
				width: 4rpx;
				height: 24rpx;
				bottom: -32rpx;
				left: 50%;
				background: #ffffff;
			}
		}
	}
	.audiocontent {
		flex: 1;
		overflow-y: auto;
		.audiotbox {
			width: 100%;
			font-weight: 300;
			font-size: 72rpx;
			color: #ffffff;
			// writing-mode: vertical-rl;
			// letter-spacing: 20px;
			// transform: rotate(-90deg);
			//   transform-origin: left top;
			//   white-space: nowrap;
			display: flex;
			justify-content: center;
			align-items: center;
			> view {
				max-height: 60%;
				writing-mode: vertical-lr; /* 竖直排列，从左到右 */
				-webkit-writing-mode: vertical-lr; /* 兼容 iOS */
				transform: rotate(0deg); /* 不需要额外旋转 */
				letter-spacing: 20px; 
			}
		}
		.audiombox {
			width: 100%;
			height: 310rpx;
			flex-direction: column;
			align-items: center;
			margin-bottom: 80rpx;

			.slider {
				width: 100%;
				display: flex;
				flex-direction: column;
				color: #ffffff;

				&-time {
					height: 22upx;
					padding: 0 18px;
					&-container {
						display: flex;
					}
				}
			}
			&-content {
				width: 100%;
				justify-content: space-around;
				align-items: center;
				.audiombox-item {
					align-items: center;
					&-icon {
						width: 136rpx;
						height: 136rpx;
					}
					font-weight: 400;
					font-size: 24rpx;
					color: #ffffff;
				}
				.audiombox-play {
					width: 160rpx;
					height: 160rpx;
				}
			}

			//     &-up {
			//       width: 60rpx;
			//       height: 16rpx !important;
			// min-height: 16rpx;
			//       margin: 26rpx auto;
			//     }
		}
		.audiobbox {
			// height: 968upx;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(3, 238rpx);
			grid-column-gap: 40rpx;
			grid-row-gap: 40rpx;
			padding: 0 32rpx;
			&-item {
				align-items: center;
				&-cover {
					width: 100%;
					height: 182rpx;
					border-radius: 16rpx;
					margin-bottom: 16rpx;
				}
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
			}
			.current {
				.audiotbox-item-cover {
					border: 4rpx solid #ffffff;
				}
				font-weight: bold;
				font-size: 28rpx;
				color: #ffffff;
			}
		}
	}
}
.timer {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100vh;
	z-index: 1;
	background-color: rgba(0, 0, 0, 0.56);
	align-items: center;
	justify-content: center;
	&-label {
		font-weight: bold;
		font-size: 32rpx;
		color: #ffffff;
		padding: 56rpx 0;
		width: 100%;
		text-align: center;
	}
	.titem {
		width: 562rpx;
		height: 96rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #ffffff;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		&-label {
			flex: 1;
			text-align: center;
		}
		.check {
			font-weight: bold;
			color: #ffd60a;
		}
		&-icon {
			position: absolute;
			width: 32rpx;
			height: 32rpx;
			right: 0;
		}
	}
}
