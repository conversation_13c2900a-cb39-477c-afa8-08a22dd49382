.user-pages {
  background-image: url("https://cbti.zhisongkeji.com/uniapp-static/userbg.png");
  background-position: top left;
  background-size: 100%;
  background-repeat: no-repeat;
  .titlenavbar {
    font-weight: bold;
    font-size: 40rpx;
    color: #ffffff;
    padding-left: 32rpx;
  }
  .user {
    align-items: center;
	margin-bottom: 20rpx;
    &-avatar {
      width: 112rpx;
      height: 112rpx;
	  object-fit: contain;
      border-radius: 50%;
      margin-bottom: 16rpx;
    }

    &-name {
      font-weight: bold;
      font-size: 28rpx;
      color: #ffffff;
      margin-bottom: 8rpx;
    }
    &-phone {
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
    }
    &-record {
      width: calc(100% - 64rpx);
      height: 128rpx;
      background: rgba(255, 255, 255, 0.8);
      box-shadow: 0px 4 12rpx 0px rgba(145, 160, 255, 0.2);
      border-radius: 24rpx;
      backdrop-filter: blur(10px);
      margin-top: 18rpx;
      &-item {
        flex: 1;
        height: 72rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        .label {
          font-weight: 400;
          font-size: 22rpx;
          color: #8e8e93;
        }
        .value {
          font-weight: bold;
          font-size: 32rpx;
          color: #1c1c1e;
        }
      }
      .middle {
        border-left: 2rpx solid #e5e5ea;
        border-right: 2rpx solid #e5e5ea;
      }
    }
  }
  .list {
    margin: 0 40rpx;
    &-item {
      width: 100%;
      height: 108rpx;
      border-bottom: 1rpx solid #e5e5ea;
      display: flex;
      align-items: center;
      &-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 24rpx;
      }
      &-label {
        flex: 1;
        font-weight: 400;
        font-size: 28rpx;
        color: #1c1c1e;
      }
      &-info {
        font-weight: 400;
        font-size: 28rpx;
        color: #8e8e93;
        margin-right: 16rpx;
      }
      &-next {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}
