<template>
	<view class="user-pages" :style="{ paddingTop: navBarHeight + 'px' }">
		<navbar :isCustom="true">
			<view class="titlenavbar" slot="left">我的</view>
		</navbar>
		<view class="user flex-col">
			<image class="user-avatar" v-if="user.avatarUrl" :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${user.avatarUrl}`" mode=""></image>
			<image class="user-avatar" v-else src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png" mode=""></image>
			<view class="user-name">
				{{ user.name }}
			</view>
			<view class="user-phone">
				{{ hiddenMobile(user.telphone) }}
			</view>
			<view class="user-record flex-row">
				<view class="user-record-item ">
					<view class="label">
						睡眠日记
					</view>
					<view class="value">
						{{ user.sleepDiaryNum }}
					</view>
				</view>
				<view class="user-record-item middle">
					<view class="label">
						平均睡眠时长
					</view>
					<view class="value">
						{{ user.averageSleepDurationLabel }}
					</view>
				</view>
				<view class="user-record-item">
					<view class="label">
						训练时长
					</view>
					<view class="value">
						{{ user.totalTrainDurationLabel }}
					</view>
				</view>
			</view>
		</view>
		<view class="list flex-col">
			<view class="list-item flex-row" :key="index" v-for="(item, index) of list" @click="goPage(index, item.path)">
				<image class="list-item-icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${item.icon}.png`" mode="">
				</image>
				<view class="list-item-label">
					{{ item.label }}
				</view>
				<view class="list-item-info">
					{{ item.info }}
				</view>
				<image class="list-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
import {
	useStore
} from 'vuex'
import {
	getUser
} from '@/assets/api/index.js'

import navbar from '@/components/navbar.vue'
export default {
	components: {
		navbar
	},
	data () {
		const store = useStore();
		return {
			user: store.state.user,
			navBarHeight: store.state.navBarHeight || 87,
			list: [{
				icon: 'icon10',
				label: '我的档案',
				info: `已完善${store.state.user.completeness}%`,
				path: '/pages/users/personal/personal'
			}, {
				icon: 'icon11',
				label: '我的医生',
				info: store.state.user.doctorName,
				path: '/pages/doctor/doctor'
			}, {
				icon: 'icon12',
				label: '我的睡眠日记',
				info: '',
				path: '/pages/users/weekly/weekly'
			}, {
				icon: 'icon13',
				label: '我的任务',
				info: '',
				path: '/pages/users/mission/mission'
			}, {
				icon: 'icon14',
				label: '我的评测',
				info: '',
				path: '/pages/users/myReview/myReview' //tod0 没有接口
			}, {
				icon: 'icon15',
				label: '我的预约',
				info: '',
				path: '/pages/users/myAppointment/myAppointment'
			}, {
				icon: 'icon16',
				label: '我的方案',
				info: '',
				path: '/pages/users/myCourse/myCourse'
			}, {
				icon: 'icon17',
				label: '设置',
				info: '',
				path: '/pages/users/setting/setting'
			}]
		}
	},
	onLoad () {
		this.getUser()
	},
	methods: {
		goPage (index, path) {
			if (index == 1) {
				// 医生为菜单页面
				uni.switchTab({
					url: path
				})
			} else {
				uni.navigateTo({
					url: path
				})
			}
		},
		getUser () {
			getUser().then(result => {
				this.user = result
				uni.setStorageSync('user', result)
				this.$store.commit('setUser', result)
			})
		},
		hiddenMobile (mobile) {
			let _num = mobile + '';
			let reg = /(\d{3})\d{4}(\d{4})/;
			let _mobile = _num.replace(reg, '$1****$2');
			return _mobile;
		},
	}
}
</script>

<style lang="scss">
@import './style.scss';
</style>