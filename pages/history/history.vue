<template>
	<view class="chathistory-pages">
		<view class="chathistory-search flex-row">
			<img class="chathistory-search-icon" src="https://cbti.zhisongkeji.com/uniapp-static/search.png" alt="" />
			<input class="chathistory-search-input" @input="input" confirm-type="search" placeholder="搜索聊天记录关键字…"
						 placeholder-class="place" />
		</view>
		<scroll-view scroll-y="true" class="chathistory-content flex-col" @scrolltolower="scroll()">
			<items :list="list" />
		</scroll-view>
	</view>
</template>

<script>
import {
	debounce
} from '../../utils/index.js'
import {
	getleavemessage,
} from '@/assets/api/index.js'
import items from '@/components/chatitem/index.vue'
export default {
	components: {
		items
	},
	data () {
		return {
			pageNo: 1,
			pageSize: 50,
			end: false,
			list: [],
			keyword: ''
		}
	},
	onLoad () {
		this.getHistory()
	},
	methods: {
		input (e) {
			this.keyword = e.detail.value
			this.end = false
			this.list = []
			this.pageNo = 1

			debounce(this.getHistory(), 1000)
		},
		getHistory () {
			getleavemessage({
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				keyword: this.keyword
			}).then(result => {
				//type1患者 2医生
				let {
					records,
					total
				} = result
				if (total == 0) {
					return
				}
				let list = records.map(item => {
					return {
						role: item.type == '1' ? 'query' : 'answer',
						message: item.content
					}
				})
				// let reverselist = list.reverse();

				if (this.pageNo == 1) {
					this.list = list
					if (total <= this.pageSize) {
						this.end = true
					}

				} else {
					if (this.pageNo < Math.ceil(total / this.pageSize) + 1) {
						this.list = [].concat(this.list, list)

					} else {
						this.end = true
					}
				}
				this.pageNo++
			})
		},
		scroll () {

			this.getHistory()
		},
	}
}
</script>


<style lang="scss">
@import './style.scss';
</style>