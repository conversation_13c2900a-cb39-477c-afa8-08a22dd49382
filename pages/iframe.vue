<template>
	<view><web-view :src="isSrc" @message="handleMessage"></web-view></view>
</template>

<script>
	import {
		getAction, finishCourseVideoInfo
	} from '@/assets/api/index.js'
	import {
		useStore
	} from 'vuex'
	import store from '../store'
export default {
	data() {
		const store = useStore();
		return {
			isSrc: '',
			progressParams: [],
		};
	},
	onLoad({id,schemeId,elementId}) {	
		this.initEncryptedString(id,schemeId,elementId)//获取加密
	},
	onUnload() {
		this.putPlayProgress()
	},
	methods:{
		handleMessage(event) {
			// 处理iframe发送过来的消息
			this.progressParams = event.detail.data;
		},
		initEncryptedString(id,schemeId,elementId) {
			let patientId = store.state.user.id
			let tenantId = store.state.tenantId
			this.isSrc = `https://cbti.zhisongkeji.com/uniapp/scheme?patientId=${patientId}&id=${id}&schemeId=${schemeId}&tenantId=${tenantId}`
			console.log(this.isSrc)
		},
		putPlayProgress() {
			if (this.progressParams.length <= 0) return
			finishCourseVideoInfo(this.progressParams[this.progressParams.length - 1]).then(result => {})
		}
	},
};
</script>

<style lang="scss"></style>
