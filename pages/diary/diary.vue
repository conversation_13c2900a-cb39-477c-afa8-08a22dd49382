<template>
	<view class="diary-pages">
		<calendar showType="month" v-if="currentTime" :endDate="currentTime" @changeCalendar="changeCalendar"
			:currentSchemeDayFinish="currentSchemeDayFinish" :diaryDates="diaryDates" @changeMonth="changeMonth" />
		<view class="diary-content w-full" v-if="createDiary">
			<view class="empty  flex-col">
				<image class="empty-icon" src="https://cbti.zhisongkeji.com/uniapp-static/diaryempty.png" mode="">
				</image>
				<view class="empty-text">
					今日暂未记录睡眠
				</view>
				<view class="empty-btn" @click="resetDiary()">
					<img src="https://cbti.zhisongkeji.com/uniapp-static/edit.png" alt="" />
					填写睡眠日记
				</view>
			</view>
		</view>
		<view class="diary-content w-full flex-col" v-else>
			<view class="analysis w-full flex-col">
				<view class="analysis-content flex-row">
					<view class="analysis-half flex-col">
						<view class="label">
							睡眠分析
							<span class="status">{{ detail.diary_mood }}</span>
						</view>
						<view class="part  bgcolor1">
							<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/rushuishijian.png"
								mode=""></image>
							<view class="part-content flex-col">
								<view class="plabel">
									入睡用时
								</view>
								<view class="pvalue">
									{{ detail.sleep_length  === '0分' ? '无' : detail.fall_asleep }}
								</view>
							</view>
						</view>
					</view>
					<view class="analysis-pie analysis-half">
						<Pie v-if="detail.score !== null" :point="detail.score" :efficiency="detail.sleep_efficiency" />
					</view>
				</view>
				<view class="analysis-content flex-row">
					<view class="part flex-col analysis-half bgcolor3">
						<view class="part-title flex-row">
							<view class="plabel flex-row">
								<image class="plabel-icon"
									src="https://cbti.zhisongkeji.com/uniapp-static/hospitalBed.png" mode=""></image>
								夜醒次数
							</view>
							<view class="pvalue">
								{{ detail.sleep_length  === '0分' ? '无' : `${detail.weakup_time}次` }}
							</view>
						</view>
						<view class="part-value">
							{{ detail.sleep_length  === '0分' ? '--' :  detail.weakup_time_label }}
						</view>
					</view>
					<view class="part flex-col analysis-half bgcolor4">
						<view class="part-title flex-row">
							<view class="plabel flex-row">
								<image class="plabel-icon"
									src="https://cbti.zhisongkeji.com/uniapp-static/sleepeffice.png" mode=""></image>
								睡眠效率
							</view>
							<view class="pvalue">
								{{ detail.sleep_efficiency }}%
							</view>
						</view>
						<view class="part-value">
							{{ detail.sleep_efficiency_label }}
						</view>
					</view>
					<view class="part onerow analysis-half bgcolor5">
						<view class="part-title flex-row">
							<view class="plabel flex-row">
								<image class="plabel-icon"
									src="https://cbti.zhisongkeji.com/uniapp-static/rushuishijian.png" mode=""></image>
								起床时感觉
							</view>
							<view class="pvalue">
								{{ detail.weakup_style }}
							</view>
						</view>
					</view>
					<view class="part  analysis-half bgcolor2">
						<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/whiteSleep.png"
							mode=""></image>
						<view class="part-content flex-col">
							<view class="plabel">
								日间小睡时间
							</view>
							<view class="pvalue">
								{{ detail.nap_length }}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="label recordlabel">
				睡眠记录
			</view>
			<view class="diary-process">
				<view class="diary-process-bg" :style="{ width: `${stages[stage].width}%;` }">

				</view>
				<view class="diary-process-stage" :style="{ marginLeft: `calc(${item.width}% - 64rpx)` }" :key="index"
					v-for="(item, index) of stages">
					<image class="sicon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${item.icon}`" mode="">
					</image>
					<view class="slabel">
						{{ item.label }}
					</view>
					<view class="stime">
						{{ item.time }}
					</view>
				</view>
			</view>
			<view class="tab">
				<view class="tab-item">
					<view class="tab-item-label">
						睡眠时长
					</view>
					<view class="tab-item-value">
						{{ detail.sleep_length }}
					</view>
				</view>
				<view class="tab-item">
					<view class="tab-item-label">
						卧床时长
					</view>
					<view class="tab-item-value">
						{{ detail.bedridden_length }}
					</view>
				</view>
			</view>
			<view class="label">
				小睡记录
			</view>
			<view class="records flex-col">
				<view class="recordempty" v-if="recordsleep.length == 0">
					无
				</view>
				<view class="record flex-row" v-else :key="index" v-for="(item, index) of recordsleep">
					<view class="record-duration">
						{{ item.duration }}
					</view>
					<view class="record-minute">
						{{ item.minute }}
					</view>
				</view>
			</view>
			<view class="label">
				服药记录
			</view>
			<view class="records flex-col">
				<view class="recordempty" v-if="recorddrug.length == 0">
					无
				</view>
				<view class="record flex-row" v-else :key="index" v-for="(item, index) of recorddrug">
					<view class="record-duration flex-row">
						<view class="time">
							{{ item.drugTime }}
						</view>
						<view class="drug">
							<view class="drug">
								<view>{{item.medicineName}}</view>
								<view class="drug-unit">({{item.commonName}}) {{item.medicineSpecification}}</view>
							</view>
						</view>
					</view>
					<view class="record-minute">
						{{ item.dosage }}
					</view>
				</view>
			</view>
			<view class="label">
				其他记录
			</view>
			<view class="tab othertab">
				<view class="tab-item">
					<view class="tab-item-label">
						睡眠感受
					</view>
					<view class="tab-item-value">
						{{ detail.diary_mood }}
					</view>
				</view>
				<view class="tab-item">
					<view class="tab-item-label">
						饮酒、喝茶和咖啡
					</view>
					<view class="tab-item-value">
						<view v-if="drinkRecord.length == 0">
							无
						</view>
						<view class="values flex-col" v-else>
							<view class="value flex-row" :key="index" v-for="(item, index) of drinkRecord">
								<view class="value-duration ">
									{{ item.drink }}
								</view>
								<view class="value-minute">
									{{ item.dosage }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="tab-item">
					<view class="tab-item-label">
						放松训练
					</view>
					<view class="tab-item-value">
						<view v-if="relaxRecord.length == 0">
							无
						</view>
						<view class="values flex-col" v-else>
							<view class="value flex-row" :key="index" v-for="(item, index) of relaxRecord">
								<view class="value-duration ">
									{{ item.duration }}
								</view>
								<view class="value-minute">
									{{ item.minute }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="tab-item">
					<view class="tab-item-label">
						有最困的时候？
					</view>
					<view class="tab-item-value">
						<view class="tab-item-value">
							<view v-if="triedRecord.length == 0">
								无
							</view>
							<view class="values flex-col" v-else>
								<view class="value flex-row" :key="index" v-for="(item, index) of triedRecord">
									<view class="value-tiredTime">
										{{ item.tiredTime }}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="editbtn" @click="resetDiary()">
				<img src="https://cbti.zhisongkeji.com/uniapp-static/edit.png" alt="" />
				重填
			</view>
		</view>

	</view>
</template>

<script>
	import {
		getSleepDiary,
		getSleepTime,
		getMedicineRecord,
		getDiarys
	} from '@/assets/api/index.js'
	import {
		formatDate,
		isObjEmpty
	} from '../../utils/index.js'
	import calendar from '@/components/calendar/calendar.vue'
	import Pie from '@/components/pie.vue'
	export default {
		components: {
			calendar,
			Pie
		},
		data() {
			return {
				diaryDates: [],
				currentSchemeDayFinish: [],
				recordsleep: [],
				recorddrug: [],
				triedRecord: [],
				drinkRecord: [],
				relaxRecord: [],
				currentTime: '',
				createDiary: true,
				detail: {},
				plan: [],
				stage: 3,
				stages: [{
					icon: 'bed.png',
					ingicon: 'beding.png',
					width: 10,
					label: '上床',
					time: '22:00'
				}, {
					icon: 'sleepIcon.png',
					ingicon: 'sleeping.png',
					width: 34.9,
					label: '入睡',
					time: '23:00'
				}, {
					icon: 'wakeIcon.png',
					ingicon: 'wakeing.png',
					width: 78.5,
					label: '醒来',
					time: '05:00'
				}, {
					icon: 'up.png',
					ingicon: 'uping.png',
					width: 100,
					label: '起床',
					time: '08:00'
				}],
				// showPie:false
			}
		},
		onLoad({
			time
		}) {
			this.currentTime = time || formatDate(new Date(), 'YYYY-MM-dd')
		},
		onShow() {
			this.initData()
		},
		computed: {
			createDiary() {
				return isObjEmpty(this.detail);
			}
		},
		methods: {
			initData() {
				// this.showPie = false
				// console.log('showPie',this.showPie)
				this.getSleepDiary()
				this.getDiarys()
			},
			getDiarys() {
				let params = this.getMonth()
				getDiarys(params).then(result => {
					if (result.total > 0) {
						this.diaryDates = result.records.map(item => item.recordTime);
					}
				})
			},
			changeMonth(monthfirst) {
				this.currentTime = monthfirst
				this.initData()
			},
			getMonth() {
				// 当前日期
				const currentDate = new Date(this.currentTime);

				// 获取当前月份的第一天
				const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

				// 获取当前月份的最后一天
				// 将日期设置为下个月的第一天，然后减去一天
				const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

				// 格式化日期为 YYYY-MM-DD 格式
				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = (date.getMonth() + 1).toString().padStart(2, '0');
					const day = date.getDate().toString().padStart(2, '0');
					return `${year}-${month}-${day}`;
				};
				return {
					startDate: formatDate(firstDay),
					endDate: formatDate(lastDay),
				}
			},
			getSleepDiary(date) {
				getSleepDiary({
					recordTime: this.currentTime
				}).then(result => {
					if (isObjEmpty(result)) {
						this.detail = {}
						this.recordsleep = []
						this.recorddrug = []
						this.triedRecord = []
						this.drinkRecord = []
						this.relaxRecord = []

						return
					}
					this.detail = result
					// this.showPie = true
					// console.log('getSleepDiary',this.showPie)
					console.log('detail', this.detail)
					let {
						willTime,
						doseTime,
						weakupTime,
						getupTime
					} = result

					this.relaxRecord = result.diary_train_list.map(item => {
						return {
							duration: formatDate(item.startDate, 'hh:mm') + '~' + formatDate(item.endDate,
								'hh:mm'),
							minute: item.totalNum + '分钟'
						}
					})
					this.drinkRecord = result.diary_drink_list.map(item => {
						return {
							drink: formatDate(item.useDate, 'hh:mm') + ' ' + item.drinkName,
							dosage: item.dosage
						}
					})
					this.triedRecord = result.diary_tired_list.map(item => {
						return {
							tiredTime: formatDate(item.tiredTime, 'hh:mm')
						}
					})
					this.recorddrug = result.diary_medicine_list.map(item => {
						return {
							...item,
							drugTime: formatDate(item.useDate, 'hh:mm')
						}
					})
					this.recordsleep = result.diary_nap_list.map(item => {
						return {
							duration: formatDate(item.startDate, 'hh:mm') + '~' + formatDate(item.endDate,
								'hh:mm'),
							minute: item.totalNum + '分钟'
						}
					})


					// willTime 上床 doseTime 入睡
					this.stages[0].time = formatDate(willTime, 'hh:mm')
					this.stages[1].time = doseTime ? formatDate(doseTime, 'hh:mm') : '--'
					this.stages[2].time = weakupTime ? formatDate(weakupTime, 'hh:mm') : '--'
					this.stages[3].time = formatDate(getupTime, 'hh:mm')
				})
			},
			changeCalendar(date) {
				this.currentTime = formatDate(date, 'YYYY-MM-dd')
				this.initData()
			},
			resetDiary() {
				// this.showPie = false
				uni.navigateTo({
					url: '/pages/diaryCreate/diary?time=' + this.currentTime
				})
			}
		}
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>