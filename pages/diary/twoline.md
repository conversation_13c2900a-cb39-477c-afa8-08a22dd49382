const x = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
const y1 = [100, 100, 100, 200, 300, 400, 290, 380, 800, 100, 20, 30]
const y2 = [10, 50, 100, 20, 800, 40, 290, 190, 80, 0, 200, 730]
option = {
   //  grid: {
   //    top: 50,
   //    left: 30,
   //    right: 10,
   //    bottom: 50
   //  },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(81, 127, 253, 0.1)'
        },
        label: {
          show: false,
          color: '#517FFD',
          backgroundColor: 'transparent',
          fontSize: 12
        }
      },
      className: 'my-tooltip-box',
      formatter: function (params) {
        var str = '<div class="my-tooltip">'
        params.forEach((el) => {
          if (el.seriesName !== 'maxdata') {
            str += `<div><span class="name large">${el.seriesName}年${el.axisValue}月：</span><span class="value">${el.value} 亿元</span></div>`
          }
        })
        str += '</div>'
        return str
      }
    },
    legend: {
      data: ['卧床时长', '睡眠时长'],
      top: '0%',
      clickable: false,
      selectedMode: false,
      itemWidth: 20,
      itemHeight: 2,
        icon: 'rect',
      textStyle: {
        color: 'rgba(107, 107, 107, 1)',
        fontSize: 16
      }
    },
    xAxis: {
      data: x,
      axisLine: {
        show: true, // 隐藏X轴轴线
        lineStyle: {
          color: 'rgba(221, 221, 221, 1)',
          width: 2
        }
      },
      axisTick: {
        show: false // 隐藏X轴刻度
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(107, 107, 107, 1)', // X轴文字颜色
          fontSize: 12
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        nameTextStyle: {
          color: 'rgba(107, 107, 107, 1)',
          padding: [0, 0, 0, 0],
          fontSize: 12
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false,
          textStyle: {
            color: 'rgba(107, 107, 107, 1)',
            fontSize: 12
          }
        }
      }
    ],
    series: [
      {
        name:'卧床时长',
        type: 'line',
        itemStyle: {
          normal: {
            color: '#FF9F0A',
            lineStyle: {
              color: '#FF9F0A',
              width: 4
            },
            areaStyle: {
              color: '#fff'
            }
          }
        },
        data: y1
      },
      {
        name: '睡眠时长',
        type: 'line',
        symbolSize: 6,
        itemStyle: {
          normal: {
            color: '#32ADE6',
            lineStyle: {
              color: '#32ADE6',
              width: 4
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: '#FFFFFF'
                },
                {
                  offset: 1,
                  color: '#CCEFFF'
                }
              ])
            }
          }
        },
        data: y2
      }
    ]
  }
