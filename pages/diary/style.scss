.diary-pages {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0 32rpx;
	overflow-x: hidden;

	.diary-content {
		padding-bottom: 100rpx;
		.empty {
			align-items: center;
			border-top: 1rpx solid #e5e5ea;
			padding-top: 32rpx;
			&-icon {
				width: 96rpx;
				height: 96rpx;
				margin: 64rpx 0 16rpx;
			}
			&-text {
				font-weight: 400;
				font-size: 24rpx;
				color: #b3b3d8;
				margin-bottom: 36rpx;
			}
			&-btn {
				width: 264rpx;
				height: 64rpx;
				min-height: 64rpx;
				background: #ffffff;
				border-radius: 32rpx;
				border: 2rpx solid #d1d1d6;
				> image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 5rpx;
				}
				font-weight: 400;
				font-size: 24rpx;
				color: #636366;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.recordlabel {
			padding-top: 16upx;
		}
		.label {
			font-weight: bold;
			font-size: 32rpx;
			color: #1c1c1e;
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			.status {
				font-size: 28rpx;
				color: #5e5ce6;
				margin-left: 8rpx;
			}
		}
		.analysis {
			padding-top: 32rpx;
			border-top: 1rpx solid #e5e5ea;
			border-bottom: 1rpx solid #e5e5ea;
			&-content {
				flex: 1;
				flex-wrap: wrap;
				justify-content: space-between;
				.analysis-half {
					width: calc(50% - 11upx);
				}
				.bgcolor1 {
					background: #f3f4ff;
					.part-title {
						.pvalue {
							color: #445df0;
						}
					}
				}
				.bgcolor2 {
					background: #fbf7f3;
					.part-title {
						.pvalue {
							color: #ff9604;
						}
					}
				}
				.bgcolor3 {
					background: #f3fbf7;
					.part-title {
						.pvalue {
							color: #31afaa;
						}
					}
				}
				.bgcolor4 {
					background: #ffefef;
					.part-title {
						.pvalue {
							color: '#FF6570';
						}
					}
				}
				.bgcolor5 {
					background: #eaf4ff;
					.part-title {
						.pvalue {
							color: '#76A0FF';
						}
					}
				}
				.onerow {
					display: flex;
					align-items: center;
				}
				.part {
					margin-bottom: 18rpx;
					border-radius: 28rpx;
					padding: 24rpx 20rpx;
					height: 152upx;
					// justify-content: space-between;
					display: flex;
					&-icon {
						width: 41rpx;
						height: 41rpx;
						min-height: 41rpx;
						margin-right: 5upx;
					}
					&-content {
						flex: 1;
						width: 100%;
						font-weight: bold;
						font-size: 28rpx;
						color: #2c2c2e;
						line-height: 60rpx;
						.plabel {
							font-size: 24upx;
							color: #3b4552;
							flex: 1;

							&-icon {
								width: 41rpx;
								height: 41rpx;
								margin-right: 5upx;
							}
						}
						.pvalue {
							font-size: 34upx;
						}
					}

					&-title {
						width: 100%;
						font-weight: bold;
						font-size: 28rpx;
						color: #2c2c2e;
						line-height: 60rpx;
						.plabel {
							font-size: 24upx;
							color: #3b4552;
							flex: 1;

							&-icon {
								width: 41rpx;
								height: 41rpx;
								margin-right: 5upx;
							}
						}
						.pvalue {
							font-size: 34upx;
						}
					}
					&-value {
						flex: 1;
						font-size: 18rpx;
						color: #687587;
						line-height: 24rpx;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: normal;
					}
				}
			}
			&-pie {
				height: 100%;
				display: flex;
				justify-content: center;
			}
		}

		.diary-process {
			width: 100%;
			height: 64rpx;
			background: rgba(235, 235, 245, 0.6);
			border-radius: 32rpx;
			display: flex;
			position: relative;
			margin-top: 60rpx;
			&-bg {
				height: 64rpx;
				border-radius: 32rpx;
				background: linear-gradient(270deg, #fff8d3 0%, rgba(255, 248, 211, 0) 100%);
			}
			&-stage {
				width: 100%;
				height: 64rpx;
				position: absolute;
				border-radius: 32rpx;
				top: 0;
				left: 0;
				.sicon {
					width: 64rpx;
					height: 64rpx;
				}
				.slabel {
					position: absolute;
					bottom: -50rpx;
					width: 64rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #2c2c2e;
					text-align: center;
				}
				.stime {
					position: absolute;
					top: -50rpx;
					width: 64rpx;
					font-weight: 400;
					font-size: 20rpx;
					color: #8e8e93;
					text-align: center;
				}
			}
		}

		.othertab {
			margin-top: 0 !important;
		}
		.tab {
			width: 100%;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-column-gap: 24rpx;
			grid-row-gap: 24rpx;
			margin: 72rpx 0 32rpx;
			&-item {
				width: 100%;
				min-height: 104rpx;
				background: rgba(235, 235, 245, 0.3);
				border-radius: 16rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 24rpx;
				&-label {
					font-weight: 400;
					font-size: 24rpx;
					color: #8e8e93;
					padding-bottom: 8rpx;
				}
				&-value {
					width: 100%;
					font-weight: bold;
					font-size: 28rpx;
					color: #3a3a3c;
					text-align: center;

					.values {
						width: 100%;
						.value {
							margin-bottom: 16rpx;
							display: flex;
							justify-content: space-around;
							&-duration {
								flex: 1;
								text-align: left;
							}
							&-tiredTime {
								flex: 1;
							}
						}
						.value:last-child {
							margin-bottom: 0;
						}
					}
				}
			}
		}
		.records {
			width: 100%;
			background: rgba(235, 235, 245, 0.3);
			border-radius: 24rpx;
			padding: 0 32rpx;
			margin-bottom: 40rpx;
			.recordempty {
				width: 100%;
				height: 88rpx;
				border-bottom: 1rpx solid #e5e5ea;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 700;
				font-size: 28rpx;
				color: #3a3a3c;
			}
			.record {
				width: 100%;
				height: 88rpx;
				border-bottom: 1rpx solid #e5e5ea;
				&-duration {
					flex: 1;
					font-weight: bold;
					font-size: 28rpx;
					color: #2c2c2e;
					align-items: center;

					.drug {
						font-weight: 400;
						font-size: 28rpx;
						color: #000000;
						padding: 0 16rpx;
						&-unit {
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;
						}
					}
				}
				&-minute {
					font-weight: 400;
					font-size: 24rpx;
					color: #000000;
				}
			}
			.record:last-child {
				border: none;
			}
		}
		.editbtn {
			width: 160rpx;
			height: 64rpx;
			min-height: 64rpx;
			background: #ffffff;
			border-radius: 32rpx;
			border: 2rpx solid #d1d1d6;
			> image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 5rpx;
			}
			font-weight: 400;
			font-size: 24rpx;
			color: #636366;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto;
		}
	}
}
