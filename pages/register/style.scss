.list {
	width: 100%;
	height: 100vh;
	&-item {
		width: calc(100% - 80rpx);
		height: 108rpx;
		border-bottom: 1rpx solid #e5e5ea;
		display: flex;
		align-items: center;
		margin: 0 auto;

		&-label {
			flex: 1;
			font-weight: 400;
			font-size: 28rpx;
			color: #1c1c1e;
			>label{
				color: red;
			}
		}
		&-info {
			font-weight: 400;
			font-size: 28rpx;
			color: #1c1c1e;
			margin-right: 16rpx;
			display: flex;
			> input {
				flex: 1;
				text-align: right;
			}
			.phone{
				height: 44upx;
				background: #E1E0FF;
				border-radius: 8upx;
				border: 1px solid #5F5BE4;
				font-weight: 700;
				font-size: 24upx;
				color: #5F5BE4;
				line-height: 44upx;
				padding: 0 5upx;
			}
			button {
				-webkit-tap-highlight-color: transparent;
				background-color: transparent;
				box-sizing: border-box;
				color: #fff;
				cursor: pointer;
				display: block;
				font-size: 18px;
				line-height: 2.55555556;
				margin-left: 0;
				margin-right: 0;
				overflow: hidden;
				padding-left: 0;
				padding-right: 0;
				position: relative;
				text-align: center;
				text-decoration: none;
			}
			button::after {
				border: none;
			}
		}
		.place {
			font-weight: 400;
			font-size: 28rpx;
			color: #aeaeb2;
		}
		&-next {
			width: 24rpx;
			height: 24rpx;
		}
	}

	.submit {
		width: calc(100% - 80rpx);
		height: 80rpx;
		background: #5e5ce6;
		border-radius: 24rpx;
		font-weight: 600;
		font-size: 32rpx;
		color: #ffffff;
		line-height: 80rpx;
		text-align: center;
		margin: 48rpx auto;
	}
	// .loginButton {
	// 	display: flex;
	// 	width: 100%;
	// 	height: 40px;
	// 	background: #375802;
	// 	border-radius: 20px;
	// 	align-items: center;
	// 	justify-content: center;
	// 	font-size: 14px;
	// 	font-family: PingFangSC-Regular, PingFang SC;
	// 	font-weight: 400;
	// 	color: #5e5ce6;
	// 	line-height: 20px;
	// 	margin-top: 39px;
	// }
	.agreementBottom {
		width: calc(100% - 80rpx);
		display: flex;
		align-items: center;
		font-size: 13px;
		font-weight: 400;
		font-size: 24rpx;
		color: #5e5ce6;
		line-height: 24rpx;
		margin: 0 auto;
		button {
			-webkit-tap-highlight-color: transparent;
			background-color: transparent;
			box-sizing: border-box;
			color: #fff;
			cursor: pointer;
			display: block;
			font-size: 18px;
			line-height: 2.55555556;
			margin-left: 0;
			margin-right: 0;
			overflow: hidden;
			padding-left: 0;
			padding-right: 0;
			position: relative;
			text-align: center;
			text-decoration: none;
		}
		button::after {
			border: none;
		}

	}
}
