<template>
	<view class="list">
		<view class="list-item flex-row" :key="index" v-for="(item,index) of list">
			<view class="list-item-label">
				{{item.label}}
				<label class="required" v-if="item.required">
					*
				</label>
			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')" v-if="item.type == 'date'">
				<picker mode="date" :value="dataForm[item.modelName]" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<view class="uni-input">{{dataForm[item.modelName] ? dataForm[item.modelName] :item.place}}</view>
				</picker>

			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')"
				v-else-if="item.type == 'picker'">
				<picker mode="selector" :value="dataForm[item.modelName]"
					@change="(e)=>{changePicker(e,item.modelName)}" :range="dicCodeHash[item.modelName]"
					range-key="title">
					<view class="uni-input">
						{{dataForm[item.modelName] ? dicCodeLabelHash[item.modelName][dataForm[item.modelName]] :item.place}}
					</view>
				</picker>

			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')"
				v-else-if="item.type == 'phone'">
				<view v-if="dataForm.telphone" class="input">
					{{dataForm.telphone}}
				</view>
				<button class="phone" v-else-if="checked" open-type="getPhoneNumber"
					@getphonenumber="decryptPhoneNumber">授权获取手机号</button>
				<view class="phone" v-else @click="pop()">
					授权获取手机号
				</view>
			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')" v-else-if="item.type == 'text'">
				<input type="text" v-model="dataForm[item.modelName]" :placeholder="item.place" />
			</view>
			<image v-if="item.showIcon" class="list-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png"
				mode=""></image>
		</view>
		<view class="agreementBottom">
			<button open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="agreePrivacy">
				<radio style="transform: scale(0.7);" color="#5e5ce6" value="r1" :checked="checked" />
			</button>
			勾选代表已同意
			<label>《用户服务协议》</label>
		</view>
		<view class="submit" @click="submit()">完成</view>
	</view>
</template>

<script>
	import {
		login,
		loginMobile,
		getUser,
		getDictCode,
		getPhone
	} from '@/assets/api/index.js'
	import {
		useStore
	} from 'vuex'

	export default {
		data() {
			const store = useStore();
			return {
				doctorId: uni.getStorageSync('doctorId') || "",
				dicCodeHash: {
					sex: uni.getStorageSync('sex'),
					cultural: uni.getStorageSync('cultural'),
					maritalStatus: uni.getStorageSync('maritalStatus')
				},
				dicCodeLabelHash: {
					sex: uni.getStorageSync('dicsexMap'),
					cultural: uni.getStorageSync('dicculturalMap'),
					maritalStatus: uni.getStorageSync('dicmaritalStatusMap')
				},
				dataForm: {
					name: '',
					telphone: '',
					sex: '',
					birthday: '',
					profession: '',
					cultural: '',
					maritalStatus: ''
				},
				list: [{
					label: '姓名',
					modelName: 'name',
					showIcon: false,
					type: 'text',
					place: '请输入',
					required: true,

				}, {
					label: '电话',
					modelName: 'telphone',
					showIcon: false,
					type: 'phone',
					place: '请输入',
					required: true,
				}, {
					label: '性别',
					modelName: 'sex', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择',
					required: true,
				}, {
					label: '生日',
					modelName: 'birthday',
					showIcon: true,
					type: 'date',
					place: '请选择',
					required: true,

				}, {
					label: '职业',
					modelName: 'profession',
					showIcon: false,
					type: 'text',
					place: '请输入',
				}, {
					label: '文化程度',
					modelName: 'cultural', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择'
				}, {
					label: '婚姻状况',
					modelName: 'maritalStatus', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择'
				}],
				rule: [{
						modelName: 'name',
						value: '请输入姓名'
					},
					{
						modelName: 'telphone',
						value: '请输入电话'
					},
					{
						modelName: 'sex',
						value: '请选择性别'
					}, {
						modelName: 'birthday',
						value: '请选择生日'
					}
				],
				openId: '',
				unionId: '',
				isBind: false,
				tenantId: null,
				token: store.state.token,
				checked: false,
			}
		},
		onLoad(option) {
			let url = decodeURIComponent(option.q)
			let doctorId = this.getParameterByName('doctorId', url)
			console.log('url',url)
			console.log('doctorId',doctorId)
			if (this.token) {
				this.getUserInfo()
			} else {
				if (doctorId || uni.getStorageSync('doctorId')) {
					this.doctorId = doctorId
					uni.setStorageSync('doctorId', doctorId)
				}
				this.initData()
			}
		},
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			 getParameterByName(name, url) {
			  if (!url) {
			    url = window.location.href;
			  }
			  name = name.replace(/[\[\]]/g, '\\$&');
			  var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
			      results = regex.exec(url);
			  if (!results) return null;
			  if (!results[2]) return '';
			  return decodeURIComponent(results[2].replace(/\+/g, ' '));
			},
			initData() {
				this.initDicCode()

			},
			initDicCode() {
				this.getDicCode('sex') //性别
				this.getDicCode('cultural') //文化
				this.getDicCode('marital_status') //婚姻状况 
			},
			getDicCode(dicCode) {
				getDictCode(dicCode).then(result => {
					if (dicCode == 'marital_status') {
						dicCode = 'maritalStatus'
					}
					uni.setStorageSync(dicCode, result)

					this.dicCodeHash[dicCode] = result
					let dicMap = {}
					for (let item of result) {
						dicMap[item.value] = item.title
					}
					//dicSexMap
					uni.setStorageSync(`dic${dicCode}Map`, dicMap)
					this.dicCodeLabelHash[dicCode] = dicMap

				})
			},
			//对象取并集
			unionOfObjects(obj1, obj2) {
				const set1 = new Set(Object.keys(obj1));
				const set2 = new Set(Object.keys(obj2));
				const union = new Set([...set1, ...set2]);
				const result = {};
				for (let key of union) {
					result[key] = obj1[key] || obj2[key];
				}
				return result;
			},
			pop() {
				uni.showModal({
					title: '提示',
					content: '请阅读并勾选用户服务协议',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			bindDateChange(e) {
				this.dataForm.birthday = e.detail.value
			},
			changePicker(e, modelName) {
				let index = e.detail.value
				this.dataForm[modelName] = this.dicCodeHash[modelName][index].value
			},
			agreePrivacy(e) {
				this.checked = !this.checked
			},
			submit() {
				let isChecked = true
				let dataForm = Object.assign({}, this.dataForm)
				for (let i = 0; i < this.rule.length; i++) {
					let ruleItem = this.rule[i]
					if (!dataForm[ruleItem.modelName]) {
						uni.showToast({
							title: ruleItem.value,
							duration: 2000,
							icon: 'none'
						});
						isChecked = false
						return;
					}
				}
				if (isChecked) {
					if (this.isBind) { // 已绑定手机号
						this.$store.commit('setToken', this.token)
						this.$store.commit('setTenantId', this.tenantId)
						
						uni.setStorageSync('token', this.token)
						uni.setStorageSync('tenantId', this.tenantId)
						this.getUserInfo()
						//绑定成功后,清除医生id
						uni.removeStorageSync('doctorId')
					} else {
						let phoneparams = {
							code: this.dataForm.telphone,
							openId: this.openId,
							unionId: this.unionId,
						}
						//存在doctorId
						this.doctorId ? phoneparams.doctorId = this.doctorId : ''
						phoneparams = this.unionOfObjects(phoneparams, dataForm)

						loginMobile(phoneparams).then(result => {
							if (result.isBind) { // 已绑定手机号
								this.$store.commit('setToken', result.token)
								this.$store.commit('setTenantId', result.tenantId)
								uni.setStorageSync('token', result.token)
								uni.setStorageSync('tenantId', result.tenantId)
								this.getUserInfo()
								//绑定成功后,清除医生id
								uni.removeStorageSync('doctorId')
							}

						})

					}

				}
			},
			decryptPhoneNumber(e) {
				wx.login({
					success: res => {
						let {
							code
						} = res
						login({
							code: code
						}).then(result => {

							let {
								openid,
								unionid
							} = result.wxSessionInfo
							this.openId = openid
							this.unionId = unionid
							this.isBind = result.isBind //// 已绑定手机号
							this.tenantId = result.tenantId
							this.token = result.token
							if (result.isBind) {
								this.dataForm.telphone = result.phone
							} else {
								getPhone({
									code: e.detail.code
								}).then(result => {
									this.dataForm.telphone = result.phone
								})
							}
						})
					}
				})

			},
			getUserInfo() {
				getUser().then(result => {
					uni.setStorageSync('user', result)
					this.$store.commit('setUser', result)
					uni.reLaunch({
						url: '/pages/index/index'
					})
				})
			},
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>