<template>
	<view class="myFeeling-pages">
		<view class="task-tab flex-row">
			<view :class="'task-tab-item ' + (taskCurrent == index ? 'currenttab' : '')" :key="index"
				v-for="(item, index) of tasktabs" @click="changeTab(index)">
				{{ item }}
			</view>
		</view>
		<scroll-view scroll-y="true" class="myFeeling" @scrollend="scroll()" v-if="list.length > 0">
			<view class="myFeeling-item" :key="index" v-for="(item, index) of list" @click="goInfo(item.id)">
				<image v-if="taskCurrent == 1" class="myFeeling-item-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${feelIcon[item.icon]}.png`" mode=""></image>
				<view class="myFeeling-item-label">
					<view class="label">
						{{ item.label }}
					</view>
					<view class="date">
						{{ item.date }}
					</view>
				</view>
				<image class="myFeeling-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
				</image>
			</view>
			<view class="end" v-if="end">
				-已经到底了-
			</view>
		</scroll-view>
		<Empty v-else />
	</view>
</template>

<script>
	import {
		getWorriedList,
		getFeelingList
	} from '@/assets/api/index.js'
	import Empty from '@/components/empty.vue'
	export default {
		components: {
			Empty
		},
		data() {
			return {
				tasktabs: ['忧虑记录', '心情记录'],
				taskCurrent: 0,
				list: [],
				// {
				// 	id: 1,
				// 	icon: 'hcing',
				// 	label: '忧虑的忧虑',
				// 	date: '2024-08-20 20:22:22'
				// }
				pageNo: 1,
				end: false,
				feelIcon: {
					'生气': 'hcing',
					'不开心': 'bthing',
					'平淡': 'ybing',
					'开心': 'hbcing',
					'兴奋': 'fching',
				}
			}
		},
		onShow() {
			this.getWorried()
		},
		methods: {
			changeTab(index) {
				this.taskCurrent = index
				this.end = false
				this.pageNo = 1
				if (index == 0) {
					this.getWorried()
				} else {
					this.getFeeling()
				}
			},
			goInfo(id) {
				const page = this.taskCurrent == 0 ? '/pages/addworried/addworried?id=' : '/pages/feeling/feeling?id='
				uni.navigateTo({
					url: page + id
				})
			},
			scroll() {
				this.taskCurrent == 0 ? this.getWorried() : this.getFeeling()
			},
			getFeeling() { //医嘱任务
				getFeelingList({
					pageNo: this.pageNo,
					pageSize: 50,
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records.map(item => {
						return {
							id: item.id,
							icon: item.moodType,
							label: item.reason,
							date: item.recordTime
						}
					})

					if (this.pageNo == 1) {
						this.list = list
						if (0 < total && total <= 50) {
							this.end = true
						}
					} else {
						if (this.pageNo < Math.ceil(total / 50) + 1) {
							this.list = [].concat(this.list, list)
						} else {
							this.end = true
						}
					}
					this.pageNo++
				})
			},
			getWorried() { //医嘱任务
				getWorriedList({
					pageNo: this.pageNo,
					pageSize: 50,
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records.map(item => {
						return {
							id: item.id,
							icon: 'hcing',
							label: item.content,
							date: item.recordTime
						}
					})

					if (this.pageNo == 1) {
						this.list = list
						if (total <= 50) {
							this.end = true
						}
					} else {
						if (this.pageNo < Math.ceil(total / 50) + 1) {
							this.list = [].concat(this.list, list)
						} else {
							this.end = true
						}
					}
					this.pageNo++
				})
			},

		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>