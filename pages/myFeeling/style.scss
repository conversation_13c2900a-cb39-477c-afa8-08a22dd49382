.myFeeling-pages {
	padding: 0 32rpx;
	.task-tab {
		height: 108rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #1c1c1e;

		&-item {
			margin-right: 40rpx;
		}
		.currenttab {
			font-weight: bold;
			font-size: 32rpx;
			color: #5e5ce6;
			position: relative;
		}
		.currenttab::after {
			content: '';
			position: absolute;
			width: 40rpx;
			height: 6rpx;
			background: #5e5ce6;
			border-radius: 4rpx;
			bottom: -8px;
			left: calc(50% - 20rpx);
		}
	}
	.myFeeling{
		width: 100%;
		height: calc(100vh - 108rpx);
		&-item{
			width: 100%;
			min-height: 120rpx;
			border-bottom: 1rpx solid #E5E5EA;
			display: flex;
			align-items: center;
			padding: 24rpx 0;
			&-icon{
				width: 64rpx;
				height: 64rpx;
				margin:0 20rpx;
			}
			&-label{
				flex: 1;
			
				display: flex;
				flex-direction: column;
				.label{
					font-weight: 400;
					font-size: 28rpx;
					color: #1C1C1E;
					margin-bottom: 16rpx;
				}
				.date{
					font-weight: 400;
					font-size: 24rpx;
					color: #8E8E93;
				}
			}
			&-next{
				width: 24rpx;
				height: 24rpx;
			}
		}
	}
}
