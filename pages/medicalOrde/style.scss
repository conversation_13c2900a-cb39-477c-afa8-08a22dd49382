.medicalOrde-pages {
  padding: 0 32rpx;
  .medicalOrde {
    width: 100%;
    height: 100vh;
  .taskitem {
  	padding: 24rpx;
  	background: rgba(235, 235, 245, 0.3);
  	border-radius: 24rpx;
  	margin-bottom: 16rpx;
  	&-time {
  		display: flex;
  		align-items: center;		
  		font-weight: 400;
  		font-size: 24rpx;
  		margin-bottom: 16rpx;
  		.tlabel{
  			flex: 1;
  			display: flex;
  			align-items: center;
  			font-weight: 400;
  			font-size: 24rpx;
  			.iconImg{
  				width: 25rpx;
  			}
  		}
  		.tstatus{
  			width: 107rpx;
  			height: 28rpx;
  		}
  	}
  	.unfinish {
  		.tlabel {
  			color: #ff9500;
  		}
  	}
  	.finish {
  		.tlabel {
  			color: #00c826;
  		}
  	}
  	&-label {
  		font-weight: bold;
  		font-size: 28rpx;
  		color: #1c1c1e;
  		margin-bottom: 8rpx;
  		display: flex;
  		align-items: center;
  	}
  	&-value {
  		font-size: 24rpx;
  		color: #636366;
  		line-height: 32rpx;
  		margin-bottom: 16rpx;
  	}
  	&-btn {
  		justify-content: flex-end;
  		.finish {
  			color: #ffffff;
  			background: #5e5ce6;
  			margin-left: 24rpx;
  		}
  	}
  }
  
  }
}
