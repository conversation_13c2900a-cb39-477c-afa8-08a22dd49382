<template>
	<view class="medicalOrde-pages">
		<scroll-view scroll-y="true" class="medicalOrde flex-col" @scrollend="scroll()">
			<view class="taskitem flex-col" :key="item.id" v-for="item of medicalOrdemasks">
				<view :class="'taskitem-time ' + (item.status == 0 ? 'unfinish' : 'finish')">
					<view class="tlabel">
						<image v-if="item.status == 0" class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
						</image>
						<image v-else class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png" mode="widthFix">
						</image>
						请在{{item.limitDate}}之前完成
					</view>
					<image v-if="item.status == 0" class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
					</image>
					<image v-else class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png" mode="widthFix">
					</image>
				</view>
				<view class="taskitem-label">
					{{ item.label }}
				</view>
				<view class="taskitem-value">
					{{ item.showAll ? item.valueAll : item.value }}
				</view>
				<view class="taskitem-btn flex-row">
					<view class="w-btn expand" v-if="item.value" @click="item.showAll = !item.showAll ">
						{{ item.showAll ? '收起' : '展开全部' }}
					</view>
					<view class="w-btn finish" @click="finishMedicalOrder(item.id)" v-if="item.status == 0">
						我已完成
					</view>
				</view>
			</view>

			<view class="end" v-if="end">
				-已经到底了-
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getMedicalOrderTask,
		finishMedicalOrderTask
	} from '@/assets/api/index.js'
	export default {
		data() {
			return {
				medicalOrdemasks: [],
				pageNo: 1,
				end: false
			}
		},
		onLoad() {
			this.getMedicalOrder()
		},
		methods: {
			scroll() {
				this.getMedicalOrder()
			},
			finishMedicalOrder(id) {
				finishMedicalOrderTask({
					'id': id
				}).then(result => {
					this.pageNo = 1
					this.getMedicalOrder()
				})
			},
			getMedicalOrder() { //医嘱任务
				getMedicalOrderTask({
					pageNo: this.pageNo,
					pageSize: 50,
					status: 0
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records.map(item => {
						let value = ''
						let valueAll = item.content
						if (item.content.length > 50) {
							value = item.content.slice(0, 50) + '...'
						}
						return {
							id: item.id,
							label: item.title,
							value: value,
							valueAll: item.content,
							showAll: value.length > 0 ? false : true,
							limitDate: item.limitDate,
							status: item.status
						}
					})

					if (this.pageNo == 1) {
						this.medicalOrdemasks = list
						if (total <= 50) {
							this.end = true
						}
					} else {
						if (this.pageNo < Math.ceil(total / 50) + 1) {
							this.medicalOrdemasks = [].concat(this.medicalOrdemasks, list)
						} else {
							this.end = true
						}
					}
					this.pageNo++
				})
			},

		}
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>