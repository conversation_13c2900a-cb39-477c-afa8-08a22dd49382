<template>
    <view class="password-page">
        <view class="input">
            <input class="uni-input" v-model="password" name="password" placeholder="请设置新密码"/>
            <input class="uni-input" placeholder="再次输入新密码"/>
        </view>
        <view class="submit" @click="submit">
            确定
        </view>
    </view>
</template>

<script>
	import {
	editPassword
	} from '@/assets/api/index.js'
    export default {
        data() {
            return {
                password: '',
            }
        },
        methods: {
            submit() {
                editPassword({
                    password: this.password
                }).then(result => {
                    uni.navigateBack({
                        delta: 1
                    })
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "./style.scss";
</style>
