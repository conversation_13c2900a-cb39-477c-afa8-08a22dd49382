.diary-page {
  // padding: 0 64rpx 180rpx;
  // padding: 0 0  180rpx;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  .diary-container {
    height: calc(100vh - 220rpx);
    overflow: hidden;
    .scrollview {
      height: 100%;
    }
  }
  .diary-changebtn {
    // position: fixed;
    // bottom: 0;
    // padding-bottom: 120rpx;
    width: 100%;
    height: 220rpx;
    display: flex;
    align-items: flex-start;
    justify-content: center;
	padding-top: 10rpx;
    > image {
      width: 112rpx;
      height: 112rpx;
    }
    > label {
      padding: 0 48rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #636366;
    }
  }
 .sleepyPop {
    width: 100%;
    height: 100vh;
    position: fixed;
    bottom: 0;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.56);
    display: flex;
    flex-direction: column;
    &-bg {
      flex: 1;
    }
    .timeDuration {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 24rpx 24rpx 0px 0px;
      padding: 0 20rpx;
      &-label {
        width: 100%;
        height: 104rpx;
        display: flex;
        align-items: center;

        > label {
          flex: 1;
          font-weight: bold;
          font-size: 32rpx;
          color: #1c1c1e;
          text-align: center;
        }
      }
      &-content {
        width: 100%;
        height: 268rpx;
        display: flex;
        justify-content: space-between;
        .timePicker {
          width: 100%;
          height: 268rpx;
		  .item{
			  text-align: center;
		  }
        }
      }
      &-btn {
        width: 300rpx;
        height: 64rpx;
        background: #ffffff;
        border-radius: 32rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #fff;
        line-height: 64rpx;
        text-align: center;
        background-color: #5e5ce6;
        margin: 50rpx 0 100rpx;
      }
    }
  }
  
  .pop {
    width: 100%;
    height: 100vh;
    position: fixed;
    bottom: 0;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.56);
    display: flex;
    flex-direction: column;
    &-bg {
      flex: 1;
    }
    .timeDuration {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 24rpx 24rpx 0px 0px;
      padding: 0 20rpx;
      &-label {
        width: 100%;
        height: 104rpx;
        display: flex;
        align-items: center;

        > label {
          flex: 1;
          font-weight: bold;
          font-size: 32rpx;
          color: #1c1c1e;
          text-align: center;
        }
      }
      &-content {
        width: 100%;
        height: 268rpx;
        display: flex;
        justify-content: space-between;
        .timePicker {
          width: calc(50% - 10rpx);
          height: 268rpx;
        }
      }
      &-btn {
        width: 300rpx;
        height: 64rpx;
        background: #ffffff;
        border-radius: 32rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #fff;
        line-height: 64rpx;
        text-align: center;
        background-color: #5e5ce6;
        margin: 50rpx 0 100rpx;
      }
    }
  }
}
.diary-page-padding {
  padding: 0 64rpx;
}
