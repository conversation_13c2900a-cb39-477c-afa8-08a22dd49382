.diary-title {
	font-weight: bold;
	font-size: 32rpx;
	color: #1c1c1e;
	margin: 48rpx 0;
	text-align: center;
}

.diary-process {
	width: 100%;
	height: 64rpx;
	background: rgba(235, 235, 245, 0.6);
	border-radius: 32rpx;
	display: flex;
	position: relative;
	&-bg {
		height: 64rpx;
		border-radius: 32rpx;
		background: linear-gradient(270deg, #fff8d3 0%, rgba(255, 248, 211, 0) 100%);
	}
	&-stage {
		width: 100%;
		height: 64rpx;
		position: absolute;
		border-radius: 32rpx;
		top: 0;
		left: 0;
		.icon {
			width: 64rpx;
			height: 64rpx;
		}
		.label {
			position: absolute;
			bottom: -50rpx;
			width: 64rpx;
			font-weight: 400;
			font-size: 20rpx;
			color: #8e8e93;
			text-align: center;
		}
		.time {
			position: absolute;
			top: -40rpx;
			width: 64rpx;
			font-weight: 400;
			font-size: 20rpx;
			color: #8e8e93;
			text-align: center;
		}
	}
	.currentstage {
		.label {
			font-weight: bold;
			font-size: 22rpx;
			color: #5e5ce6;
		}
	}
	.stage {
		position: absolute;
	}
	.stage:after {
		content: '';
		display: block;
		position: absolute;
		bottom: -78rpx;
		left: 8rpx;
		border-left: 24rpx solid transparent;
		border-right: 24rpx solid transparent;
		border-bottom: 20rpx solid rgba(235, 235, 245, 0.3);
	}
}
.diary-time {
	margin-top: 78rpx;
	height: 568rpx;
	background: rgba(235, 235, 245, 0.3);
	border-radius: 6px;
	padding: 32rpx;
	align-items: center;

	&-label {
		font-weight: bold;
		font-size: 28rpx;
		color: #000000;
	}
	&-picker {
		width: 100%;
		height: 308rpx;
		margin-top: 20rpx;
		// .column0{
		// 	background-color: red;
		// 	text-align: center;
		// }
	}
	.indicator {
		height: 80rpx;
	}
	.item {
		height: 80rpx;
		text-align: center;
		font-weight: bold;
		font-size: 28rpx;
		color: #000000;
		line-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

}

	.tips{
		font-size: 14px;
		color: #5E5CE6;
		margin: 64rpx auto;
		display: block;
		text-align: center;
	}