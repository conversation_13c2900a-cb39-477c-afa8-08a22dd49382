<template>
	<view class="diary-title">
		回顾你的作息时间
	</view>
	<view class="diary-process">
		<view class="diary-process-bg" :style="{width:`${stages[stage].width}%;`}">

		</view>
		<view :class="'diary-process-stage ' + (index <= stage ? 'currentstage ':'')   + (index == stage ? 'stage':'')"
			:style="{marginLeft:`calc(${item.width}% - 64rpx)`}" :key="index" v-for="(item,index) of stages">
			<view class="time">
				{{dataForm[item.modlName] ?  formatDate(dataForm[item.modlName], 'hh:mm')  : '--'}}
			</view>
			<image v-if="index <= stage" class="icon"
				:src="`https://cbti.zhisongkeji.com/uniapp-static/${item.ingicon}`" mode=""></image>
			<image v-else class="icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${item.icon}`" mode=""></image>
			<view class="label">
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="diary-time flex-col">
		<view class="diary-time-label">
			{{label}}
		</view>
		<picker-view v-if="value" :value="value" @change="bindChange" class="diary-time-picker" indicator-class="indicator"
			mask-style="background:transparent">
			<picker-view-column class="column0">
				<view class="item" @touchstart.stop @touchend.stop v-for="(item,index) in pickerData[0]"
					:key="`0-${index}`">{{item}}</view>
			</picker-view-column>
			<picker-view-column class="column1">
				<view class="item" v-for="(item,index) in pickerData[1]" :key="`1-${index}`">{{item}}</view>
			</picker-view-column>
			<picker-view-column class="column2">
				<view class="item" v-for="(item,index) in pickerData[2]" :key="`2-${index}`">{{item}}</view>
			</picker-view-column>
			<picker-view-column class="column3">
				<view class="item" v-for="(item,index) in pickerData[3]" :key="`3-${index}`">{{item}}</view>
			</picker-view-column>
		</picker-view>
	</view>
	<!-- 填写入睡时间时展示 -->
	<view class="tips" v-if="stage == 1" @click="neverSleep()">
		整晚没睡？
	</view>
</template>

<script setup>
	import {
		ref,
		watch
	} from 'vue';
	import {
		getDay,
		formatDate
	} from '/utils/index.js'
	const props = defineProps(['modlName', 'value', 'stage', 'label', 'dateHash', 'dataForm'])
	const emits = defineEmits(['timeChange', 'neverSleep'])
	const stages = [{
		icon: 'bed.png',
		ingicon: 'beding.png',
		width: 10,
		label: '上床',
		modlName: 'nightPrepareSleepDate',
	}, {
		icon: 'sleep.png',
		ingicon: 'sleeping.png',
		width: 34.9,
		label: '入睡',
		modlName: 'nightAsleepDate'
	}, {
		icon: 'wake.png',
		ingicon: 'wakeing.png',
		width: 78.5,
		label: '醒来',
		modlName: 'wakeDate'
	}, {
		icon: 'up.png',
		ingicon: 'uping.png',
		width: 100,
		label: '起床',
		modlName: 'getUpDate'
	}]
	const pickerData = [
		['昨日', '今日'],
		['10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '00', '01', '02', '03',
			'04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15'
		],
		[':'],
		['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '60']
	]

	function bindChange(e, index) {
		const timeindex = e.detail.value

		if (timeindex[1] > 13) {
			timeindex[0] = 1
		} else {
			timeindex[0] = 0
		}
		let time = ''
		time = props.dateHash[timeindex[0]] + ' ' + pickerData[1][timeindex[1]] + ':' + pickerData[3][timeindex[3]] + ':00'
		emits('timeChange', time, timeindex, props.modlName)
	}

	function neverSleep() {
		emits('neverSleep')
	}
</script>
<style lang="scss">
	@import './style.scss';
</style>