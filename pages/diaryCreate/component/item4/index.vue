<template>
	<view class="feel flex-col">
		<view class="feel-label">
			睡眠感受
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (mornFeelType == item.key ? 'currentFeel':'')" :key="index"
				v-for="(item,index) of feels" @click="addMornFeelType(item.key)">
				<img class="fitem-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${mornFeelType == item.key ? item.ingicon :item.icon}`"
					alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="feel flex-col">
		<view class="feel-label">
			是否饮酒、喝茶和咖啡？
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (drink == '1' && index == 0 ? 'current':'')" :key="index"
				v-for="(item,index) of yesno" @click="changeYesNo(item.key,'drink')">
				<img class="fitem-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${drink == item.key ? item.ingicon :item.icon}`"
					alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="records flex-col" v-if="drink == '1' && recorddrink.length > 0">
		<view class="record flex-row" :key="index" v-for="(item,index) of recorddrink">
			<view class="record-duration flex-row">
				<view class="time">
					{{item.useDate}}
				</view>
				<view class="drug">
					{{item.drinkName}}
					<!-- <span class="drug-unit">{{item.drugunit}}</span> -->
				</view>
			</view>
			<view class="record-minute">
				{{item.dosage}}
			</view>
			<view class="record-icon" @click="delectRecord(index,'drink')">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row" @click="addDrink()" v-if="drink == '1'">
		<image class="addrecord-img" src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
	<view class="feel flex-col">
		<view class="feel-label">
			是否做放松训练？
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (relax == '1' && index == 0 ? 'current':'')" :key="index"
				v-for="(item,index) of yesno" @click="changeYesNo(item.key,'relax')">
				<img class="fitem-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${relax == item.key ? item.ingicon :item.icon}`"
					alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="records flex-col" v-if="relax == '1' && recordrelax.length > 0">
		<view class="record flex-row" :key="index" v-for="(item,index) of recordrelax">
			<view class="record-duration">
				{{item.duration}}
			</view>
			<view class="record-minute">
				{{item.minute}}
			</view>
			<view class="record-icon" @click="delectRecord(index,'relax')">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row" @click="addRelax()" v-if="relax == '1'">
		<image class="addrecord-img" src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
	<view class="feel flex-col">
		<view class="feel-label">
			是否有最困的时候？
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (drowsy == '1' && index == 0 ? 'current':'')" :key="index"
				v-for="(item,index) of yesno" @click="changeYesNo(item.key,'drowsy')">
				<img class="fitem-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${drowsy == item.key ? item.ingicon :item.icon}`"
					alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="records flex-col" v-if="drowsy == '1' && recordDrowsy.length > 0">
		<view class="record flex-row" :key="index" v-for="(item,index) of recordDrowsy">
			<view class="record-duration flex-row">
				<view class="time">
					{{item.time}}
				</view>
			</view>
			<view class="record-icon" @click="delectRecord(index,'drowsy')">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row" @click="showDatePop()" v-if="drowsy == '1'">
		<image class="addrecord-img" src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
	
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		getDay,
	} from '/utils/index.js'
	const time = ref(null)
	const props = defineProps(['mornFeelType', 'drink', 'recorddrink', 'relax',
		'recordrelax', 'recordDrowsy', 'drowsy'
	])
	const emits = defineEmits(['addMornFeelType', 'changeYesNo', 'delectRecord', 'addDrowsy', 'addDrink','addSleepy'])

	const current = ref(0)
	const yesno = [{
		label: '有',
		icon: 'yes.png',
		ingicon: 'yesing.png',
		key: '1'
	}, {
		label: '无',
		icon: 'no.png',
		ingicon: 'noing.png',
		key: '0'
	}]

	const feels = [{
		label: '很差',
		key: '1',
		icon: 'hc.png',
		ingicon: 'hcing.png'
	}, {
		label: '不太好',
		key: '2',
		icon: 'bth.png',
		ingicon: 'bthing.png'
	}, {
		label: '一般',
		key: '3',
		icon: 'yb.png',
		ingicon: 'ybing.png'
	}, {
		label: '很不错',
		key: '4',
		icon: 'hbc.png',
		ingicon: 'hbcing.png'
	}, {
		label: '非常好',
		key: '5',
		icon: 'fch.png',
		ingicon: 'fching.png'
	}]

	function showDatePop(){
		emits('addSleepy', true)
	}
	
	function addMornFeelType(label) {
		emits('addMornFeelType', label)
	}

	function addRelax() {
		emits('addRelax', 'addRelax')
	}

	function addDrink() {
		emits('addDrink', 'addDrink')
	}


	function delectRecord(index, type) {
		emits('delectRecord', index, type)
	}


	function changeYesNo(key, type) {

		emits('changeYesNo', key, type)

	}
</script>
<style lang="scss">
	@import './style.scss';
</style>