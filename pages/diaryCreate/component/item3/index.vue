<template>
	<!-- <carditem label="昨天有小睡吗？"/> -->
	<view class="feel flex-col">
		<view class="feel-label">
			昨天有小睡吗?
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (nap == '1' && index == 0 ? 'current':'')" :key="index"
				v-for="(item,index) of yesno" @click="changeYesNo(item.key,'nap')">
				<img class="fitem-icon"
					:src="`https://cbti.zhisongkeji.com/uniapp-static/${nap == item.key ? item.ingicon :item.icon}`"
					alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="records flex-col" v-if="nap == '1' && recordsleep.length > 0">
		<view class="record flex-row" :key="index" v-for="(item,index) of recordsleep">
			<view class="record-duration">
				{{item.duration}}
			</view>
			<view class="record-minute">
				{{item.minute}}
			</view>
			<view class="record-icon" @click="delectRecord(index,'sleep')">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row" @click="addSleep()" v-if="nap == '1'">
		<image src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
	<view class="waketime">
		<view class="feel flex-col">
			<view class="feel-label">
				昨天有服药吗？
			</view>
			<view class="feel-content w-full flex-row">
				<view :class="'fitem flex-col ' + (drug == '1' && index == 0 ? 'current':'')" :key="index"
					v-for="(item,index) of yesno" @click="changeYesNo(item.key,'drug')">
					<img class="fitem-icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${drug == item.key ? item.ingicon :item.icon}`" alt="" />
					{{item.label}}
				</view>
			</view>
		</view>
	</view>
	<view class="records flex-col" v-if="drug == '1' && recorddrug.length > 0">
		<view class="record flex-row" :key="index" v-for="(item,index) of recorddrug">
			<view class="record-duration flex-row">
				<view class="time">
					{{item.useDate}}
				</view>
				<view class="drug">
					<view>{{item.medicineName}}</view>
					<view class="drug-unit">({{item.commonName}}) {{item.medicineSpecification}}</view>
				</view>
			</view>
			<view class="record-minute">
				{{item.dosage}}
			</view>
			<view class="record-icon" @click="delectRecord(index,'drug')">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row" v-if="drug == '1'" @click="addDrug()">
		<image src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
</template>

<script setup>
	import carditem from '../yesnocard.vue'
	import {
		ref
	} from 'vue';

	const props = defineProps(['recordsleep', 'recorddrug', 'nap', 'drug'])
	const emits = defineEmits('timeChange', 'changeYesNo', 'addSleep', 'delectRecord','addDrug')

	const yesno = [{
		label: '有',
		icon: 'yes.png',
		ingicon: 'yesing.png',
		key: '1'
	}, {
		label: '无',
		icon: 'no.png',
		ingicon: 'noing.png',
		key: '0'
	}]

	function addSleep() {
		emits('addSleep','addSleep')
	}

	function delectRecord(index, type) {
		emits('delectRecord', index, type)
	}

	function addDrug() {
		emits('addDrug')
	}

	function changeYesNo(key, type) {
		emits('changeYesNo', key, type)
	}

	function bindChange(e, type) {
		const index = e.detail.value
		let str = ''
		if (type == 'nightWakeTimes') {
			str = wakeunit[0][index[0]]
		} else if (type == 'nightWakeTotalTime') {
			str = parseInt(pickerData[0][index[0]], 10) * 60 + parseInt(pickerData[2][index[2]], 10)
		}

		emits('timeChange', str, index, type)
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>