.feel {
	align-items: center;
	padding-bottom: 28rpx;
	&-label {
		font-weight: bold;
		font-size: 32rpx;
		color: #1c1c1e;
		margin: 48rpx auto;
	}

	&-content {
		flex: 1;
		align-items: center;
		justify-content: space-between;
		.fitem {
			flex: 1;
			align-items: center;
			justify-content: center;
			&-icon {
				width: 64rpx;
				height: 64rpx;
				margin-bottom: 16rpx;
			}
			font-weight: 400;
			font-size: 20rpx;
			color: #2c2c2e;
		}

		.current {
			position: relative;
		}
		.current:after {
			content: '';
			display: block;
			position: absolute;
			bottom: -30rpx;
			left: calc(50% - 27rpx);
			border-left: 24rpx solid transparent;
			border-right: 24rpx solid transparent;
			// border-bottom: 20rpx solid rgba(235, 235, 245, 0.3);
		}
	}

}
.records {
	width: 100%;
	background: rgba(235, 235, 245, 0.3);
	border-radius: 24rpx;
	padding: 0 32rpx;
	margin-bottom: 16rpx;
	.record {
		width: 100%;
		height: 88rpx;
		border-bottom: 1rpx solid #e5e5ea;
		&-duration {
			flex: 1;
			font-weight: bold;
			font-size: 28rpx;
			color: #5e5ce6;
			align-items: center;

			.drug {
				font-weight: 400;
				font-size: 28rpx;
				color: #000000;
				padding: 0 16rpx;
				&-unit {
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
			}
		}
		&-minute {
			font-weight: 400;
			font-size: 24rpx;
			color: #000000;
		}
		&-icon {
			margin-left: 24rpx;
			> image {
				width: 30rpx;
				height: 30rpx;
			}
		}
	}
	.record:last-child {
		border: none;
	}
}
.addrecord {
	width: 100%;
	height: 88rpx;
	background: rgba(235, 235, 245, 0.3);
	border-radius: 24rpx;
	justify-content: center;
	align-items: center;
	margin-bottom: 32rpx;
	> image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 16rpx;
	}
	font-weight: 400;
	font-size: 28rpx;
	color: #5e5ce6;
}
