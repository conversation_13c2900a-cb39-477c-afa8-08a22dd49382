<template>
	<view class="feel flex-col">
		<view class="feel-label">
			{{label}}
		</view>
		<view class="feel-content w-full flex-row">
			<view :class="'fitem flex-col ' + (current == 0 && index == 0 ? 'current':'')" :key="index"
				v-for="(item,index) of yesno">
				<img class="fitem-icon" :src="`https://cbti.zhisongkeji.com/uniapp-static/${item.icon}`" alt="" />
				{{item.label}}
			</view>
		</view>
	</view>
	<view class="records flex-col">
		<view class="record flex-row" :key="index" v-for="(item,index) of recordsleep">
			<view class="record-duration">
				{{item.duration}}
			</view>
			<view class="record-minute">
				{{item.minute}}
			</view>
			<view class="record-icon">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/delect.png" mode=""></image>
			</view>
		</view>
	</view>
	<view class="addrecord flex-row">
		<image src="https://cbti.zhisongkeji.com/uniapp-static/add.png" mode=""></image>
		添加记录
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	const props = defineProps(['label'])
	const current = ref(0)
	const yesno = [{
		label: '有',
		icon: 'yes.png',
		ingicon: 'yesing.png'
	}, {
		label: '无',
		icon: 'no.png',
		ingicon: 'noing.png'
	}]

	const recordsleep = ref([{
		duration: '12:00~12:35',
		minute: '35分钟'
	}, {
		duration: '12:00~12:35',
		minute: '35分钟'
	}])
	
	
	const recorddrug = ref([{
		time: '12:00',
		drug:'Belsomra',
		drugunit:'15mg/片',
		minute: '1/2片'
	}, {
		time: '12:00',
		drug:'Belsomra',
		drugunit:'15mg/片',
		minute: '1/2片'
	}])
</script>
<style lang="scss">
	.feel {
		align-items: center;
		padding-bottom: 28rpx;
		&-label {
			font-weight: bold;
			font-size: 32rpx;
			color: #1c1c1e;
			margin: 48rpx auto;
		}
	
		&-content {
			flex: 1;
			align-items: center;
			justify-content: space-between;
			.fitem {
				flex: 1;
				align-items: center;
				justify-content: center;
				&-icon {
					width: 64rpx;
					height: 64rpx;
					margin-bottom: 16rpx;
				}
				font-weight: 400;
				font-size: 20rpx;
				color: #2c2c2e;
			}
	
			.current {
				position: relative;
			}
			.current:after {
				content: '';
				display: block;
				position: absolute;
				bottom: -27rpx;
				left: calc(50% - 27rpx);
				border-left: 24rpx solid transparent;
				border-right: 24rpx solid transparent;
				border-bottom: 20rpx solid rgba(235, 235, 245, 0.3);
			}
		}
	}
	.records {
		width: 100%;
		background: rgba(235, 235, 245, 0.3);
		border-radius: 24rpx;
		padding: 0 32rpx;
		.record {
			width: 100%;
			height: 88rpx;
			border-bottom: 1rpx solid #e5e5ea;
			&-duration {
				flex: 1;
				font-weight: bold;
				font-size: 28rpx;
				color: #5e5ce6;
				align-items: center;
	
				.drug {
					font-weight: 400;
					font-size: 28rpx;
					color: #000000;
					padding: 0 16rpx;
					&-unit {
						font-weight: 400;
						font-size: 24rpx;
						color: #999999;
					}
				}
			}
			&-minute {
				font-weight: 400;
				font-size: 24rpx;
				color: #000000;
			}
			&-icon {
				margin-left: 24rpx;
				> image {
					width: 30rpx;
					height: 30rpx;
				}
			}
		}
		.record:last-child {
			border: none;
		}
	}
	.addrecord {
		width: 100%;
		height: 88rpx;
		background: rgba(235, 235, 245, 0.3);
		border-radius: 24rpx;
		justify-content: center;
		align-items: center;
		margin: 16rpx 0 32rpx;
		> image {
			width: 28rpx;
			height: 28rpx;
			margin-right: 16rpx;
		}
		font-weight: 400;
		font-size: 28rpx;
		color: #5e5ce6;
	}
	
</style>