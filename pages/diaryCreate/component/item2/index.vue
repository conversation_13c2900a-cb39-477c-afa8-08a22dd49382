<template>
	<view class="waketime">
		<view class="feel flex-col">
			<view class="feel-label">
				夜里有醒来吗？
			</view>
			<view class="feel-content w-full flex-row">
				<view :class="'fitem flex-col ' + (nightWake == '1' && index == 0 ? 'current':'')" :key="index"
					v-for="(item,index) of yesno" @click="changeYesNo(item.key,'nightWake')">
					<img class="fitem-icon"
						:src="`https://cbti.zhisongkeji.com/uniapp-static/${nightWake == item.key ? item.ingicon :item.icon}`"
						alt="" />
					{{item.label}}
				</view>
			</view>
		</view>
	</view>
	<view class="w-full" v-if="nightWake == '1'">
		<view class="wake-time flex-col">
			<view class="wake-time-label">
				夜醒次数
			</view>
			<picker-view v-if="nightWakeTimes" :value="nightWakeTimes" @change="(e)=>{bindChange(e,'nightWakeTimes')}" class="wake-time-picker"
				indicator-class="indicator" mask-style="background:transparent">
				<picker-view-column :class="`column column${pickerIndex}`" :key="pickerIndex"
					v-for="(picker,pickerIndex) of wakeunit">
					<view class="item" v-for="(item,index) in picker" :key="`${pickerIndex}-${index}`">{{item}}</view>
				</picker-view-column>
			</picker-view>
		</view>
		<view class="wake-time flex-col">
			<view class="wake-time-label">
				夜醒时长
			</view>
			<picker-view v-if="nightWakeTotalTime" :value="nightWakeTotalTime" @change="(e)=>{bindChange(e,'nightWakeTotalTime')}" class="wake-time-picker"
				indicator-class="indicator" mask-style="background:transparent">
				<picker-view-column :class="`column column${pickerIndex}`" :key="pickerIndex"
					v-for="(picker,pickerIndex) of pickerData">
					<view class="item" v-for="(item,index) in picker" :key="`${pickerIndex}-${index}`">{{item}}</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>

</template>

<script setup>
	import {
		ref
	} from 'vue';
	const props = defineProps(['nightWakeTimes','nightWakeTotalTime', 'nightWake'])
	const emits = defineEmits('timeChange','changeYesNo')
	const current = ref(0)
	const yesno = [{
		label: '有',
		icon: 'yes.png',
		ingicon: 'yesing.png',
		key: '1'
	}, {
		label: '无',
		icon: 'no.png',
		ingicon: 'noing.png',
		key: '0'
	}]
	const wakeunit = [
		['1', '2', '3', '4', '5', '6', '7', '8', '9','10','11', '12', '13', '14', '15', '16', '17', '18', '19','20'],
		['次']
	]
	const pickerData = [
		['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15',
			'16', '17', '18', '19', '20', '21', '22', '23', '24'
		],
		['小时'],
		['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15',
			'16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31',
			'32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47',
			'48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60'
		],
		['分钟']
	]


	function changeYesNo(key, type) {
		emits('changeYesNo', key, type)
		if (key == '0') { //无
			emits('timeChange', '', [0, 0], 'nightWakeTimes')
			emits('timeChange', '', [0, 0, 1, 0], 'nightWakeTotalTime')
		}
	}

	function bindChange(e, type) {
		const index = e.detail.value
		let str = ''
		if (type == 'nightWakeTimes') {
			str = wakeunit[0][index[0]]
		} else if (type == 'nightWakeTotalTime') {
			str = parseInt(pickerData[0][index[0]], 10) * 60 + parseInt(pickerData[2][index[2]], 10)
		}

		emits('timeChange', str, index, type)
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>