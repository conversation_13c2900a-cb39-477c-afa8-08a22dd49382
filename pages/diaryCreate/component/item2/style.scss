.feel {
	align-items: center;
	padding-bottom: 28rpx;
	&-label {
		font-weight: bold;
		font-size: 32rpx;
		color: #1c1c1e;
		margin: 48rpx auto;
	}

	&-content {
		flex: 1;
		align-items: center;
		justify-content: space-between;
		.fitem {
			flex: 1;
			align-items: center;
			justify-content: center;
			&-icon {
				width: 64rpx;
				height: 64rpx;
				margin-bottom: 16rpx;
			}
			font-weight: 400;
			font-size: 20rpx;
			color: #2c2c2e;
		}
		
		.current {
			position: relative;
		}
		.current:after {
			content: '';
			display: block;
			position: absolute;
			bottom: -30rpx;
			left: calc(50% - 27rpx);
			border-left: 24rpx solid transparent;
			border-right: 24rpx solid transparent;
			border-bottom: 20rpx solid rgba(235, 235, 245, 0.3);
		}
	}
}
.wake-time {
	height: 444rpx;
	background: rgba(235, 235, 245, 0.3);
	border-radius: 24px;
	padding: 32rpx;
	align-items: center;
	margin-bottom: 16rpx;

	&-label {
		font-weight: bold;
		font-size: 28rpx;
		color: #000000;
	}
	&-picker {
		width: 100%;
		height: 308rpx;
		margin-top: 20rpx;
		display: flex;
		justify-content: center;

		.column{
			max-width: 96rpx;
			width: 96rpx;
		}
		.item {
			width: 96rpx;
			height: 80rpx;
			text-align: center;
			font-weight: bold;
			font-size: 28rpx;
			color: #000000;
			line-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	.indicator {
		height: 80rpx;
	}

}
