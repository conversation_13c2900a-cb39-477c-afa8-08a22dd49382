<template>
	<view catchtouchmove='true' :class="['diary-page ', currentPage > 2 && 'diary-page-padding']">
		<scroll-view :scroll-y="currentPage < 3 ? false : true" :scroll-top="viewTop" class="w-full diary-container">
			<!-- <view class="w-full diary-container"> -->
			<!-- 	nightPrepareSleepDate: 22, //昨晚准备睡觉时间
				nightAsleepDate: 22, //昨晚睡觉时间
				wakeDate: 7, //醒来时间
				getUpDate: 7, //起床时间 -->
			<clock v-if="currentPage < 3" @neverSleep="neverSleep" @changeTime="changeTime" :step="currentPage"
				:gobedTime="dataFormIndex.nightPrepareSleepDate" :getUpTime="dataFormIndex.getUpDate"
				:sleepTime="dataFormIndex.nightAsleepDate" :wakeTime="dataFormIndex.wakeDate" />
			<!-- <clock v-else-if="currentPage == 2"  step=2 /> -->
			<!-- 				<Base v-else-if="currentPage == 3" modlName='wakeDate' :value="dataFormIndex.wakeDate"
					:dataForm="dataForm" stage="2" :label="timeHash[currentPage]" @timeChange="timeChange"
					:dateHash="dateHash" />
				<Base v-else-if="currentPage == 4" modlName='getUpDate' :value="dataFormIndex.getUpDate"
					:dataForm="dataForm" stage="3" :label="timeHash[currentPage]" :dateHash="dateHash"
					@timeChange="timeChange" /> -->
			<item2 v-else-if="currentPage== 3" @changeYesNo="changeYesNo" :nightWake="dataForm.nightWake"
				:nightWakeTotalTime="dataFormIndex.nightWakeTotalTime" :nightWakeTimes="dataFormIndex.nightWakeTimes"
				@timeChange="timeChange" />
			<item3 v-else-if="currentPage== 4" :recordsleep="recordsleep" :recorddrug="recorddrug" :nap="dataForm.nap"
				:drug="dataForm.drug" @changeYesNo="changeYesNo" @addSleep="showTimeDurationPop"
				@delectRecord="delectRecord" @addDrug="addDragPop = true" />
			<Item4 v-else-if="currentPage== 5" :mornFeelType="dataForm.mornFeelType" :drink="dataForm.drink"
				@addMornFeelType="addMornFeelType" @changeYesNo="changeYesNo" @delectRecord="delectRecord"
				:recorddrink="recorddrink" :relax="dataForm.relax" :recordrelax="recordrelax"
				@addRelax="showTimeDurationPop" :drowsy="dataForm.drowsy" @addDrowsy="addDrowsy"
				:recordDrowsy="recordDrowsy" @addDrink="addDrinkPop = true" @addSleepy="showDate = true" />
			<!-- </view> -->
		</scroll-view>
		<view class="diary-changebtn">
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dback.png" mode="" @click="back()"></image>
			<label>{{currentPage}}/5</label>
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dnext.png" v-if="currentPage < 5" mode=""
				@click="next()"></image>
			<image src="https://cbti.zhisongkeji.com/uniapp-static/dfinish.png" v-else mode="" @click="subimit()">
			</image>
		</view>
		<view class="sleepyPop" v-if="showDate">
			<view class="sleepyPop-bg" @click="showDate= false">

			</view>
			<view class="timeDuration">
				<view class="timeDuration-content">
					<picker-view :value="sleepy" @change="sleepyChange" class="timePicker" indicator-class="indicator"
						mask-style="background:transparent">
						<picker-view-column class="column0">
							<view class="item" @touchstart.stop @touchend.stop v-for="(item,index) in pickerData[0]"
								:key="`0-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column1">
							<view class="item" v-for="(item,index) in pickerData[1]" :key="`1-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column2">
							<view class="item" v-for="(item,index) in pickerData[2]" :key="`2-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column3">
							<view class="item" v-for="(item,index) in pickerData[3]" :key="`3-${index}`">{{item}}</view>
						</picker-view-column>
					</picker-view>
				</view>
				<view class="timeDuration-btn" @click="confirmSleepyTime()">
					确定
				</view>
			</view>
		</view>

		<view class="pop" v-if="showTimeDuration">
			<view class="pop-bg" @click="showTimeDuration= false">

			</view>
			<view class="timeDuration">
				<view class="timeDuration-label">
					<label>开始时间</label>
					<label>结束时间</label>
				</view>
				<view class="timeDuration-content">
					<picker-view v-if="startTimeValue" :value="startTimeValue"
						@change="(e)=>{bindChange(e,'startTime','startTimeValue')}" class="timePicker"
						indicator-class="indicator" mask-style="background:transparent">
						<picker-view-column class="column0">
							<view class="item" @touchstart.stop @touchend.stop v-for="(item,index) in pickerData[0]"
								:key="`0-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column1">
							<view class="item" v-for="(item,index) in pickerData[1]" :key="`1-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column2">
							<view class="item" v-for="(item,index) in pickerData[2]" :key="`2-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column3">
							<view class="item" v-for="(item,index) in pickerData[3]" :key="`3-${index}`">{{item}}</view>
						</picker-view-column>
					</picker-view>
					<picker-view v-if="endTimeValue" :value="endTimeValue"
						@change="(e)=>{bindChange(e,'endTime','endTimeValue')}" class="timePicker"
						indicator-class="indicator" mask-style="background:transparent">
						<picker-view-column class="column0">
							<view class="item" @touchstart.stop @touchend.stop v-for="(item,index) in pickerData[0]"
								:key="`0-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column1">
							<view class="item" v-for="(item,index) in pickerData[1]" :key="`1-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column2">
							<view class="item" v-for="(item,index) in pickerData[2]" :key="`2-${index}`">{{item}}</view>
						</picker-view-column>
						<picker-view-column class="column3">
							<view class="item" v-for="(item,index) in pickerData[3]" :key="`3-${index}`">{{item}}</view>
						</picker-view-column>
					</picker-view>
				</view>
				<view class="timeDuration-btn" @click="confirmTimeDuration()">
					确定
				</view>
			</view>
		</view>
		<addDrug v-if="addDragPop" @addDrug="addDrug" />
		<addDrink v-if="addDrinkPop" @addDrink="addDrink" />
	</view>
</template>

<script>
	import Base from './component/basic/index.vue'
	import item2 from './component/item2/index.vue'
	import Item3 from './component/item3/index.vue'
	import Item4 from './component/item4/index.vue'
	import addDrug from '@/components/addDrug/index.vue'
	import addDrink from '@/components/addDrink/index.vue'
	import clock from '@/components/Draggable-circle/index.vue'
	import {
		getDay,
		formatDate,
		getDayDifference,
		mergeAndIntersectObjects
	} from '/utils/index.js'
	import {
		getSleepDiary,
		getSleepTime,
		getMedicineRecord,
		createSleepDiary,
		addMedicineRecord,
		addSleepRecord,
		addDrinkRecord,
		addRelaxRecord,
		addDrowsyRecord
	} from '@/assets/api/index.js'
	export default {
		components: {
			Base,
			item2,
			Item3,
			Item4,
			addDrug,
			addDrink,
			clock
		},
		data() {
			return {
				viewTop: 0,
				recordDrowsy: [],
				recordrelax: [],
				recorddrink: [],
				recordsleep: [], // 小睡记录
				recorddrug: [], //服药记录
				startTime: getDay(-1) + ' ' + '12:00:00',
				endTime: getDay(-1) + ' ' + '12:30:00',
				startTimeValue: [0, 2, 0, 0],
				endTimeValue: [0, 2, 0, 6],
				addDrinkPop: false,
				addDragPop: false,
				showTimeDuration: false,
				timeHash: {
					1: '昨晚几点准备睡觉的？',
					2: '昨晚几点入睡的？',
					3: '今天几点睡醒的？',
					4: '今天几点起床的'
				},
				currentPage: 1,
				dataForm: {
					nightPrepareSleepDate: "22:00", //昨晚准备睡觉时间
					nightAsleepDate: "22:00", //昨晚睡觉时间
					wakeDate: "07:00", //醒来时间
					getUpDate: "07:00", //起床时间
					nightWake: '0', // 是否夜醒
					nightWakeTimes: 1, //夜醒次数,
					nightWakeTotalTime: 1, //夜醒总时长（分钟）
					nap: '0', //是否小睡
					drug: '0', //是否服药
					mornFeelType: '', //早晨感觉如何,
					drink: '0',
					relax: '0',
					drowsy: '0',
					remark: '', //备注,
					recordTime: getDay(0),
				},
				dataFormIndex: {
					nightPrepareSleepDate: 22, //昨晚准备睡觉时间 默认昨日22:00
					nightAsleepDate: 22, //昨晚睡觉时间
					wakeDate: 7, //醒来时间
					getUpDate: 7, //起床时间
					nightWakeTimes: [0, 0], //夜醒次数,
					nightWakeTotalTime: [0, 0, 1, 0], //夜醒总时长（分钟）
				},
				pickerData: [
					['昨日', '今日'],
					['10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '00', '01',
						'02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15'
					],
					[':'],
					['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '60']
				],
				dateHash: {
					0: getDay(-1), //昨日
					1: getDay(0) //今日
				},
				currentTime: getDay(0),
				yesterday: getDay(-1),
				showTimeDurationType: 'addSleep',
				isNeverSleep: false,
				showDate: false,
				sleepy: [0, 0, 0, 0],
				sleepyTime: ''
			}
		},
		onLoad({
			time,
			timeCreate
		}) {
			this.currentTime = time || timeCreate
			if (time) {
				uni.setNavigationBarTitle({
					title: '编辑睡眠日记',
				});
			}
			if (this.currentTime) {
				this.yesterday = getDay(getDayDifference(this.currentTime) - 1)

				this.startTime = this.yesterday + ' ' + '12:00:00'
				this.endTime = this.yesterday + ' ' + '12:30:00'
				this.dataForm.nightPrepareSleepDate = '22:00' //昨晚准备睡觉时间
				this.dataForm.nightAsleepDate = '22:00' //昨晚睡觉时间
				this.dataForm.wakeDate = '07:00' //醒来时间
				this.dataForm.getUpDate = '07:00' //起床时间
				this.dataForm.recordTime = this.currentTime
				this.dateHash = {
					0: this.yesterday, //昨日
					1: this.currentTime //今日
				}
			}
		},
		onShow() {
			uni.hideHomeButton();
		},
		methods: {
			confirmSleepyTime() {
				this.addDrowsy(this.sleepyTime || this.yesterday + ' ' + '10:00:00')
				this.showDate = false
			},
			sleepyChange(e) {
				//type start end
				const timeindex = e.detail.value
				if (timeindex[1] > 13) {
					timeindex[0] = 1
				} else {
					timeindex[0] = 0
				}
				this.sleepy = timeindex

				let time = ''
				time = this.dateHash[timeindex[0]] + ' ' + this.pickerData[1][timeindex[1]] + ':' + this
					.pickerData[3][
						timeindex[3]
					] + ':00'
				this.sleepyTime = time
			},
			dealTime(timeString) {
				const [hours, minutes] = timeString.split(':').map(Number);
				return hours + minutes / 60;
			},
			changeTime(e) {
				let {
					nightPrepareSleepDate,
					nightAsleepDate,
					wakeDate,
					getUpDate
				} = e

				this.dataForm = Object.assign({}, this.dataForm, e);

				this.dataFormIndex = Object.assign({}, this.dataFormIndex, {
					nightPrepareSleepDate: this.dealTime(nightPrepareSleepDate),
					nightAsleepDate: this.dealTime(nightAsleepDate),
					wakeDate: this.dealTime(wakeDate),
					getUpDate: this.dealTime(getUpDate)
				});
			},
			formattedUseDate(date, time) {
				// 获取当前时间
				let now = new Date();
				let today = new Date(getDay(0))
				let currentHour = now.getHours();
				let currentMinute = now.getMinutes();

				let useDate = ''

				let dateParams = new Date(date);
				if (dateParams < today) {
					useDate = date + ` ${time}:00`
				} else {
					// 解析传入的时间
					let timeParts = time.split(":");
					let useHour = parseInt(timeParts[0], 10);
					let useMinute = parseInt(timeParts[1], 10);

					if (useHour < currentHour || (useHour === currentHour && useMinute < currentMinute)) {
						useDate = getDay(0) + ` ${time}:00`
					} else {
						useDate = getDay(-1) + ` ${time}:00`
					}
				}
				return useDate
			},
			neverSleep() { //填写入睡时间时选择了整晚没睡觉
				this.isNeverSleep = true
				this.currentPage = 4
			},
			subimit() {
				if(!this.next()) return;
				
				let dataFormParams = {
					nightPrepareSleepDate: '', //昨晚准备睡觉时间
					nightAsleepDate: '', //昨晚睡觉时间
					wakeDate: '', //醒来时间
					getUpDate: '', //起床时间
					nightWakeTimes: '', //夜醒次数,
					nightWakeTotalTime: '', //夜醒总时长（分钟）
					mornFeelType: '', //早晨感觉如何,
					remark: '', //备注,
					recordTime: '',
				}
				let params = mergeAndIntersectObjects(dataFormParams, this.dataForm)
				const fun = (timeStr) => {
					const [hours] = timeStr.split(':').map(Number);
					if (hours > 18) { // 18点前今天 18点后昨日
						return this.yesterday + ' ' + timeStr + ':00'
					} else {
						return this.currentTime + ' ' + timeStr + ':00'
					}
				}

				if (this.dataForm.nightWake == '0') {
					params.nightWakeTimes = '' //夜醒次数,
					params.nightWakeTotalTime = '' //夜醒总时长（分钟）
				}

				if (this.isNeverSleep) {
					params.nightAsleepDate = ''
					params.wakeDate = ''
					params.nightPrepareSleepDate = fun(params.nightPrepareSleepDate)
					params.getUpDate = fun(params.getUpDate)
				} else {
					params.nightPrepareSleepDate = fun(params.nightPrepareSleepDate)
					params.nightAsleepDate = fun(params.nightAsleepDate)
					params.wakeDate = fun(params.wakeDate)
					params.getUpDate = fun(params.getUpDate)
				}

				createSleepDiary(params).then(reuslt => {
					let {
						id
					} = reuslt
					//小睡记录
					if (this.dataForm.nap == '1') {
						let sleepForm = this.recordsleep.map(item => {
							return {
								"startDate": item.startDate,
								"endDate": item.endDate,
								"recordDate": params.recordTime,
								"diaryId": id
							}
						})

						addSleepRecord(sleepForm).then(result => {

						})
					}

					//服药记录
					if (this.dataForm.drug == '1') {
						let drugForm = this.recorddrug.map(item => {

							return {
								"medicineId": item.medicineId,
								"medicineName": item.medicineName,
								"medicineSpecification": item.medicineSpecification,
								"useDate": this.formattedUseDate(params.recordTime, item.useDate),
								"dosage": item.dosage,
								"recordDate": params.recordTime,
								"diaryId": id
							};
						});
						console.log('drugForm', drugForm)
						addMedicineRecord(drugForm).then(result => {

						})
					}


					//饮品记录
					if (this.dataForm.drink == '1') {
						let drinkForm = this.recorddrink.map(item => {
							return {
								"dosage": item.dosage,
								"drinkName": item.drinkName,
								"useDate": this.formattedUseDate(params.recordTime, item.useDate),
								"recordDate": params.recordTime,
								"diaryId": id
							}
						})
						addDrinkRecord(drinkForm).then(result => {

						})
					}

					//放松训练记录
					if (this.dataForm.relax == '1') {
						let relaxForm = this.recordrelax.map(item => {

							return {
								startDate: item.startDate,
								endDate: item.endDate,
								totalNum: item.minuteNum,
								recordDate: params.recordTime,
								diaryId: id
							}
						})
						addRelaxRecord(relaxForm).then(result => {

						})
					}


					//最困时间记录
					if (this.dataForm.drowsy == '1') {
						let drowsyForm = this.recordDrowsy.map(item => {

							return {
								// this.formattedUseDate(params.recordTime, item.timeDate)
								tiredTime: item.timeDate,
								recordDate: params.recordTime,
								diaryId: id
							}
						})
						addDrowsyRecord(drowsyForm).then(result => {

						})
					}
					// uni.navigateBack({})
					uni.reLaunch({
						url: '/pages/index/index'
					})
				})
			},
			addDrink(list) {
				this.addDrinkPop = false
				let unionArray = Array.from(new Set([...this.recorddrink, ...list]));
				this.recorddrink = unionArray
			},
			addDrowsy(value) {
				this.recordDrowsy.push({
					time: formatDate(value, 'hh:mm'),
					timeDate: value
				})
			},
			addMornFeelType(value) {
				this.dataForm.mornFeelType = value
			},
			addDrug(list) {
				this.addDragPop = false
				let unionArray = Array.from(new Set([...this.recorddrug, ...list]));
				this.recorddrug = unionArray
			},
			delectRecord(index, type) {
				if (type == 'sleep') {
					this.recordsleep.splice(index, 1)
				} else if (type == 'drug') {
					this.recorddrug.splice(index, 1)
				} else if (type == 'drowsy') {
					this.recordDrowsy.splice(index, 1)
				} else if (type == 'relax') {
					this.recordrelax.splice(index, 1)
				} else if (type == 'drink') {
					this.recorddrink.splice(index, 1)
				}
			},
			showTimeDurationPop(type) {
				if (type == 'addSleep') { //默认时间昨天的12：00到12：30
					this.startTimeValue = [0, 2, 0, 0]
					this.endTimeValue = [0, 2, 0, 6]
					this.startTime = this.yesterday + ' ' + '12:00:00'
					this.endTime = this.yesterday + ' ' + '12:30:00'
				}
				this.showTimeDurationType = type
				this.showTimeDuration = true
			},
			bindChange(e, type, typeIndex) {
				//type start end
				const timeindex = e.detail.value
				if (timeindex[1] > 13) {
					timeindex[0] = 1
				} else {
					timeindex[0] = 0
				}
				this[typeIndex] = timeindex

				let time = ''
				time = this.dateHash[timeindex[0]] + ' ' + this.pickerData[1][timeindex[1]] + ':' + this
					.pickerData[3][
						timeindex[3]
					] + ':00'
				this[type] = time

			},
			confirmTimeDuration() {

				const startTime = new Date(this.startTime).getTime()
				const endTime = new Date(this.endTime).getTime()

				if (startTime > endTime) {
					wx.showToast({
						title: '当前结束时间早于开始时间',
						duration: 2000,
						icon: 'none'
					})
				} else if (startTime < endTime) {
					let minute = (endTime - startTime) / 60000
					let obj = {
						duration: formatDate(this.startTime, 'hh:mm') + '~' + formatDate(this.endTime, 'hh:mm'),
						minute: minute + '分钟',
						startDate: this.startTime,
						endDate: this.endTime,
						minuteNum: minute
					}
					if (this.showTimeDurationType == 'addSleep') {
						this.recordsleep.push(obj)
					} else if (this.showTimeDurationType == 'addRelax') {
						this.recordrelax.push(obj)
					}
					this.showTimeDuration = false
					// 重制开始结束

				} else if (startTime === endTime) {
					wx.showToast({
						title: '当前开始时间的等于结束时间',
						duration: 2000,
						icon: 'none'
					})
				}

			},
			changeYesNo(key, type) {
				this.dataForm[type] = key
				if (type == 'drowsy' && key == '1') { //需要填写最困时间
					this.$nextTick(function() {
						setTimeout(() => {
							uni.createSelectorQuery().select('.diary-container').boundingClientRect((
								res) => {
								this.viewTop = res.height
							}).exec()
						}, 50)

					});
				}
			},
			timeChange(time, timeindex, type) {
				console.log('--', time, timeindex, type)
				this.dataForm[type] = time
				this.dataFormIndex[type] = timeindex
				if (type == 'nightAsleepDate') { //入睡时间不能早于上床时间
					const t1 = new Date(this.dataForm.nightPrepareSleepDate)
					const t2 = new Date(time)
					if (t2 < t1) {
						wx.showToast({
							title: '入睡时间不能早于上床时间',
							duration: 1000,
							icon: 'none'
						})
						return
					}
				} else if (type == 'wakeDate') { //醒来时间不得早于入睡和上床时间
					const t1 = new Date(this.dataForm.nightPrepareSleepDate)
					const t2 = new Date(this.dataForm.nightAsleepDate)
					const t3 = new Date(time)
					if (t3 < t1) {
						wx.showToast({
							title: '醒来时间不得早于上床时间',
							duration: 1000,
							icon: 'none'
						})
						return
					} else if (t3 < t2) {
						wx.showToast({
							title: '醒来时间不得早于入睡时间',
							duration: 1000,
							icon: 'none'
						})
						return
					}
				} else if (type == 'getUpDate') { //起床时间不得早于醒来时间
					const t3 = new Date(this.dataForm.wakeDate)
					const t4 = new Date(time)
					if (t4 < t3) {
						wx.showToast({
							title: '起床时间不得早于醒来时间',
							duration: 1000,
							icon: 'none'
						})
						return
					}
				}


			},
			back() {
				if (this.isNeverSleep && this.currentPage === 4) { //在起床时间页,点击返回上一页,直接到1
					this.currentPage = 1
					this.isNeverSleep = false
				} else {
					this.currentPage > 1 ? this.currentPage-- : ''
				}
			},
			next() {

				let rule = {
					3: [{
						modleName: 'nightWakeTimes',
						error: '请选择夜醒次数'
					}, {
						modleName: 'nightWakeTotalTime',
						error: '请选择夜醒时长'
					}],
					4: [{
						modleName: 'nap',
						recordName: 'recordsleep',
						error: '请添加小睡记录'
					}, {
						modleName: 'drug',
						recordName: 'recorddrug',
						error: '请添加服药记录'
					}],
					5: [{
						modleName: 'mornFeelType',
						error: '请选择睡眠感受'
					}, {
						modleName: 'drink',
						recordName: 'recorddrink',
						error: '请添加饮品记录'
					}, {
						modleName: 'relax',
						recordName: 'recordrelax',
						error: '请添加训练记录'
					}, {
						modleName: 'drowsy',
						recordName: 'recordDrowsy',
						error: '请添加最困时间记录'
					}],
				}
				let check = true
				if (this.currentPage == 3 && this.dataForm.nightWake == '1') {
					for (let item of rule[3]) {
						if (!this.dataForm[item.modleName]) {
							wx.showToast({
								title: item.error,
								duration: 1000,
								icon: 'none'
							})
							return
						}
					}
				} else if (this.currentPage == 4) {
					for (let item of rule[4]) {
						if (this.dataForm[item.modleName] == '1' && this[item.recordName].length == 0) {
							wx.showToast({
								title: item.error,
								duration: 1000,
								icon: 'none'
							})
							return 
						}
					}
				} else if (this.currentPage == 5) {
					for (let item of rule[5]) {
						
						if (!this.dataForm[item.modleName]) {
							wx.showToast({
								title: item.error,
								duration: 1000,
								icon: 'none'
							})
							check = false
							return check;
						}

						if (this.dataForm[item.modleName] == '1' && this[item.recordName].length == 0) {
							wx.showToast({
								title: item.error,
								duration: 1000,
								icon: 'none'
							})
							check = false
							return check;
						}

					}
					
				}
				this.currentPage < 5 ? this.currentPage++ : ''
				return check
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>