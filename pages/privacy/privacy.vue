<template>
	<view class="privacy-page">
		<view class="content">
			<view class="title">
				一、协议的范围
			</view>
			<view class="text">
				1.1 本协议是用户与ECBTI小程序（以下简称“本小程序”）之间关于使用本小程序服务所订立的协议。用户是指注册、登录、使用本小程序的个人或组织。
			</view>
			<view class="text">
				1.2 本服务是指本小程序根据本协议向用户提供的技术服务，包括但不限于认知行为疗法（CBTI）的评估、训练和相关支持服务。用户使用本小程序即成为本小程序的服务用户。
			</view>
			<view class="text">
				1.3 本小程序属于微信小程序平台的一部分，用户在使用本小程序时，应同时遵守《微信小程序平台服务协议》等相关协议规则。
			</view>
			<view class="title">
				二、用户个人信息保护
			</view>
			<view class="text">
				2.1 本服务将适用《微信小程序平台服务协议》关于“用户个人信息保护”的约定。
			</view>
			<view class="text">
				2.2 用户在申请本服务过程中，需要填写一些必要的信息，请保持这些信息的真实、准确、合法、有效并注意及时更新，以便本小程序向用户提供及时有效的帮助，或更好地为用户服务。根据相关法律法规和政策，请用户填写真实的身份信息。若用户填写的信息不完整或不准确，则可能无法使用本服务或在使用过程中受到限制。用户理解并同意，为了向用户提供服务，需要在法律允许的范围内展示用户的注册信息。
			</view>
			<view class="text">
				2.3 用户在申请和使用本服务过程中，需要按照要求准确填写、预留用户与服务用户、其他用户的直接数据通信方式，以便该等数据能准确地实现加密传输，确保相关用户的信息安全。否则，由此造成相关信息的泄漏、丢失、损坏等，均与本小程序无关。
			</view>
			<view class="text">
				2.4 如果用户的小程序收集、存储、处理或使用终端用户的任何个人数据或信息（相关术语定义见适用法律）：
			</view>
			<view class="text">
				(1) 用户应遵守（且不得在知情的情况下让终端用户违反）本协议以及任何适用的法律法规（包括与个人数据处理相关的法律法规）。用户应向本小程序提供所有必要的信息，以表明用户遵守与隐私及数据安全相关的法律法规以及本第2.4条的要求，包括促使开展合规审计及/或检查。
			</view>
			<view class="text">
				(2) 用户应遵守隐私政策，本小程序应清晰准确地向终端用户说明收集哪些个人数据、如何使用这些信息（包括用于广告）以及如何与本小程序及其它第三方共享这些信息（如任何该等第三方是否向终端用户提供内容服务，或是否从终端用户处收集信息）。该等隐私政策必须至少遵守并符合本协议项下载明的条款和条件及《微信隐私保护指引》（以适用者为准）。
			</view>
			<view class="text">
				(3) 如果本小程序在提供小程序服务时必须收集或处理任何用户的个人数据，本小程序应在请求获取该数据时征得用户对该等数据处理的明确同意。
			</view>
			<view class="text">
				(4) 除非另外（包括根据用户的隐私政策）获得了授权或同意，本小程序仅可在对完成相关小程序的运作或功能而言所属必要的情况下方可收集或处理用户的个人数据。本小程序不得在相关小程序之外或为任何其它目的而使用用户数据。在不限制用户在本协议或适用法律项下之义务的前提下，本小程序应向终端用户告知用户或用户代表直接或间接收集或处理数据的目的、范围和使用处理，从而向终端用户提供能够针对该等数据行使相关信息权的机制。
			</view>
			<view class="text">
				(5) 对于本小程序所提供的任何个人数据而言，本小程序仅可根据用户所提供的书面指示使用该等数据。如果用户要求删除该等数据，本小程序应尽合理努力删除该等数据。若因技术或法律原因无法完全删除数据（如数据已匿名化处理或涉及司法调查），服务提供方应在‌5个自然日‌内向用户说明理由。
			</view>
			<view class="text">
				(6) 本小程序应在技术和组织层面采取适当的安全措施，以防止用户数据被擅自访问或使用，还应定期检查这些措施的有效性。在收集了用户数据之后，本小程序必须采取合理的措施，以防止该等数据被盗或泄露。
			</view>
			<view class="text">
				(7) 本小程序应向用户提供修改和删除其数据的途径，如果任何用户需要删除其用户数据，可自行予以删除，本小程序则应确保相关的数据已被完全删除且无法恢复。若因技术或法律原因无法完全删除数据（如数据已匿名化处理或涉及司法调查），服务提供方应在‌5个自然日‌内向用户说明理由。
			</view>
			<view class="text">
				(8) 用户应协助本小程序处理终端根据适用的数据保护和隐私法律行使其权利的请求。
			</view>
			<view class="text">
				9) 未经用户事先书面同意，本小程序不得将用户提供的任何个人数据转移到任何其它国家或传送给任何其它人士，且任何该等转移或传送均仅可在适用的数据和隐私法律许可的情况下进行。
			</view>
			<view class="text">
				2.5 未经用户同意，本小程序不得收集、处理、存储、抓取、获得或要求任何终端用户提供任何数据，包括但不限于微信及/或其服务平台上含有的信息，如用户数据等。本小程序和用户不得使用任何违反本协议或适用法律法规而获得的数据，也不得将任何该等数据提供给任何第三方（包括小程序的任何其他客户）。
			</view>
			<view class="text">
				2.6 未经用户事先书面同意，本小程序不得将任何就微信或小程序而获得的数据公开或私下用于任何促销、营销或广告活动。
			</view>
			<view class="text">
				2.7 如果用户认为（由本小程序全权酌情判断）收集、处理或使用任何与用户相关之信息的方式可能会损害或不利地影响到用户体验，本小程序应根据要求，及时删除相关的数据，并不再收集、处理或使用该等数据。如果用户要求删除任何用户个人数据，本小程序应(i)对相关的数据进行匿名化处理，从而使该等数据不再是个人数据（定义见适用的数据和隐私法律），或(ii)永久性地删除相关的数据或使该等数据不再可读。本小程序必须根据用户的要求，向用户提供相关的个人数据已被匿名处理或删除的书面确认，但涉及司法调查的除外。
			</view>
			<view class="text">
				2.8 用户可在任何时间限制或阻止本小程序获取用户信息及/或微信开放平台内处理或存储的任何其它数据。
			</view>
			<view class="text">
				2.9 除非相关的数据是本小程序根据法律所拥有的，任何数据、用户数据或微信内处理或存储的其它数据中的一切权利都归用户所有，这些数据都属商业敏感信息，且都被视为本小程序的商业机密。本小程序应确保能访问到个人数据的所有人都受保密义务约束。未经用户序事先书面同意，本小程序不得将前述数据用于本协议所载目的之外的任何其它目的，亦不得向任何第三方披露该等信息。
			</view>
			<view class="text">
				2.10 一旦用户出于任何原因不再使用小程序，或本小程序出于任何原因不再提供与小程序相关的服务，本小程序应删除就此及小程序而获得的所有数据（包括备份的数据），且即刻起不再以任何方式使用该等数据。
			</view>
			<view class="text">
				2.11 本小程序应采取合理且安全的技术措施，保障因小程序而保存在本小程序服务器上的任何信息的安全。因用户自己或用户代表的行为（包括但不限于自行安装的软件、加密措施或其它安全措施）而产生的所有后果均由用户自行承担。一旦发生任何擅自访问或使用个人数据的情况，本小程序应及时向终端用户及（如适用）相关的数据保护机构汇报。
			</view>
			<view class="text">
				2.12 为向所有用户提供更优质的服务，本小程序可能会对用户的昵称、头像、认证信息、小程序页面等内容在法律允许的范围内进行展示或使用，包括但不限于提供索引、链接等服务。用户清楚理解并知悉，如用户认为本小程序页面因含有用户个人信息、通信信息、商业秘密等，应当对该小程序页面采取拒绝授权等防止措施。本小程序将尽可能采用互联网行业通用的方式，以使用户的防止措施有效实现。
			</view>
			<view class="title">
				三、服务条款
			</view>
			<view class="text">
				3.1 用户有权根据本协议的约定和本小程序的规定，使用本小程序提供的相关服务。
			</view>
			<view class="text">
				3.2 用户有义务遵守国家法律法规以及本小程序的相关规定，不得利用本小程序从事非法活动。
			</view>
			<view class="text">
				3.3 用户在使用本小程序过程中，应遵守以下规则：
			</view>
			<view class="text">
				(1) 不得发布违法、违规、虚假、不道德等信息，不得侵犯他人权益。
			</view>
			<view class="text">
				(2) 不得利用本小程序进行任何形式的非法活动，包括但不限于欺诈、骚扰、侵犯他人隐私等。
			</view>
			<view class="text">
				(3) 不得干扰本小程序的正常运行，不得进行任何形式的网络攻击或破坏。
			</view>
			<view class="text">
				3.4 本小程序有权在必要时修改本协议。用户可以在相关服务页面查阅最新版本的条款。协议条款发生重大变更（如服务范围调整、数据使用规则变更等）时，如果用户继续使用本小程序服务，即视为用户已接受修改后的协议。用户若不同意修改后的协议，
			</view>
			<view class="title">
				四、用户投诉与争议解决
			</view>
			<view class="text">
				若用户与服务提供方因本协议产生争议，双方应优先通过友好协商解决；协商不成的，任何一方可向‌服务提供方所在地有管辖权的人民法院‌提起诉讼。
			</view>
			<view class="title">
				五、其他
			</view>
			<view class="text">
				鉴于本服务将涉及微信小程序平台的技术开发，可能会不定期邀请部分用户参与内部测试。若用户被邀请且同意参与内部测试，未经本小程序书面同意，请勿向任何第三方以任何形式披露、展示、传递涉及本次内测的相关信息、资料。否则，本小程序可能会取消用户本次及其他内测资格等，由此造成本小程序的损失，用户也应一并赔偿。
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		}
	}
</script>

<style lang="less">
	.privacy-page {
		display: flex;
		flex-direction: column;

		.bg-container {
			height: 220px;

			.bg {
				width: 100%;
				position: absolute;
				z-index: 1;
			}

			.header {
				position: relative;
				z-index: 2;
			}
		}

		.content {
			border-radius: 10px 10px 0 0;
			background-color: #fff;
			margin-top: -23px;
			flex: 1;
			position: relative;
			z-index: 2;
			padding: 20px 20px;
			box-sizing: border-box;
			overflow-y: auto;
			.title {
				font-size: 16px;
				font-family: PingFang SC;
				font-weight: 600;
				color: #333333;
				margin: 11px;
			}

			.time {
				font-size: 14px;
				font-family: PingFang SC;
				font-weight: 400;
				color: #999999;
				text-align: center;
				margin-bottom: 10px;
				border-bottom: 1px dotted #9f9f9f;
			}

			.text {
				padding-top: 10px;
				font-size: 14px;
				text-indent: 2em;
				color: #333333;
			}
		}
	}
</style>