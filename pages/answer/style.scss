.answer-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .answerHeader {
    display: flex;
    width: 100%;
    flex-direction: column;
    .answerHeaderTop {
      display: flex;
      width: 100%;
      height: 52upx * 2;
      flex-direction: row;
      // justify-content: space-between;
      align-items: center;
      .answerNum {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        flex: 1;
        .currentPage {
          font-size: 18upx * 2;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #7f8fe9;
          line-height: 25upx * 2;
        }
        .total {
          font-size: 12upx * 2;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #666666;
          line-height: 21upx * 2;
        }
      }
      .answerVideo {
        display: flex;
        font-size: 12upx * 2;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 17upx * 2;
        flex-direction: row;
      }
      .checked {
        display: flex;
        width: 37upx * 2;
        height: 18upx * 2;
        transform: scale(0.4);
      }
    }

    .answerTitle {
      font-size: 20upx * 2;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 28upx * 2;
    }
  }
  .answerContentContainer {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 40upx;
    padding: 0 15upx * 2;
    font-weight: 500;
    .answerOptionsContainer > view {
      // border-top: 2upx solid rgb(242, 242, 242);
      display: flex;
      width: 100%;
      height: 40upx * 2;
      align-items: center;
      justify-content: center;
      padding: 10upx;
      background: #f0f0f0;
      border-radius: 100px;
      margin-bottom: 12upx * 2;

      font-size: 14upx * 2;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 20upx * 2;

      &.current {
        color: #fff;
        background: #7f8fe9;
      }
    }
    .answerContent {
      color: #15b2f5;
      font-size: 35upx;
      padding: 10upx;
      // line-height: 140upx;
      view {
        font-size: 38rpx;
      }
    }
  }

  .answerActions {
    display: flex;
    margin-bottom: 40 upx;
    width: 100%;
    height: 50upx * 2;
    flex-direction: row;
    align-items: flex-start;
    .button {
      display: flex;
      width: 50%;
      height: 20upx * 2;
      justify-content: center;
      align-items: center;
      font-size: 14upx * 2;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      line-height: 20upx * 2;
    }
    .before {
      color: #7f8fe9;
      border-right: 1upx * 2 solid #d5d5d5;
    }
    .left-none {
      color: #999999;
      border-right: 1upx * 2 solid #d5d5d5;
    }
    .submit {
      color: #7f8fe9;
    }
    .right-none {
      color: #999999;
    }
  }
}
