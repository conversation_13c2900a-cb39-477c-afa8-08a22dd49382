<template>
	<view class="answer-page">
		<!--文字答题-->
		<view class="answerContentContainer">

			<view class="answerHeader">
				<view class="answerHeaderTop">
					<view class="answerNum">
						<view class="currentPage">{{currentPage == total ? total : currentPage +1}}</view>
						<view class="total">/{{total}}</view>
					</view>
				</view>
				<view class="answerTitle" v-html="currentAnswer.title"></view>
			</view>
			<view class="answerOptionsContainer">
				<view v-for="(item, index) in currentAnswer.psOptions" :class="'answerOptions' + (clickOptionId == item.id ? ' current' : '' )"
					@click="selectOption(item.id , currentAnswer.sort,item)" :key="index">
					{{item.content}}
				</view>

			</view>
		</view>
		<view class="answerActions">
			<view :class="firstQuestion ? 'button left-none': 'button before'"
				@click="backBeforeAnswer(currentAnswer.sort)">上一题</view>
			<view :class="lastQuestion ? 'button submit': 'button right-none'" @click="sbumitAnswer()">提交</view>
		</view>
	</view>
</template>

<script>
	import {
		loadAnswerData, addOption, uploadResult
	} from '@/assets/api/index.js'
	export default {
		data() {
			return {
				backDisabled: true,
				submitDisabled: true,
				firstQuestion: true,
				lastQuestion: false,
				currentPage: 0,
				total: 0,
				clickOptionId: "",
				resultId: "",
				measureTaskId: '',
				currentAnswer: {},
				currentAnswerTitle: "",
				currentAnswerOptions: [],
				list: [],
				measureId: "",
				measureName: "",
				measureDescription: "",
				startTime: 0,
				totleTime: 0,
				url: {
					addOption: 'result/addOption',
					uploadResult: 'result/uploadResult',
					getExecuteDetail: 'scheme/getExecuteDetail'
				},
			};
		},
		onLoad(parameter) {
			let {
				reportId,
				measureTaskId
			} = parameter;
			this.resultId = reportId;
			this.measureTaskId = measureTaskId;
			this.startTime = new Date().getTime();
		},
		watch: {
			currentPage(newVal) {
				this.firstQuestion = (newVal == 0);
				this.lastQuestion = (newVal == this.total);
			}
		},
		mounted() {
			this.loadAnswerData();
		},
		methods: {
			loadAnswerData() {
				loadAnswerData({id: this.resultId}).then(result => {
					let data = result;
					if (data) {
						let {
							quesionCount,
							quesionList,
							measure,
							dxResult
						} = data;
						if (quesionList) {
							let total = quesionList.length;
							if (quesionCount == total) {
								this.currentAnswer = quesionList[quesionCount - 1];
								this.clickOptionId = quesionList[quesionCount - 1].optionId;
							} else {
								this.currentAnswer = quesionList[quesionCount];
							}
							this.list = quesionList;
							this.total = total;
							this.currentPage = quesionCount;
						}
						this.resultId = dxResult.id;
						this.measureId = measure.id;
						this.measureName = measure.name;
						this.measureDescription = measure.description;
					}
				});
			},
			selectOption(id, sort, item) {
				if (!sort) {
					uni.showModal({
						title: '错误',
						content: "数据异常，请联系管理员！",
						showCancel: false,
						success: function(res) {}
					});
				}
				this.clickOptionId = id;
				let afterData = this.list[sort];
				let beforeData = this.list[sort - 1];
				beforeData.optionId = id;
				let afterSort = sort;
				let time = (new Date().getTime() - this.startTime).toFixed(0);
				
				let params = {
						resultId: this.resultId,
						measureId: this.measureId,
						optionId: id,
						questionId: beforeData.id,
						totalTime: time,
						time,
					}
				addOption(params).then(result => {
					setTimeout(() => {
						if (afterData) {
							this.currentAnswer = afterData;
						}
						this.currentPage = afterSort;
						if (afterData && afterData.optionId) {
							this.clickOptionId = afterData.optionId;
						}
					}, 200)
				});
				this.startTime = new Date().getTime();
			},
			backBeforeAnswer(sort) {
				if (this.currentPage <= 0) {
					return;
				} else {
					if (sort) {
						let beforeData = this.list[sort - 2];
						this.currentAnswer = beforeData;
						this.currentPage = sort - 2;
						this.clickOptionId = this.list[sort - 2].optionId;
					}
				}

			},
			sbumitAnswer() {
				if (this.currentPage !== this.total) {
					return;
				}
				let params = {
					resultId: this.resultId,
					measureTaskId: this.measureTaskId
				}
				uploadResult(params).then(result => {
					if (result) {
						if (this.measureTaskId) {
							uni.navigateBack({
								delta: 2
							})
						} else {
							uni.redirectTo({
								url:"/pages/summary/summary?reportId=" + result.id,
							})
						}
					} else {
						uni.showModal({
							title: '提示',
							content: result.message,
							showCancel: false,
							success: function(res) {}
						});
					}
				});
			},
			// 强制模式获取正在执行的计划
			getExecuteDetail() {
				let that = this
				this.$request({
					url: that.url.getExecuteDetail
				}).then(result => {
					if (result && result.result) {
						if (result.result.length > 0) {
							uni.setStorageSync('CompulsoryPlan','1')
							let item = result.result[0]
							if (item.type == 'measureTask') {
								// 量表
								uni.redirectTo({
								  url: '/pages/beginMeasure/index?reportId=' + item.resultId + "&measureTaskId=" + item.id + "&type=2&status=" + item.status,
								});
							} else if (item.type == 'schemeTrain') {
								// 训练  
								uni.redirectTo({
								  url: "/pages/trainDetail/index?train=" + encodeURIComponent(JSON.stringify(item)) + '&isScheme=true'
								})
							} else if (item.type == 'schemeSub') {
								// 课程  
								uni.redirectTo({
								  url: "/pages/programDetail/programDetailPlan?schemeId=" + item.schemeId + '&id=' + item.id,
								})
							}
						} else {
							uni.removeStorageSync('CompulsoryPlan')
							uni.showModal({
							  title: '温馨提示',
							  content: `您已完成今天的学习内容，不要忘记填写睡眠日记哦`,
								showCancel: false,
							  confirmText: '返回首页',
							  success: (res) => {
							    if (res.confirm) {
							      uni.navigateBack()
							    }
							  }
							});
						}
					}
				})
			},
		}
	}
</script>


<style lang="scss">
	@import "./style.scss";
</style>
