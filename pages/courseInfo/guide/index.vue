<template>
	<view class="guide-page" :style="{background:form.bgColor }">
		<view class="fireworks">
			<l-confetti ref="confettiRef" @done="done"></l-confetti>
		</view>
		<view class="img">
			<image :src="`https://cbti.zhisongkeji.com/uniapp-static/${form.cover}`" mode="widthFix"></image>
		</view>
		<image class="imgTitle" :src="`https://cbti.zhisongkeji.com/uniapp-static/${form.imgTitle}`" mode="widthFix">
		</image>
		<view class="next">
			{{form.nextTitle}}
		</view>
		<view class="btn" :style="{background:form.btnColor }" @click="goPage()">
			{{form.btnText}}
		</view>
	</view>
</template>

<script>
	import {
		getCourseInfo,
		finishCourseInfo,
		recordProcess,
		getCurrentSchemeGroupInfo
	} from '@/assets/api/index.js'
	import {
		formatDate
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				course: [],
				guideHash: {
					'course': {
						cover: 'guide3.png',
						imgTitle: 'guideTitle1.png',
						nextTitle: '接下来学习剩余的课程吧～',
						btnText: '继续学习',
						bgColor: '#FFF8E8',
						btnColor: '#FF9500'
					},
					'measure': {
						cover: 'guide2.png',
						imgTitle: 'guideTitle2.png',
						nextTitle: '接下来进入量表作答环节',
						btnText: '开始量表作答',
						bgColor: '#E4F2F1',
						btnColor: '#018D73'
					},
					'relax': {
						cover: 'guide1.png',
						imgTitle: 'guideTitle3.png',
						nextTitle: '接下来进入到放松环节',
						btnText: '开始放松环节',
						bgColor: '#FFF8E8',
						btnColor: '#FF9500'
					},
					'review': {
						cover: 'guide4.png',
						imgTitle: 'guideTitle4.png',
						nextTitle: '接下来进入知识回顾环节',
						btnText: '开始知识回顾',
						bgColor: '#E4F2F1',
						btnColor: '#018D73'
					}
				},
				detail: {},
				form: {
					cover: 'guide1.png',
					imgTitle: 'guideTitle1.png',
					nextTitle: '接下来学习剩余的课程吧～',
					btnText: '继续学习'
				}
			}
		},
		onLoad({
			id,
			schemeId,
			elementId
		}) {
			// elementId = "1842902638477705217"
			// id = "1865769840000581634"
			// schemeId = "1865769839874752514"
			// type: 6
			this.id = id
			this.schemeId = schemeId
			this.elementId = elementId

			this.startTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
			this.getInfo(id)

		},
		methods: {

			getInfo(id) {
				getCourseInfo({
					"id": id
				}).then(result => {
					//course 课程 measure 量表 relax 放松训练 review 知识回顾
					this.detail = result
					let {
						content
					} = result
					this.form = Object.assign({}, this.guideHash[content])
					uni.setNavigationBarColor({
						frontColor: '#ffffff',
						backgroundColor: this.form.bgColor,
						animation: {
							duration: 400,
							timingFunc: 'easeIn'
						}
					})
					setTimeout(() => {
						this.run()
					}, 1000)
					this.finish()

				})
			},
			finish() {
				finishCourseInfo(this.id).then(result => {
					this.getPlanInfo()
				})
			},
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					this.course = result
					let {
						currentPaPatientScheme,
					} = result

					this.unfinish = currentPaPatientScheme.elementList.filter(item => {
						return !item.locked && ((item.type !== 4 &&item.status != 1) || (item.type == 4 &&item.status != 2)) //未锁且未完成的
					})

				})
			},
			goPage() {
				let item = this.unfinish[0]
				// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
				let url = ''
				switch (item.type) {
					case 1:
						url = '/pages/courseInfo/article/article?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 2:
						url = '/pages/iframe?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
						break;
					case 3:
						url = '/pages/courseInfo/audio/audio?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 4:
						url = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item.id +
							"&type=2&status=" + item.dxResultStatus
						break;
					case 5:
						url = '/pages/courseInfo/questionnaire/questionnaire?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 6:
						url = '/pages/courseInfo/guide/index?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
				}
				if (url) {
					uni.redirectTo({
						url: url
					})
				}
			},
			run() {
				console.log('run', this.$refs.confettiRef)
				this.$refs.confettiRef.play({
					particleCount: 100,
					spread: 70,
					origin: {
						y: 0.6
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.guide-page {
		max-width: 100%;
		height: 100vh;
		padding: 16px;
		display: flex;
		flex-direction: column;
		align-items: center;
		// background: #FFF8E8;

		.fireworks {
			width: 100%;
			height: 324px;
			position: fixed;
			z-index: 1;
			top: 0;
			left: 0;
		}

		.img {
			width: 100%;
			height: 324px;
			display: flex;
			justify-content: center;
			align-items: flex-end;

			>image {
				width: 280px;
				height: 280px;
			}
		}

		.imgTitle {
			width: 307px;
		}

		.next {
			font-size: 14px;
			color: #636366;
			line-height: 17px;
		}

		.btn {
			position: fixed;
			bottom: 50px;
			width: calc(100% - 32px);
			height: 48px;
			left: 16px;
			// background: #FF9500;
			border-radius: 24px;
			font-size: 16px;
			color: #FFFFFF;
			line-height: 48px;
			text-align: center;
		}
	}
</style>