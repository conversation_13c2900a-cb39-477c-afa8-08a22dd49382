<template>
	<view class="train-page " v-if="detail.titleImg"
		:style="{backgroundImage:'url(https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/'+detail.titleImg+')'}">
		<view class="train" @touchend="touchend">
			<view class="audiotbox" id="audiotbox">
				—<label>{{detail.title ? detail.title :'' }}</label>—
			</view>
			<view class="audiombox flex-col" id="audiombox">
				<view class="slider">
					<view class="slider-time">
						<view class="slider-time-container" v-if="durationAudio">
							{{Math.floor(currentTime / 60)}}:{{currentTime % 60}}
							/{{Math.floor(durationAudio / 60)}}:{{durationAudio % 60}}
						</view>
					</view>
					<slider :value="percentAudio" max="100" block-color="#5856D6" block-size="12" activeColor="#5856D6">
					</slider>
				</view>
				<view class="audiombox-content flex-row">
					<image class="audiombox-play" @click="pauseAudio()" v-if="isPlay"
						src="https://cbti.zhisongkeji.com/uniapp-static/palying.png" mode=""></image>
					<image class="audiombox-play" @click="playAudio()" v-else
						src="https://cbti.zhisongkeji.com/uniapp-static/stopPlay.png" mode="">
					</image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCourseaudioInfo,
		finishCourseaudioInfo,
		recordProcess,
		getCurrentSchemeGroupInfo
	} from '@/assets/api/index.js'
	import {
		useStore
	} from 'vuex'
	import {
		formatDate
	} from '@/utils/index.js'

	import navbar from '@/components/navbar.vue'
	import {
		List
	} from 'echarts';
	const AudioPlayerCcContainer = {};
	let innerAudioContext = uni.createInnerAudioContext(); //引用组件


	export default {
		components: {
			navbar
		},
		data() {
			const store = useStore();
			return {
				uuid: 0,
				currentTime: 0,
				percentAudio: '',
				durationAudio: 0,
				detail: {},
				user: store.state.user,
				navBarHeight: store.state.navBarHeight || 87,
				glideDirection: null,
				isPlay: false, // 是否播放
				isloop: false,
				scrollview: 'audiotbox',
				id: '',
				schemeId: '',
				elementId: '',
				unfinish: [],
				ready: false,
				srcChange: false,
				init: false,
			}
		},
		onLoad({
			id,
			schemeId,
			elementId
		}) {
			// elementId = "1638822612322652162",
			// 	id = "1854411622259302401",
			// 	schemeId = "1854411622003449858",
			// uni.showLoading({
			// 	title: '音频文件加载中',
			// 	mask: true,
			// });
			this.id = id
			this.schemeId = schemeId
			this.elementId = elementId

			this.startTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
			this.createUuid();
			this.getInfo(id, 'init')

		},
		onUnload() {
			console.log('onUnload')
			if (this.percentAudio != 100) {
				this.radioPlayProgress()
			}
			innerAudioContext.stop();
			// innerAudioContext.destroy()
		},
		methods: {
			//创建唯一uuid
			createUuid() {
				let s = [],
					hexDigits = '0123456789abcdef',
					i;

				for (i = 0; i < 36; i++) {
					s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
				}

				s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
				s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
				s[8] = s[13] = s[18] = s[23] = '-';

				this.uuid = s.join('');
			},
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					this.course = result
					let {
						currentPaPatientScheme,
					} = result

					this.unfinish = currentPaPatientScheme.elementList.filter(item => {
						return !item.locked && ((item.type !== 4 && item.status != 1) || (item.type == 4 &&
							item.status != 2)) //未锁且未完成的
						// return !item.locked && item.status != 1 && item.type != 2 //未锁且未完成的且没有视频的
					})
					if (this.unfinish.length > 0) {
						let that = this
						uni.showModal({
							title: '提示',
							content: '此课程已完成,是否继续学习?',
							cancelText: '否',
							confirmText: '是',
							success: function(res) {
								if (res.confirm) {
									that.currentTime = 0
									that.durationAudio = 0
									that.isPlay = false
									that.goPage()
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
									uni.navigateBack({})
								}
							}
						})
					} else {
						uni.navigateBack({})
					}
				})
			},
			recordProcess() {
				if (!this.schemeId) return
				let params = {
					elementType: 3, // 1文章 2视频 3放松训练'
					"elementId": this.elementId, //对应的素材id(material_id)或训练id(train_id)
					"startTime": this.startTime, // 学习开始时间
					"endTime": this.endTime, // 学习结束时间
					"playTime": this.currentTime, // 学习总时间 秒
					"playProgress": this.percentAudio, // 学习进度
					status: this.percentAudio == 100 ? 1 : 0, // 状态(0_未完成，1_已完成)，当playProgress=100时表示已完成
					'paPatientSchemeld': this.schemeId //患者方案主表id
				}
				recordProcess(params).then(result => {
					if (this.percentAudio == 100) {
						this.getPlanInfo()
					}


				})
			},
			getInfo(id, type) {
				getCourseaudioInfo({
					"id": id
				}).then(result => {
					this.detail = result
					uni.setNavigationBarTitle({
						title: result.title
					})
					if (type == 'init') {
						this.audioInit()
					}
				})
			},
			radioPlayProgress() {
				if (!this.schemeId) return
				let progress = (innerAudioContext.currentTime / innerAudioContext.duration).toFixed(2) * 100
				finishCourseaudioInfo({
					id: this.id,
					radioPlayProgress: this.percentAudio,
					radioPlayTime: this.currentTime
				}).then(result => {

					this.endTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
					this.recordProcess()
					// uni.showToast({
					// 	title: '上传成功',
					// 	duration: 2000,
					// 	icon: 'none'
					// });
				})
			},
			finish(id) {
				finishCourseInfo(this.id).then(result => {})
			},
			//定时播放
			startCountdown() {
				this.durationSet = setInterval(() => {
					if (this.timer > 0) {
						this.timer -= 1000;
					} else {
						this.pauseAudio(); // 倒计时结束，播放音频
						clearInterval(this.durationSet); // 清除计时器
						this.timer = null;
						this.durationSet = null
					}
				}, 1000);
			},
			formatSeconds(seconds) {
				var date = new Date(null);
				date.setSeconds(seconds / 1000);
				var m = date.getMinutes().toString().padStart(2, '0');
				var s = date.getSeconds().toString().padStart(2, '0');
				return m + ':' + s;
			},
			audioInit() {
				if (innerAudioContext != undefined) {
					innerAudioContext.stop();
				}
				innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.src = this.detail.voicePath
				innerAudioContext.autoplay = false
				innerAudioContext.loop = false
				innerAudioContext.onCanplay(() => {
					console.log('1.初始化加载事件');
				});
				//开始播放
				innerAudioContext.onPlay(() => {
					console.log('开始播放');
					if (!this.isPlay) {
						this.playAudio()
					}

					for (let uuid in AudioPlayerCcContainer) {
						if (this.uuid !== uuid) {
							AudioPlayerCcContainer[uuid] && AudioPlayerCcContainer[uuid].pause();
						}
					}
				});
				//音频播放进度更新事件
				innerAudioContext.onTimeUpdate((res) => {
					if (this.currentTime == 0) {
						uni.hideLoading();
					}
					this.durationAudio = Math.round(innerAudioContext.duration);
					this.currentTime = Math.round(innerAudioContext.currentTime);
					this.percentAudio = Math.round((innerAudioContext.currentTime / this.durationAudio) * 100)

				})
				//onSeeked
				innerAudioContext.onSeeked(() => {
					console.log('onSeeked---', innerAudioContext.currentTime)
				})
				//音频暂停事件
				innerAudioContext.onPause((e) => {
					console.info('播放暂停');
					if (this.isPlay) {
						this.pauseAudio();
					}
				});
				//音频停止事件
				innerAudioContext.onStop((e) => {
					console.info('播放停止');
				});

				innerAudioContext.onEnded(() => {
					this.currentTime = Math.round(innerAudioContext.duration)
					this.percentAudio = 100
					console.log('onEnded----')
					this.pauseAudio() //暂停
					this.radioPlayProgress()
				})

				//异常监听
				innerAudioContext.onError((err) => {
					console.info(err);

					this.ready = false;
					let errorMap = {
						10001: "系统错误",
						10002: "网络错误",
						10003: "文件错误",
						10004: "格式错误",
						"-1": "文件加载失败"
					}

					let errorMsg = errorMap[err] || err

					uni.showToast({
						title: errorMsg,
						icon: 'error'
					});
				});

				this.init = true;

			},
			goPage() {
				let item = this.unfinish[0]
				let {
					url,
					id,
					paPatientSchemeId,
					elementId
				} = item
				console.log('item', item)
				// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
				let path = ''
				switch (item.type) {
					case 1:
						path = '/pages/courseInfo/article/article?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 2:
						path = '/pages/iframe?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 3:
						//放松训练

						this.currentTime = 0
						this.durationAudio = 0
						this.id = id
						this.schemeId = paPatientSchemeId
						this.elementId = elementId
						this.getInfo(id)
						innerAudioContext.src = url
						// url = '/pages/courseInfo/audio/audio?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
						// 	'&elementId=' + item.elementId
						break;
					case 4:
						path = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item
							.id +
							"&type=2&status=" + item.dxResultStatus
						break;
					case 5:
						path = '/pages/courseInfo/questionnaire/questionnaire?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 6:
						path = '/pages/courseInfo/guide/index?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
				}
				if (path) {
					uni.redirectTo({
						url: path
					})
				}
			},
			playAudio() {
				if (this.currentTime > 0) {
					innerAudioContext.seek(this.currentTime);
				} else {
					uni.showLoading({
						title: '音频文件加载中',
						mask: true,
					});
				}
				this.isPlay = true
				innerAudioContext.play();

			},
			pauseAudio() {
				this.isPlay = false
				innerAudioContext.pause();

			},
			loopAudio() {
				this.isloop = !this.isloop
				innerAudioContext.loop = this.isloop
			},
			confirmTimer(value) {
				this.startCountdown()
				this.timerset ? clearTimeout(timer) : ''
				this.timer = value

				this.showTimer = false
				if (this.isPlay) {
					this.timerset = setTimeout(function() {
						innerAudioContext.pause()
					}, value);
				}
			},
		},
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>