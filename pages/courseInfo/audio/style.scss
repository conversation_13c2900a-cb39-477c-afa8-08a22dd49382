.train-page {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	// background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
	background-size: cover;
	background-repeat: no-repeat;
	padding-bottom: 50px;
	.train{
		  background-color: RGBA(0, 0, 0, 0.1);
	}
	.audiotbox {
		width: 100%;
		min-height: calc(100vh - 186px);
		font-weight: 300;
		font-size: 72rpx;
		color: #ffffff;
		writing-mode: vertical-rl;
		letter-spacing: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		> label {
			max-height: 60%;
		}
	}

	.audiombox {
		width: 100%;
		height: 136px;
		flex-direction: column;
		align-items: center;
		.slider {
			width: 100%;
			display: flex;
			flex-direction: column;
			color: #ffffff;

			&-time {
				height: 22upx;
				padding: 0 18px;
				&-container {
					display: flex;
				}
			}
		}
		&-content {
			width: 100%;
			justify-content: space-around;
			align-items: center;
			.audiombox-item {
				min-width: 140rpx;
				align-items: center;
				&-icon {
					width: 136rpx;
					height: 136rpx;
				}
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
			}
			.audiombox-play {
				width: 160rpx;
				height: 160rpx;
			}
		}

		&-up {
			width: 60rpx;
			height: 16rpx;
			margin: 26rpx auto;
		}
	}
}
.timer {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100vh;
	z-index: 1;
	background-color: rgba(0, 0, 0, 0.56);
	align-items: center;
	justify-content: center;
	&-label {
		font-weight: bold;
		font-size: 32rpx;
		color: #ffffff;
		padding: 56rpx 0;
	}
	.titem {
		width: 562rpx;
		height: 96rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #ffffff;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		&-label {
			flex: 1;
			text-align: center;
		}
		.check {
			font-weight: bold;
			color: #ffd60a;
		}
		&-icon {
			position: absolute;
			width: 32rpx;
			height: 32rpx;
			right: 0;
		}
	}
}
