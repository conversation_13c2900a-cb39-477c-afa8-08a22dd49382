<template>
	<view class="process-page">
		<view class="form">
			<template v-for="(item,index) of list">
				<view class="item" :index="index">
					<view class="item-label">
						{{item.questionNo}}、{{ item.label }}
					</view>
					<view class="item-content" v-if="item.type == '多选'">
						<view class="check" :key="index">
							<checkbox-group @change="(e)=>{checkChange(item.questionNo,e)}">
								<label class="check-item" v-for="(child, _i) in  item.options" :key="child.label">
									<view>
										<checkbox class="checkIcon" :value="child.id" :checked="child.checked" />
									</view>
									<view>{{child.label}}</view>
								</label>
							</checkbox-group>
						</view>
					</view>
					<view class="item-content" v-else-if="item.type == 1">
						<radio-group @change="(e)=>{radioChange(item.questionNo,e)}" class="radioClomun">
							<label v-for="(child, _i) in item.options" :key="child.key">
								<view>
									<radio :value="child.key" :checked="child.key == dataForm[item.questionNo]" />
								</view>
								<view class="text">{{child.label}}</view>
							</label>
						</radio-group>
					</view>
				</view>
			</template>
			<!-- </view> -->
		</view>
		<view class="btn">
			<div class="next-btn" @click="submit">提交问卷</div>
		</view>
	</view>
</template>

<script>
	import {
		getQuestionnaireInfo,
		finishQuestionnaireInfo,
		getCurrentSchemeGroupInfo
	} from '@/assets/api/index.js'
	export default {
		data() {
			return {
				id: '',
				schemeId: '',
				elementId: '',
				detail: {},
				list: [],
				unfinish: []
			}
		},
		onLoad({
			id,
			schemeId,
			elementId
		}) {
			// elementId = "62b8df599c5a11efbef800163e08bbc9"
			// id = "1854411622313828354"
			// schemeId = "1854411622003449858"
			this.getInfo(id)
			this.id = id
			this.schemeId = schemeId
			this.elementId = elementId

		},
		methods: {
			getInfo(id) {
				getQuestionnaireInfo({
					"id": id
				}).then(result => {
					this.detail = result
					uni.setNavigationBarTitle({
						title: result.questionnaireTitle
					})
					this.getQuestion(result.questionList)
				})
			},
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					this.course = result
					let {
						currentPaPatientScheme,
					} = result

					this.unfinish = currentPaPatientScheme.elementList.filter(item => {
						return !item.locked && ((item.type !== 4 &&item.status != 1) || (item.type == 4 &&item.status != 2)) //未锁且未完成的
						// return !item.locked && item.status != 1 && item.type != 2 //未锁且未完成的且没有视频的
					})
					if (this.unfinish.length > 0) {
						let that = this
						uni.showModal({
							title: '提示',
							content: '此课程已完成,是否继续学习?',
							cancelText: '否',
							confirmText: '是',
							success: function(res) {
								if (res.confirm) {
									that.goPage()
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
									uni.navigateBack({})
								}
							}
						})
					} else {
						uni.navigateBack({})
					}
				})
			},
			goPage() {
				// let unfinish = [{
				// 	elementId: "62b8df599c5a11efbef800163e08bbc9",
				// 	id: "1854411622313828354",
				// 	paPatientSchemeId: "1854411622003449858",
				// 	type: 5,
				// }, {
				// 	elementId: "1842902638477705217",
				// 	id: "1865769840000581634",
				// 	paPatientSchemeId: "1865769839874752514",
				// 	type: 6
				// }]
				// let item = unfinish[0]

				let item = this.unfinish[0]
				// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
				let url = ''
				switch (item.type) {
					case 1:
						url = '/pages/courseInfo/article/article?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 2:
						url = '/pages/iframe?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 3:
						url = '/pages/courseInfo/audio/audio?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 4:
						url = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item
							.id +
							"&type=2&status=" + item.dxResultStatus
						break;
					case 5:
						url = '/pages/courseInfo/questionnaire/questionnaire?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 6:
						url = '/pages/courseInfo/guide/index?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
				}
				if (url) {
					uni.redirectTo({
						url: url
					})
				}
			},
			getQuestion(_data) { // 获取问题
				let _dataForm = {}
				for (let item of _data) {
					if (item.type == "1") {
						_dataForm[item.number] = ""
					}
					this.list.push({
						questionNo: item.number,
						id: item.id,
						type: item.type,
						label: item.title,
						options: item.optionList.map(child => {
							return {
								id: child.id,
								key: child.title,
								label: child.content,
							}
						}),
					})
				}
				this.dataForm = _dataForm

			},
			checkChange(key, e) {
				this.dataForm[key] = e.detail.value
			},
			radioChange(key, e) {
				this.dataForm[key] = e.detail.value
			},
			submit() {
				let check = true
				let _dataForm = Object.assign({}, this.dataForm)
				for (let [key, value] of Object.entries(_dataForm)) {
					if (_dataForm[key] == '' || _dataForm[key] == []) {
						uni.showToast({
							title: '第' + key + '题还未选择答案',
							icon: "none",
							duration: 1000,
							mask: true
						})
						check = false
						return
					}
				}
				if (check) {
					let params = {
						"id": this.id,
						"answerMap": _dataForm
					}
					finishQuestionnaireInfo(params).then(result => {
						uni.showToast({
							title: '提交成功',
							icon: "none",
							duration: 1000,
						})
						this.getPlanInfo()

					})
				}

			},
		}
	}
</script>

<style lang="scss">
	@import "./style.scss"
</style>