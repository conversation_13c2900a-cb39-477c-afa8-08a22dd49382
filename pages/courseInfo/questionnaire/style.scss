.process-page{
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow-y: scroll;
	background: rgba(247, 247, 247, 1);
	.form{
		flex: 1;
		width: calc(100% - 52upx);
		border-radius: 6upx*2;
		box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.06),inset 0px 1px 4px rgba(255, 255, 255, 1);
		backdrop-filter: blur(20upx*2);
		background:rgba(255,255,255,0.88);
		margin: 15upx*2 auto;
		padding: 0 16upx*2;
		// &-label{
		// 	width: 100%;
		// 	height: 64upx*2;
		// 	position: relative;
		// 	&-content{
		// 		display: flex;
		// 		justify-content: space-between;
		// 		margin: 0 16upx*2;
		// 		padding: 15upx*2 0;
		// 		border-bottom: 1upx solid rgba(230, 230, 230, 1);
		// 		.label{
		// 			flex: 1;
		// 			overflow: hidden;
		// 			display: flex;
		// 			align-items: center;
		// 			font-size: 15upx*2;
		// 			color: rgba(51, 51, 51, 1);
		// 			font-weight: bold;
					
		// 			&-num{
		// 				color: rgba(255, 255, 255, 1);
		// 				font-size: 12upx*2;
		// 				display: flex;
		// 				justify-content: center;
		// 				align-items: center;
		// 				width: 16upx*2;
		// 				height: 16upx*2;
		// 				background: linear-gradient(90deg, rgba(255, 164, 65, 1) 0%, rgba(255, 126, 5, 1) 100%);
		// 				border-radius: 8upx*2 8upx*2 0 8upx*2;
		// 				overflow: hidden;
		// 				margin-right: 8upx*2;
						
		// 			}
		// 		}
		// 		.msg{
		// 			color: rgba(153, 153, 153, 1);
		// 			font-size: 11upx*2;
		// 		}
				
		// 	}
		// }
		// &-label::before{
		// 	content: '';
		// 	position: absolute;
		// 	width: 4upx*2;
		// 	height: 18upx*2;
		// 	border-radius: 0 3upx*2 3upx*2 0;
		// 	left: 0;
		// 	top: 16upx*2;
		// 	background: linear-gradient(90deg, rgba(255, 164, 65, 1) 0%, rgba(255, 126, 5, 1) 100%);
		// }
		// &-content{
		// 	flex: 1;
		// 	height: 100%;
		// 	display: flex;
		// 	flex-direction: column;
		// 	overflow: hidden;
		// 	padding: 0 16upx*2;
			.item{
				display: flex;
				flex-direction: column;
				overflow: hidden;
				margin-top: 13upx*2;
				&-label{
					color: rgba(51, 51, 51, 1);
					font-size: 13upx*2;
					font-weight: bold;
				}
				&-content{
					display: flex;
					flex-direction: column;
					margin: 16upx*2 0;
					.radioClomun{
						padding: 0 10upx;
						display: flex;
						flex-direction: column;
						/* 自定义样式.... */
						 label{
							 display: flex;
							 width:100%;
							font-size: 13upx*2;
							font-weight: 400;
							line-height: 18upx*2;
							color: rgba(51, 51, 51, 1);
							 margin-bottom: 22upx*2;
							 radio .wx-radio-input {
							   height: 36rpx;
							   width: 36rpx;
							   margin-top: -4rpx;
							   border-radius: 50%;
							   border: 2rpx solid rgba(197, 197, 197, 1);
							   background: transparent;
							 }
							 
							 radio .wx-radio-input.wx-radio-input-checked::before {
							   border-radius: 50%; /* 圆角 */
							   width: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
							   height: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
							   line-height: 36rpx;
							   text-align: center;
							   font-size: 30rpx; /* 对勾大小 30rpx */
							   color: #fff; /* 对勾颜色 白色 */
							   background: #5E5CE6;
							   border: 2rpx solid #5E5CE6;
							   transform: translate(-50%, -50%) scale(1);
							   -webkit-transform: translate(-50%, -50%) scale(1);
							 }
							 
							 .text{
								color: rgba(51, 51, 51, 1);
								font-size: 13upx*2;
								line-height: 18upx*2;
							 }
						 }
								
					}
					.check{
						display: flex;
						flex-wrap: wrap;
						
						 label{
							display: flex;
							font-size: 13upx*2;
							font-weight: 400;
							line-height: 18upx*2;
							color: rgba(51, 51, 51, 1);
							/*checkbox选中后样式  */
							margin-bottom: 22upx*2;
							checkbox .wx-checkbox-input {
							width: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
							height: 36rpx;
							}
							/*checkbox选中后图标样式  */
							checkbox .wx-checkbox-input.wx-checkbox-input-checked {
								width: 36rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
								height: 36rpx;
								color: #fff;
								background: #FF7E06;
								border: 1rpx solid #5E5CE6;
							}
						 }
					}
				}
			}
		// }
	}
	.btn{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 17upx*2;
		margin-bottom: 28upx*2;
		.next-btn{
			flex: 1;
			height: 40upx*2;
			background: #5E5CE6;
			border-radius: 3upx*2;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(255, 255, 255, 1);
			font-size: 15upx*2;
		}
		.up-btn{
			width: 109px;
			height: 40px;
			background: rgba(255, 255, 255, 1);
			border: 0.5px solid #5E5CE6;
			border-radius: 3upx*2;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(255, 255, 255, 1);
			font-size: 15upx*2;
			color: rgba(255, 126, 6, 1);
			margin-right: 10upx*2;
		}
	}
		
	// .scroll{
	// 	flex: 1;
		
		
	// 	.banner{
	// 		width: 100%;
	// 		height: 199px;
	// 		background: url(https://oss.ihaier.vip/minipro/nav-bg.jpg); 
	// 		background-repeat: no-repeat;
	// 		background-size: 100% auto;
	// 		padding: 29upx*2 22upx*2;
	// 		display: flex;
	// 		flex-direction: column;
	// 		justify-content: flex-start;
	// 		&-label{
	// 			width: 148upx*2;
	// 			margin-bottom: 17upx*2;
	// 			font-size: 12upx*2;
	// 			line-height: 12upx*2;
	// 			color: rgba(255, 255, 255, 0.85);
	// 		}
	// 		&-text{
	// 			color: rgba(255, 255, 255, 1);
	// 			font-size: 17upx*2;
	// 			line-height: 17upx*2;
	// 		}
	// 	}
	// 	.contanier{
	// 		flex: 1;
	// 		margin-top: -94upx*2;
	// 		display: flex;
	// 		flex-direction: column;
	// 		overflow: hidden;
	// 		align-items: center;
			
	// 		.nav{
	// 			width: calc(100% - 52upx);
	// 			height: 42px;
	// 			background: rgba(65, 61, 69, 1);
	// 			color: rgba(255, 255, 255, 1);
	// 			font-size: 12upx*2;
	// 			padding: 10upx*2 12upx*2;
	// 			border-radius: 6upx*2 6upx*2 0 0;
	// 		}
				
		
	// 	}
		
	// }

}