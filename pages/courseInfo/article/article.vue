<template>
	<scroll-view scroll-y="true" class="detail-page" @scrolltolower="scrollend">
		<view class="name">
			{{ detail.title }}
		</view>
		<view class="desc" v-html="detail.content">
		</view>
		<view class="cbtn" @click="goPage()" v-if="unfinish.length > 0 && schemeId">
			继续学习
		</view>
	</scroll-view>
</template>

<script>
	import {
		getCourseInfo,
		finishCourseInfo,
		recordProcess,
		getCurrentSchemeGroupInfo
	} from '@/assets/api/index.js'
	import {
		formatDate
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				detail: {},
				id: '',
				schemeId: '', //schemeId为空时,是从/pages/users/myCourse/myCourse跳转
				elementId: '',
				recordProcessUse: 'scroll',
				unfinish: []
			}
		},
		onLoad({
			id,
			schemeId,
			elementId
		}) {
			// elementId = "1780148456411500546"
			// id = "1844536400500654081"
			// schemeId = "1844536400479682561"
			this.getInfo(id)
			this.id = id
			this.schemeId = schemeId
			this.elementId = elementId
			this.startTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
		},
		methods: {
			// initHeightData() {
			// 	const {
			// 		windowHeight
			// 	} = uni.getSystemInfoSync(); // 获取页面高度
			// 	const query = uni.createSelectorQuery().in(this);
			// 	query.select('.detail-page').boundingClientRect(rect => {
			// 		if (rect) {
			// 			const detailHeight = rect.height
			// 			this.recordProcessUse = detailHeight > windowHeight ? 'scroll' : 'time'
			// 			if (this.recordProcessUse == 'time') {
			// 				setTimeout(() => {
			// 					this.endTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
			// 					this.recordProcess()
			// 					this.finish()
			// 				}, 100)
			// 			}
			// 		}
			// 	}).exec();
			// },
			getPlanInfo() {
				getCurrentSchemeGroupInfo().then(result => {
					if (result == null) return
					this.course = result
					let {
						currentPaPatientScheme,
					} = result

					this.unfinish = currentPaPatientScheme.elementList.filter(item => {
						return !item.locked && ((item.type !== 4 &&item.status != 1) || (item.type == 4 &&item.status != 2)) //未锁且未完成的
					})

				})
			},
			goPage() {
				// let unfinish = [{
				// 	elementId: "1714116570694729730",
				// 	id: "1844536400211247105",
				// 	paPatientSchemeId: "1844536400089612290",
				// 	type: 3,
				// },{
				// 	elementId: "62b8df599c5a11efbef800163e08bbc9",
				// 	id: "1854411622313828354",
				// 	paPatientSchemeId:"1854411622003449858",
				// 	type: 5,
				// },{
				// 	elementId: "1842902638477705217",
				// 	id: "1865769840000581634",
				// 	paPatientSchemeId: "1865769839874752514",
				// 	type: 6
				// }]
				// let item = unfinish[0]
				let item = this.unfinish[0]
				// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
				let url = ''
				switch (item.type) {
					case 1:
						url = '/pages/courseInfo/article/article?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 2:
						url = '/pages/iframe?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 3:
						url = '/pages/courseInfo/audio/audio?id=' + item.id + '&schemeId=' + item.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 4:
						url = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item
							.id +
							"&type=2&status=" + item.dxResultStatus
						break;
					case 5:
						url = '/pages/courseInfo/questionnaire/questionnaire?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
					case 6:
						url = '/pages/courseInfo/guide/index?id=' + item.id + '&schemeId=' + item
							.paPatientSchemeId +
							'&elementId=' + item.elementId
						break;
				}
				if (url) {
					uni.redirectTo({
						url: url
					})
				}
			},
			getTimeDuraing(startTimeStr, endTimeStr) {
				const startTime = new Date(startTimeStr);
				const endTime = new Date(endTimeStr);
				const diffMilliseconds = endTime - startTime;
				const diffSeconds = diffMilliseconds / 1000;
				return diffSeconds
			},
			scrollend() {
				if (!this.schemeId) return
				this.endTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
				this.recordProcess()
				this.finish()
			},
			getInfo(id) {
				getCourseInfo({
					"id": id
				}).then(result => {
					this.detail = result
					uni.setNavigationBarTitle({
						title: result.title
					})
					setTimeout(() => {
						this.endTime = formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss')
						this.recordProcess()
						this.finish()
					}, 1000)
					// this.$nextTick(() => {
					// 	this.initHeightData()
					// })
				})
			},
			finish() {
				finishCourseInfo(this.id).then(result => {
					this.getPlanInfo()
				})
			},
			recordProcess() {
				if (!this.schemeId) return
				let params = {
					elementType: 1, // 类型1 文章2 视频3 放松训练'
					"elementId": this.elementId, //对应的素材id(material_id)或训练id(train_id)
					"startTime": this.startTime, // 学习开始时间
					"endTime": this.endTime, // 学习结束时间
					"playTime": this.getTimeDuraing(this.startTime, this.endTime), // 学习总时间 秒
					"playProgress": 100, // 学习进度
					status: 1, // 状态(0_未完成，1_已完成)，当playProgress=100时表示已完成
					'paPatientSchemeld': this.schemeId //患者方案主表id
				}
				recordProcess(params).then(result => {

				})
			}

		}
	}
</script>

<style lang="scss">
	.detail-page {
		max-width: 100%;
		// height: 100vh;
		padding: 16px 20px;
		box-sizing: border-box;
		display: flex;
		overflow-x: hidden;
		overflow-y: scroll;

		.name {
			font-weight: 700;
			font-size: 17px;
			color: #2B323C;
			margin-bottom: 5px;
		}

		.msg {
			font-weight: 400;
			font-size: 12px;
			color: #5B5D6A;
			display: flex;
			margin-bottom: 5px;

			>label {
				flex: 1
			}
		}

		.desc {
			flex: 1;
			width: 100%;
			color: #222;
			font-size: 15px;
			font-weight: 400;
			display: inline-block;

			p {
				font-size: 15px;
				font-weight: 400;
			}

			img {
				width: 100% !important;
				margin: 10px 0;
			}
		}

		.cbtn {
			height: 96rpx;
			background: #52c41a;
			border-radius: 48rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #ffffff;
			line-height: 96rpx;
			text-align: center;
			margin: 20px 0;
		}
	}
</style>