<template>
	<view class="mycourse-pages">
		<view class="module">
			<view class="module-label">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/ing.png" mode="aspectFit"></image>
			</view>
			<view class="module-container">
				<myCourseItem :list="ingcourses" typecourses="ingcourses" />
			</view>
		</view>
		<view class="module">
			<view class="module-label">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/finishCourse.png" mode="widthFix"></image>
			</view>
			<view class="module-container">
				<myCourseItem :list="finishcourses" typecourses="finishcourses" />
			</view>
		</view>
	</view>
</template>

<script>
import {
	getMyCourse
} from '@/assets/api/index.js'
import myCourseItem from '@/components/myCourseItem.vue';
export default {
	components: {
		myCourseItem
	},
	data () {
		return {
			ingcourses: [{
				courseId: 1,
				name: '方案名称',
				startTime: '2024-11-01',
				status: '进行中'
			}, {
				courseId: 1,
				name: '方案名称方案名称方案名称方案名称方案名称方案名称',
				startTime: '2024-11-01',
				status: '已暂停'
			}, {
				courseId: 1,
				name: '方案名称',
				startTime: '2024-11-01',
				status: '已完成'
			}],
			finishcourses: [{
				courseId: 1,
				name: '方案名称',
				startTime: '2024-11-01',
				status: '进行中'
			}, {
				courseId: 1,
				name: '方案名称方案名称方案名称方案名称方案名称方案名称',
				startTime: '2024-11-01',
				status: '已暂停'
			}, {
				courseId: 1,
				name: '方案名称',
				startTime: '2024-11-01',
				status: '已完成'
			}],

		}
	},
	onLoad () {
		this.initMyCourse()
	},
	methods: {
		initMyCourse () {
			getMyCourse({
				pageNo: 1,
				pageSize: 200,
			}).then(result => {
				let {
					records,
					total
				} = result
				let ingArr = []
				let finishArr = []
				let statusTextHash = {
					1: '已完成',
					2: '已暂停',
					0: '进行中',
				}
				let classHash = {
					1: 'finish',
					2: 'stop',
					0: 'ing',
				}
				for (let i = 0; i < records.length; i++) {
					let item = records[i]
					item.statusText = statusTextHash[item.status]
					item.className = classHash[item.status]
					// 状态（0_进行中，1_已完成,2_暂停中,）
					//量表的状态为2 代表完成
					if (item.status == 0) {
						ingArr.push(item)
					} else{
						finishArr.push(item)
					}
				}
				this.ingcourses = ingArr
				this.finishcourses = finishArr
			})
		}
	}
}
</script>
<style lang="scss">
@import './style.scss';
</style>