<template>
	<view class="mycourseinfo-pages">
		<view class="module" :key="course.id" v-for="course of list">
			<view class="module-label" @click="changeCourse(course.id)">
				<view class="clabel">
					{{ course.schemeName }}
				</view>
				<image v-if="checkActive(course.id)" class="cicon"
							 src="https://cbti.zhisongkeji.com/uniapp-static/downcourse.png" mode="widthFix"></image>
				<image v-else class="cicon" src="https://cbti.zhisongkeji.com/uniapp-static/upcourse.png" mode="widthFix">
				</image>
			</view>
			<view class="module-container" v-if="checkActive(course.id)">
				<myCourseInfoItem :list="course.elementList" @goPage="goPage" :typecourses="typecourses"/>
			</view>
		</view>
	</view>
</template>

<script>
import {
	getMyCourseInfo
} from '@/assets/api/index.js'
import myCourseInfoItem from '@/components/myCourseInfoItem.vue';
export default {
	components: {
		myCourseInfoItem
	},
	data () {
		return {
			courseId: '',
			activieCourse: ['1865293930080223233'],
			list: [],
			typecourses:''
		}
	},
	onLoad ({
		id,
		typecourses
	}) {
		//方案Id
		this.courseId = id || "1865293930055057409"
		this.typecourses = typecourses || 'finishcourses'
		this.getPlanInfo()
	},
	methods: {
		getPlanInfo () {
			getMyCourseInfo({
				id: this.courseId
			}).then(result => {
				this.list = result
				this.activieCourse = [result[0].id]
			})
		},
		changeCourse (courseId) {
			const index = this.activieCourse.indexOf(courseId);
			if (index > -1) {
				// 如果元素存在，则删除它
				this.activieCourse.splice(index, 1);
			} else {
				// 如果元素不存在，则添加它
				this.activieCourse.push(courseId);
			}
		},
		checkActive (courseId) {
			return this.activieCourse.indexOf(courseId) === -1 ? false : true
		},
		goPage (item) {
			// type: 1文章 2视频 3放松训练 4量表 5 问卷 6引导页 取item的id
			//1文章 2视频 3放松训练 4量表 可以点击看详情 //todo 量表
			if(this.typecourses == 'ingcourses' && item.status === 0){
				uni.showToast({
					title: '请到首页睡眠方案中完成',
					icon: 'none'
				})
			}
			
			let infoType = [1, 2, 3]
			if (item.status === 1 && infoType.indexOf(item.type) !== -1) {
				let url = ''
				switch (item.type) {
					case 1:
						url = '/pages/courseInfo/article/article?id=' + item.id
						break;
					case 2:
						url = '/pages/iframe?id=' + item.id
						break;
					case 3:
						url = '/pages/courseInfo/audio/audio?id=' + item.id
						break;
					case 4:
						url = '/pages/beginMeasure/index?reportId=' + item.dxResultId + "&measureTaskId=" + item
							.id +
							"&type=2&status=" + item.dxResultStatus
						break;
				}
				if (url) {
					uni.navigateTo({
						url: url
					})
				}
			} 
		},
	}
}
</script>
<style lang="scss">
@import './style.scss';
</style>