<template>
	<view class="list flex-col">
		<view class="list-item flex-row" :key="index" v-for="(item,index) of list" @click="goPage(item.path,index)">
			<view class="list-item-label">
				{{item.label}}
			</view>
			<view class="list-item-info">
				{{item.info}}
			</view>
			<image class="list-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode=""></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					label: '设置密码',
					info: '',
					path: '/pages/setPassword/index'
				}, {
					label: '应用版本',
					info: 'v1.0',
					path: ''
				}, {
					label: '关于我们',
					info: '',
					path: ''
				}, {
					label: '退出登录',
					info: '',
					path: ''
				}]
			}
		},
		methods: {
			goPage(path, index) {
				if (path) {
					uni.navigateTo({
						url: path
					})
				}
				if (index == 3) {
					this.$store.commit('setToken', null)
					this.$store.commit('setUser', {})
					this.$store.commit('tenantId', '')
					uni.removeStorageSync('token')
					uni.removeStorageSync('user')
					uni.removeStorageSync('tenantId')
					uni.removeStorageSync('openId')
					uni.removeStorageSync('unionId')
					uni.reLaunch({
						url: '/pages/login/login'
					})
					uni.showToast({
						icon:'none',
						title:'已退出，请重新登录'
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.list {
		margin: 0 40rpx;

		&-item {
			width: 100%;
			height: 108rpx;
			border-bottom: 1rpx solid #E5E5EA;
			display: flex;
			align-items: center;

			&-label {
				flex: 1;
				font-weight: 400;
				font-size: 28rpx;
				color: #1C1C1E;
			}

			&-info {
				font-weight: 400;
				font-size: 28rpx;
				color: #8E8E93;
				margin-right: 16rpx;
			}

			&-next {
				width: 24rpx;
				height: 24rpx;
			}
		}
	}
</style>