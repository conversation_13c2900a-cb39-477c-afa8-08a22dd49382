<template>
	<scroll-view scroll-y="true" class="myReview-page" @scrolltolower="scroll">
		<Empty v-if="list.length == 0"/>
		<view class="list-item flex-row" :key="index" v-for="(item,index) of list" @click="handleResultClick(item)">
			<image class="list-item-icon" src="/static/img/testIcon.png" mode="">
			</image>
			<view class="list-item-label">
				{{item.measureName}}
			</view>
			<image class="list-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode=""></image>
		</view>
		<view class="end" v-if="end">
			-已经到底了-
		</view>
	</scroll-view>
</template>

<script>
	import {
		getResultList
	} from '@/assets/api/index.js'
	import Empty from '@/components/empty.vue'
	export default {
		components: {
			Empty
		},
		data() {
			return {
				pageNo: 1,
				pageSize: 50,
				end: false,
				list: []
			}
		},
		onLoad() {
			this.getTask()
		},
		methods: {
			scroll() {
				this.getTask()
			},
			getTask() {
				let params = {
					pageNo: this.pageNo,
					pageSize: this.pageSize,
				}

				getResultList(params).then(result => {
					let total = result.total
					let list = result.records

					if (this.pageNo == 1) {
						this.list = list
						if (0 < total && total<= this.pageSize) {
							this.end = true
						}

					} else {
						if (this.pageNo < Math.ceil(total / this.pageSize) + 1) {
							this.list = [].concat(this.list, list)
						} else {
							this.end = true
						}
					}
					this.pageNo++

				})
			},
			handleResultClick(item) {
				if (item.status == 2) {
					uni.navigateTo({
						url: "/pages/summary/summary?reportId=" + item.id,
					});
				} else {
					let url = '/pages/answer/index?reportId=' + item.id;
					url = url + '&measureTaskId=' + item.measureTaskId
					uni.navigateTo({
						url: url,
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>