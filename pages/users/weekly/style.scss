.weekly-pages {
	padding: 0 32rpx 100rpx;
	overflow-y: scroll;
	.titleBox {
		width: 100%;
		height: 80rpx;
		background: rgba(235, 235, 245, 0.3);
		border-radius: 16rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 16rpx;
		margin-bottom: 24rpx;
		&-icon {
			flex: 1;
			display: flex;
			&-content {
				height: 40rpx;
				font-size: 24upx;
				color: #6260E7;
				border-radius: 32upx;
				border: 2upx solid #ACABF1;
				padding:0 10px;
				background: #ECECFE;
				display: flex;
				align-items: center;
			}
		}
			
		.after{
			justify-content: flex-end;
		}
		.title {
			flex: 2;
			font-weight: bold;
			font-size: 28rpx;
			color: #1c1c1e;
			text-align: center;
		}
	}

	.tab {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-column-gap: 24rpx;
		grid-row-gap: 24rpx;
		margin: 16rpx 0;
		&-item {
			width: 100%;
			height: 104rpx;
			background: rgba(235, 235, 245, 0.3);
			border-radius: 16rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			&-label {
				font-weight: 400;
				font-size: 24rpx;
				color: #8e8e93;
				padding-bottom: 8rpx;
			}
			&-value {
				font-weight: bold;
				font-size: 28rpx;
				color: #3a3a3c;
			}
		}
	}

	.label {
		font-weight: bold;
		font-size: 32rpx;
		color: #1c1c1e;
		display: flex;
		align-items: center;
		margin: 16rpx 0;
	}

	.bgcolor1 {
		background: #f3f4ff;
		.part-content {
			.part-title {
				.tvalue {
					color: #445df0;
				}
			}
		}
	}
	.bgcolor2 {
		background: #fbf7f3;
		.part-content {
			.part-title {
				.tvalue {
					color: #ff9604;
				}
			}
		}
	}
	.bgcolor3 {
		background: #f3fbf7;
		.part-content {
			.part-title {
				.tvalue {
					color: #31afaa;
				}
			}
		}
	}
	.bgcolor4 {
		background: #ffefef;
		.part-content {
			.part-title {
				.tvalue {
					color: '#FF6570';
				}
			}
		}
	}
	.part {
		margin-bottom: 28rpx;
		border-radius: 28rpx;
		padding: 19rpx;
		display: flex;
		&-icon {
			width: 41rpx;
			height: 41rpx;
			margin-right: 10rpx;
		}
		&-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			.ticon {
				width: 16rpx;
				height: 16rpx;
				display: inline-block;
				margin-right: 5rpx;
			}
			.part-title {
				font-weight: bold;
				font-size: 28rpx;
				color: #2c2c2e;
				margin-bottom: 8rpx;
				display: flex;
				line-height: 42rpx;
				justify-content: space-between;

				.tlabel {
					font-size: 24upx;
					color: #3b4552;
				}
				.tvalue {
					font-size: 34upx;
					color: #ff6570;
					padding-right: 15rpx;
				}
			}

			.part-value {
				font-weight: 400;
				font-size: 24rpx;
				color: #8e8e93;
				line-height: 32rpx;
				display: flex;
				align-items: center;
			}
		}
	}
}
