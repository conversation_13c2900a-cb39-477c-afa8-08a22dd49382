<template>
	<view class="weekly-pages">
		<view class="titleBox">
			<view class="titleBox-icon">
				<view class="titleBox-icon-content" @click="changeMonth('before')">
					上一周
				</view>
			</view>
			<view class="title">{{ startformat }} ～ {{ endformat }}</view>
			<view class="titleBox-icon after">
				<view class="titleBox-icon-content" @click="changeMonth('after')" v-if="endformat != toadyformat">
					下一周
				</view>
			</view>
		</view>
		<view class="chartline">
			<line v-if="showChart"  :xAxis="xAxis" :stayBed="bedriddens" :sleepEfficiencies="sleepEfficiencies"
				:sleepTime="sleepTime" />
		</view>
		<view class="tab">
			<view class="tab-item">
				<view class="tab-item-label">
					平均睡眠时长
				</view>
				<view class="tab-item-value">
					{{ detail.sleepDateAverage }}
				</view>
			</view>
			<view class="tab-item">
				<view class="tab-item-label">
					平均入睡时长
				</view>
				<view class="tab-item-value">
					{{ detail.fallAsleepAverage }}
				</view>
			</view>
			<view class="tab-item">
				<view class="tab-item-label">
					平均卧床时长
				</view>
				<view class="tab-item-value">
					{{ detail.bedriddenDateAverage }}
				</view>
			</view>
			<view class="tab-item">
				<view class="tab-item-label">
					平均睡眠效率
				</view>
				<view class="tab-item-value">
					{{( detail.efficiencyAverage * 100).toFixed(2)}}%
				</view>
			</view>

		</view>
		<view class="label">
			周睡眠分析
			<!-- <span class="status"></span> -->
		</view>
		<view class="part bgcolor1">
			<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/rushuishijian.png" mode=""></image>
			<view class="part-content">
				<view class="part-title">
					<view class="tlabel">
						平均入睡时长
					</view>
					<view class="tvalue">
						{{ detail.fallAsleepAverage }}
					</view>
				</view>
				<view class="part-value">
					{{ detail.fallAsleepAverageLabel }}
				</view>
			</view>
		</view>
		<view class="part bgcolor2">
			<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/waketime.png" mode=""></image>
			<view class="part-content">
				<view class="part-title">
					<view class="tlabel">
						平均夜醒时长
					</view>
					<view class="tvalue">
						{{ detail.weakupLengthAverage }}
					</view>
				</view>
				<view class="part-value">
					{{ detail.weakupLengthAverageLabel }}
				</view>
			</view>
		</view>
		<view class="part bgcolor3">
			<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/hospitalBed.png" mode=""></image>
			<view class="part-content">
				<view class="part-title">
					<view class="tlabel">
						平均夜醒次数
					</view>
					<view class="tvalue flex-row">
						<img v-if="(detail.weakupTimeAverageDiff - 0) > 0" class="ticon"
							src="https://cbti.zhisongkeji.com/uniapp-static/up1.png" mode="">
						</img>
						<img v-if="(detail.weakupTimeAverageDiff - 0) < 0" class="ticon"
							src="https://cbti.zhisongkeji.com/uniapp-static/down.png" mode="">
						</img>
						{{ detail.weakupTimeAverageDiff }}次

					</view>
				</view>
				<view class="part-value">
					{{ detail.weakupTimeAverageLabel }}
				</view>
			</view>
		</view>
		<view class="part bgcolor4">
			<image class="part-icon" src="https://cbti.zhisongkeji.com/uniapp-static/staybedtime.png" mode=""></image>
			<view class="part-content">
				<view class="part-title">
					<view class="tlabel">
						赖床总时长
					</view>
					<view class="tvalue flex-row">
						<img v-if="(detail.laiBedaAverageDiff - 0) > 0" class="ticon"
							src="https://cbti.zhisongkeji.com/uniapp-static/up1.png" mode="">
						</img>
						<img v-if="(detail.laiBedaAverageDiff - 0) < 0" class="ticon"
							src="https://cbti.zhisongkeji.com/uniapp-static/down.png" mode="">
						</img>
						{{ detail.laiBedaAverage }}
					</view>
				</view>
				<view class="part-value">
					{{ detail.laiBedaAverageLabel }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		formatDate
	} from '@/utils/index.js'
	import {
		getWeekSleep
	} from '@/assets/api/index.js'
	import line from '@/components/line.vue'
	export default {
		components: {
			line
		},
		data() {
			return {
				startDate: '',
				endDate: '',
				startformat: '',
				endformat: '',
				toadyformat: '',
				xAxis: [],
				stayBed: [],
				bedriddens:[],
				sleepTime: [],
				sleepEfficiencies: [],
				showChart: false,
				detail: {},
				chart: 0,
			}
		},
		onShow() {
			this.initData()
		},
		methods: {
			initData() {
				this.getDateParams()
				// this.getWeek()
			},
			getBeforeOneWeek(dateP) {
				let date = dateP ? new Date(dateP) : new Date()
				date.setDate(date.getDate() - 6);
				let dayOfWeek = date.toLocaleDateString(undefined, {
					weekday: 'short'
				});
				return date
			},
			getAfterOneWeek(dateP) {
				let date = dateP ? new Date(dateP) : new Date()
				date.setDate(date.getDate() + 6);
				let dayOfWeek = date.toLocaleDateString(undefined, {
					weekday: 'short'
				});
				return date
			},
			getDateParams() {
				this.startDate = this.getBeforeOneWeek()
				this.endDate = new Date()
				this.startformat = formatDate(this.startDate, 'MM月dd日'),
					this.endformat = formatDate(this.endDate, 'MM月dd日')
				this.toadyformat = this.endformat
				this.getWeek()
			},
			getWeek() {
				console.log('--')
				this.showChart = false
				// console.log('startDategetWeek---', this.startDate)
				// console.log('endDategetWeek---', this.endDate)
				let params = {
					startDate: formatDate(this.startDate, 'YYYY-MM-dd'),
					endDate: formatDate(this.endDate, 'YYYY-MM-dd')
				}
				// console.log('params--', params)
				// console.log('endDate---', this.endDate)
				// console.log('startformat---', this.startformat)
				// console.log('endformat---', this.endformat)
				getWeekSleep(params).then(result => {
					let {
						xAxis,
						lieBeds,
						sleeps,
						sleepEfficiencies,
						bedriddens
					} = result

					const toFixedTwo = (num) => {
						num = num * 100
						return parseFloat(num.toFixed(2))
					}
		
					this.xAxis = xAxis
					this.stayBed = lieBeds
					this.sleepTime = sleeps
					this.bedriddens = bedriddens 
					this.sleepEfficiencies =sleepEfficiencies.map(element => element * 100)
					console.log('sleepEfficiencies',this.sleepEfficiencies)
					this.detail = result
					this.showChart = true
				})

			},
			changeMonth(type) {
				if (type == 'before') {
					const date = this.startDate
					this.endDate = date
					this.startDate = this.getBeforeOneWeek(date)
					this.endformat = this.startformat
					this.startformat = formatDate(this.startDate, 'MM月dd日')

				} else if (type == 'after') {
					const date = this.endDate
					this.startDate = date
					this.endDate = this.getAfterOneWeek(date)
					this.startformat = this.endformat
					this.endformat = formatDate(this.endDate, 'MM月dd日')
				}

				this.getWeek()
			}
		}
	}
</script>


<style lang="scss">
	@import './style.scss';
</style>