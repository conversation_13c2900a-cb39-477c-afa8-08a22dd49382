.task-page {
	width: 100%;
	height: 100vh;
	padding: 0 16px;
	overflow: hidden;

	.task-tab {
		height: 108rpx;
		padding: 30rpx 0;
		font-weight: 400;
		font-size: 28rpx;
		color: #1c1c1e;

		&-item {
			margin-right: 40rpx;
		}
		.currenttab {
			font-weight: bold;
			font-size: 32rpx;
			color: #5e5ce6;
			position: relative;
		}
		.currenttab::after {
			content: '';
			position: absolute;
			width: 40rpx;
			height: 6rpx;
			background: #5e5ce6;
			border-radius: 4rpx;
			bottom: -8px;
			left: calc(50% - 20rpx);
		}
	}
	.task-content {
		height: calc(100vh - 108rpx);
		.taskitem {
			padding: 24rpx;
			background: rgba(235, 235, 245, 0.3);
			border-radius: 24rpx;
			margin-bottom: 16rpx;
			&-time {
				display: flex;
				align-items: center;		
				font-weight: 400;
				font-size: 24rpx;
				margin-bottom: 16rpx;
				.tlabel{
					flex: 1;
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					.iconImg{
						width: 25rpx;
					}
				}
				.tstatus{
					width: 107rpx;
					height: 28rpx;
				}
			}
			.unfinish {
				.tlabel {
					color: #ff9500;
				}
			}
			.finish {
				.tlabel {
					color: #00c826;
					
				}
			}
			&-label {
				font-weight: bold;
				font-size: 28rpx;
				color: #1c1c1e;
				margin-bottom: 8rpx;
				display: flex;
				align-items: center;
			}
			&-value {
				font-size: 24rpx;
				color: #636366;
				line-height: 32rpx;
				margin-bottom: 16rpx;
			}
			&-btn {
				justify-content: flex-end;
				.finish {
					color: #ffffff;
					background: #5e5ce6;
					margin-left: 24rpx;
				}
			}
		}
		.tasknext {
			justify-content: center;
			margin: 16rpx 0;
			font-weight: 400;
			font-size: 28rpx;
			color: #8e8e93;
			&-icon {
				width: 24rpx;
				height: 24rpx;
				margin-left: 8rpx;
			}
		}

		.ptaskitem {
			padding: 24rpx;
			background: rgba(235, 235, 245, 0.3);
			border-radius: 24rpx;
			margin-bottom: 16rpx;
			.pcontent {
				flex: 1;
				.ptaskitem-time {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					margin-bottom: 16rpx;
					.tlabel {
						flex: 1;
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						.iconImg {
							width: 25rpx;
						}
					}
					.tstatus {
						width: 107rpx;
						height: 28rpx;
					}
				}
				.unfinish {
					.tlabel {
						color: #ff9500;
					}
				}
				.finish {
					.tlabel {
						color: #00c826;
					}
				}
				.ptaskitem-label {
					font-weight: bold;
					font-size: 28rpx;
					color: #1c1c1e;
					margin-bottom: 8rpx;
					display: flex;
					align-items: center;
					&-icon {
						width: 32rpx;
						height: 32rpx;
						margin-right: 8rpx;
					}
				}
			}
		}
	}
}
