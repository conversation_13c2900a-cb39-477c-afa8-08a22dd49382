<template>
	<view class="task-page">
		<view class="task-tab flex-row">
			<view :class="'task-tab-item ' + (taskCurrent == index ? 'currenttab' :'')" :key="index"
				v-for="(item,index) of tasktabs" @click="taskCurrent = index">
				{{item}}
			</view>
		</view>
		<scroll-view scroll-y="true" class="task-content flex-col" v-if="taskCurrent == 0" @scrolltolower="scroll()">
			<Empty v-if="medicalOrdemasks.length == 0"/>
			<view class="taskitem flex-col" :key="item.id" v-for="item of medicalOrdemasks">
				<view :class="'taskitem-time ' + (item.status == 0 ? 'unfinish' : 'finish')">
					<view class="tlabel">
						<image v-if="item.status == 0" class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
						</image>
						<image v-else class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png" mode="widthFix">
						</image>
						请在{{item.limitDate}}之前完成
					</view>
					<image v-if="item.status == 0" class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
					</image>
					<image v-else class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png" mode="widthFix">
					</image>
				</view>
				<view class="taskitem-label">
					{{item.label}}
				</view>
				<view class="taskitem-value">
					{{ item.showAll ? item.valueAll : item.value }}
				</view>
				<view class="taskitem-btn flex-row">
					<view class="w-btn expand" v-if="item.value" @click="item.showAll = !item.showAll ">
						{{ item.showAll ? '收起' : '展开全部' }}
					</view>
					<view class="w-btn finish" @click="finishMedicalOrder(item.id)" v-if="item.status == 0">
						我已完成
					</view>
				</view>
			</view>
			<view class="end" v-if="configPage[0].end">
				-已经到底了-
			</view>
		</scroll-view>
		<scroll-view scroll-y="true" class="task-content flex-col" v-else @scrolltolower="scroll()">
			<Empty v-if="patientSchemeTasks.length == 0"/>
			<view class="ptaskitem flex-row" :key="item.id" v-for="item of patientSchemeTasks"
				@click="handleMeasureClick(item)">
				<view class="pcontent">
					<view :class="'ptaskitem-time ' + (item.status != 2 ? 'unfinish' : 'finish')">
						<view class="tlabel">
							<image v-if="item.status != 2" class="iconImg"
								src="https://cbti.zhisongkeji.com/uniapp-static/mtime.png" mode="widthFix">
							</image>
							<image v-else class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/mfinish.png"
								mode="widthFix">
							</image>
							请在{{item.limitDate}}之前完成
						</view>
						<image v-if="item.status != 2" class="tstatus"
							src="https://cbti.zhisongkeji.com/uniapp-static/munfinishtext.png" mode="widthFix">
						</image>
						<image v-else class="tstatus" src="https://cbti.zhisongkeji.com/uniapp-static/mfinishtext.png"
							mode="widthFix">
						</image>
					</view>
					<view class="ptaskitem-label">
						<image class="ptaskitem-label-icon"
							src="https://cbti.zhisongkeji.com/uniapp-static/taskicon.png" mode=""></image>
						{{item.measureName}}
					</view>
				</view>
				<image class="picon iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode="">
				</image>
			</view>
			<view class="end" v-if="configPage[1].end">
				-已经到底了-
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getMedicalOrderTask,
		getPatientSchemeTask,
		finishMedicalOrderTask
	} from '@/assets/api/index.js'
		import Empty from '@/components/empty.vue'
	export default {
		components: {
			Empty
		},
		data() {
			return {
				tasktabs: ['医嘱任务', '量表任务'],
				taskCurrent: 0,
				medicalOrdemasks: [],
				patientSchemeTasks: [],
				configPage: {
					0: {
						pageNo: 1,
						end: false
					},
					1: {
						pageNo: 1,
						end: false
					}
				}
			}
		},
		onLoad() {
			this.initData()
		},
		methods: {
			scroll() {
				this.taskCurrent == 0 ? this.getMedicalOrder() : this.getPatientScheme()
			},
			initData() {
				this.getMedicalOrder()
				this.getPatientScheme()
			},
			finishMedicalOrder(id) {
				finishMedicalOrderTask({
					'id': id
				}).then(result => {
					this.configPage[0] = {
						pageNo: 1,
						end: false
					}
					this.medicalOrdemasks = []
					this.getMedicalOrder()
				})
			},
			getMedicalOrder() { //医嘱任务
				getMedicalOrderTask({
					pageNo: this.configPage[0].pageNo,
					pageSize: 50,
					status: 0
				}).then(result => {
					let {
						records,
						total
					} = result
					let list = records.map(item => {
						let value = ''
						let valueAll = item.content
						if (item.content.length > 50) {
							value = item.content.slice(0, 50) + '...'
						}
						return {
							id: item.id,
							label: item.title,
							value: value,
							valueAll: item.content,
							showAll: value.length > 0 ? false : true,
							limitDate: item.limitDate,
							status: item.status
						}
					})
					if (this.configPage[0].pageNo == 1) {
						this.medicalOrdemasks = list
						if (total <= 50 && total != 0) {
							this.configPage[0].end = true
						}
					} else {
						if (this.configPage[0].pageNo < Math.ceil(total / 50) + 1) {
							this.medicalOrdemasks = [].concat(this.medicalOrdemasks, list)
						} else {
							this.configPage[0].end = true
						}
					}
					this.configPage[0].pageNo++
				})
			},
			getPatientScheme() { //量表任务
				getPatientSchemeTask({
					pageNo: this.configPage[1].pageNo,
					pageSize: 50
				}).then(result => {
					let {
						records,
						total
					} = result
					if (this.configPage[1].pageNo == 1) {
						this.patientSchemeTasks = records

						if (total <= 50 && total != 0) {
							this.configPage[1].end = true
						}
					} else {
						if (this.configPage[1].pageNo < Math.ceil(total / 50) + 1) {
							this.patientSchemeTasks = [].concat(this.patientSchemeTasks, records)
						} else {
							this.configPage[1].end = true
						}
					}
					this.configPage[1].pageNo++
				})
			},
			handleMeasureClick(item) {
				uni.navigateTo({
					url: '/pages/beginMeasure/index?reportId=' + item.resultId + '&measureTaskId=' + item.id +
						'&status=' + item.status + '&type=2'
				})
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>