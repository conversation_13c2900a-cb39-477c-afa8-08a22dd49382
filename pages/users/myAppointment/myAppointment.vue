<template>
	<view class="myAppointment-page">
		<view class="task-tab flex-row">
			<view :class="'task-tab-item ' + (taskCurrent == index ? 'currenttab' :'')" :key="index"
				v-for="(item,index) of tasktabs" @click="changeTab(index)">
				{{item}}
			</view>
		</view>
		<scroll-view scroll-y="true" class="myAppointment flex-col" @scrolltoupper="scroll" >
			<Empty v-if="list.length == 0" />
			<view :class="'list-item flex-row ' + statusHash[item.status].className" :key="index"
				v-for="(item,index) of list">
				<image class="list-item-icon" v-if="item.status != 4 || item.status != 5"
					src="https://cbti.zhisongkeji.com/uniapp-static/timeing.png" mode=""></image>
				<image class="list-item-icon" v-else src="https://cbti.zhisongkeji.com/uniapp-static/time.png" mode=""></image>
				<view class="list-item-label">
					{{item.label}}
				</view>
				<view class="list-item-info">
					{{ item.status ? statusHash[item.status].label :''}}
				</view>
				<!-- <image class="list-item-next" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode=""></image> -->
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getAppointmentAll,
	} from '@/assets/api/index.js'
	import {
		formatDate,
	} from '@/utils/index.js'
	import Empty from '@/components/empty.vue'
	export default {
		components: {
			Empty
		},
		data() {
			return {
				tasktabs: ['全部', '待确认', '已确认', '已完成', '已取消'],
				taskCurrent: 0,
				list: [],
				statusHash: {
					1: {
						label: '待确认',
						className: 'waitComfrim'
					},
					2: {
						label: '已确认',
						className: 'comfrim'
					},
					3: {
						label: '已完成',
						className: 'finish'
					},
					4: {
						label: '已取消',
						className: 'delect'
					},
					5: {
						label: '已逾约',
						className: 'error'
					}
				},
				pageNo: 1
			}
		},
		onLoad() {
			this.getappointment()
		},
		methods: {
			scroll() {
				this.getappointment()
			},
			changeTab(index){
				this.taskCurrent = index
				this.pageNo = 1
				this.getappointment()
			},
			getappointment() {
				let params ={
					pageNo: this.pageNo,
					pageSize: 50,
					status:this.taskCurrent == 0 ? '' :this.taskCurrent 
				}

				getAppointmentAll(params).then(result => {

					let list = result.records.map(item => {
						return {
							label:formatDate(new Date(item.visitDate), 'MM月dd日') +  ' ' + item.visitTime,
							status: item.status,
						}
					})



					if (this.pageNo == 1) {
						this.list = list

					} else {
						if (this.pageNo < Math.ceil(total / 50) + 1) {
							this.list = [].concat(this.list, list)
						}
					}
					this.pageNo++

				})
			},
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>