.myAppointment-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .task-tab {
    height: 108rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #1c1c1e;

    &-item {
      width: 20%;
      display: flex;
      justify-content: center;
    }
    .currenttab {
      width: 20%;
      font-weight: bold;
      font-size: 32rpx;
      color: #5e5ce6;
      position: relative;
    }
    .currenttab::after {
      content: "";
      position: absolute;
      width: 40rpx;
      height: 6rpx;
      background: #5e5ce6;
      border-radius: 4rpx;
      bottom: -8px;
      left: calc(50% - 20rpx);
    }
  }
  .myAppointment {
    width: 100%;
    height: calc(100vh - 108rpx);
    overflow: scroll;
    .waitComfrim {
      .list-item-label {
        color: #1c1c1e;
      }
      .list-item-info {
        color: #ff9f0a;
      }
    }
    .comfrim {
      .list-item-label {
        color: #1c1c1e;
      }
      .list-item-info {
        color: #34c759;
      }
    }
    .finish {
      .list-item-label {
        color: #8e8e93;
      }
      .list-item-info {
        color: #8e8e93;
      }
    }
    .delect {
      .list-item-label {
        color: #8e8e93;
      }
      .list-item-info {
        color: #8e8e93;
      }
    }
    .list-item {
      width: calc(100% - 40rpx);
      height: 108rpx;
      border-bottom: 1rpx solid #e5e5ea;
      display: flex;
      align-items: center;
      margin: 0 auto;
      &-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 24rpx;
      }
      &-label {
        flex: 1;
        font-weight: 400;
        font-size: 28rpx;
        color: #1c1c1e;
      }
      &-info {
        font-weight: 400;
        font-size: 28rpx;
        color: #8e8e93;
        margin-right: 16rpx;
      }
      &-next {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}
