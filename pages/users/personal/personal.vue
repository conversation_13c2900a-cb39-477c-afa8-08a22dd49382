<template>
	<view class="list">
		<view class="list-item flex-row" :key="index" v-for="(item,index) of list">
			<view class="list-item-label">
				{{item.label}}
			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')"
				v-if="item.type == 'picker'">
				{{dataForm[item.modelName] ? dicCodeLabelHash[item.modelName][dataForm[item.modelName]] :''}}
			</view>
			<view :class="'list-item-info ' + (dataForm[item.modelName] ? '' :'place')" v-else>
				{{dataForm[item.modelName]}}
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getUser,
	} from '@/assets/api/index.js'
	export default {
		data() {
			return {
				dicCodeHash: {
					sex: uni.getStorageSync('sex'),
					cultural: uni.getStorageSync('cultural'),
					maritalStatus: uni.getStorageSync('maritalStatus')
				},
				dicCodeLabelHash: {
					sex: uni.getStorageSync('dicsexMap'),
					cultural: uni.getStorageSync('dicculturalMap'),
					maritalStatus: uni.getStorageSync('dicmaritalStatusMap')
				},
				dataForm: {
					name: '',
					telphone: '',
					sex: '',
					birthday: '2024-02-12',
					profession: '',
					cultural: '',
					maritalStatus: ''
				},
				list: [{
					label: '姓名',
					modelName: 'name',
					showIcon: false,
					type: 'text',
					place: '请输入'
				}, {
					label: '电话',
					modelName: 'telphone',
					showIcon: false,
					type: 'phone',
					place: '请输入',
				}, {
					label: '性别',
					modelName: 'sex', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择'
				}, {
					label: '生日',
					modelName: 'birthday',
					showIcon: true,
					type: 'date',
					place: '请选择'
				}, {
					label: '职业',
					modelName: 'profession',
					showIcon: false,
					type: 'text',
					place: '请输入'
				}, {
					label: '文化程度',
					modelName: 'cultural', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择'
				}, {
					label: '婚姻状况',
					modelName: 'maritalStatus', //todo 字典
					showIcon: true,
					type: 'picker',
					place: '请选择'
				}],
			}
		},
		onLoad() {
			this.initData()
		},
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			initData() {
				this.getUser()
				// this.getDictCode()
			},
			submit() {

			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			bindDateChange(e) {
				this.dataForm.birthday = e.detail.value
			},
			changePicker(e, modelName) {
				let index = e.detail.value
				this.dataForm[modelName] = this.dicCodeHash[modelName][index].value
			},
			getUser() {
				getUser().then(result => {
					this.dataForm = result
					// uni.setStorageSync('user', result)
					// this.$store.commit('setUser', result)
				})
			},
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>