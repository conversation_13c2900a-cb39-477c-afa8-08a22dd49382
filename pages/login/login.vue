<template>
	<view class="login-page" :style="{ paddingTop: navBarHeight + 'px' }">
		<navbar :isCustom="true">
			<view class="titlenavbar" slot="middle">登录知眠</view>
		</navbar>
		<view class="loginForm" v-if="loginway == 'account'">
			<!-- 登录 -->
			<view class="hello">
				助您每晚睡个好觉
				<img class="hello-icon" src="/static/img/zz.png" alt="" />
			</view>
			<view class="item">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/phone.png" />
				<input class="uni-input " type="number" v-model="phone" placeholder="请输入手机号码"
					placeholder-class='sug_p' />
			</view>
			<view class="item">
				<image src="https://cbti.zhisongkeji.com/uniapp-static/mm.png" />
				<input v-if="showpassword" class="uni-input" @input="passwordInput" v-model="password"
					placeholder="请输入密码" placeholder-class='sug_p' type="text" />
				<input v-else class="uni-input" @input="passwordInput" v-model="password" placeholder="请输入密码"
					placeholder-class='sug_p' type="password" />
				<view class="showpassword" @click="changeShowPassword()">
					<image v-if="showpassword" src="https://cbti.zhisongkeji.com/uniapp-static/xs.png" />
					<image v-else src="https://cbti.zhisongkeji.com/uniapp-static/yc.png" />
				</view>
			</view>
			<view class="loginButton" @click="loginClick()">登录</view>
			<view class="agreementBottom">
				<button class="actions" open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="agreePrivacy">
					<radio style="transform: scale(0.7);" color="#2196F3" value="r1" :checked="checked" />
				</button>
				勾选代表已同意
				<view @click="goPrivacy()" style="color:#2196F3; fontWeight: 500;">《用户服务协议》</view>
			</view>
		</view>
		<view class="loginForm" v-if="loginway == 'wx'">
			<!-- 登录 -->
			<view class="hello">
				助您每晚睡个好觉
				<img class="hello-icon" src="/static/img/zz.png" alt="" />
			</view>
			<button class="loginButton" v-if="checked" open-type="getPhoneNumber"
				@getphonenumber="decryptPhoneNumber">授权您的手机号</button>
			<view v-else class="loginButton" @click="pop">一键登录</view>
			<view class="agreementBottom">
				<button class="actions" open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="agreePrivacy">
					<radio style="transform: scale(0.7);" color="#5E5CE6" value="r1" :checked="checked" />
				</button>
				勾选代表已同意
				<view style="color:#5E5CE6" @click="goPrivacy()">《用户服务协议》</view>
			</view>
		</view>
		<view class="loginWay">
			<view class="loginWay-label">
				其他登录方式
			</view>
			<view class="loginWay-item accountbg" v-if="loginway != 'account'">
				<image class="account" @click="loginway = 'account'" src="/static/img/account0414.jpg" mode="">
				</image>
			</view>
			<view class="loginWay-item" v-if="loginway != 'wx'">
				<image class="wx" @click="loginway = 'wx'" src="https://ccbt.zhisongkeji.com/uniapp-static/account.png"
					mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		login,
		loginMobile,
		getUser,
		loginAccount,
		getPhone
	} from '@/assets/api/index.js'
	import {
		checkPhone
	}
	from '@/assets/utils/index.js'
	import {
		useStore
	} from 'vuex'
	import navbar from '@/components/navbar.vue'
	export default {
		components: {
			navbar
		},
		data() {
			const store = useStore();
			return {
				// 周礼：***********
				// 密码：zhisongkeji,123
				navBarHeight: store.state.navBarHeight || 87,
				openId: '',
				unionId: '',
				phone: '',
				password: '',
				showpassword: false,
				checked: false,
				loginway: 'account', //登录方式: 账号密码、微信一键登录

			}
		},
		methods: {
			changeShowPassword() {
				this.showpassword = !this.showpassword
			},
			passwordInput(e) {
				this.password = e.detail.value
			},
			pop() {
				uni.showModal({
					title: '提示',
					content: '请阅读并勾选用户服务协议',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			loginClick() {
				if (!this.checked) {
					uni.showToast({
						title: '请阅读并勾选用户服务协议',
						icon: 'none'
					})
					return
				}
				if (!this.phone) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					})
					return
				}
				if (!checkPhone(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none'
					})
					return
				}
				if (!this.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return
				}
				let loginData = {
					phone: this.phone,
					password: this.password,
					openId: uni.getStorageSync('openId') || '',
					unionId: uni.getStorageSync('unionId') || ''
				}
				loginAccount(loginData).then(result => {
					uni.setStorageSync('token', result.token)
					uni.setStorageSync('tenantId', result.tenantId)
					this.$store.commit('setToken', result.token)
					this.$store.commit('setTenantId', result.tenantId)
					this.getUserInfo()
				})
			},
			getUserInfo() {
				getUser().then(result => {
					uni.setStorageSync('user', result)
					this.$store.commit('setUser', result)
					uni.reLaunch({
						url: '/pages/index/index'
					})
				})
			},
			goPrivacy() {
				uni.navigateTo({
					url: "/pages/privacy/privacy"
				})
			},
			agreePrivacy(e) {
				this.checked = !this.checked
			},
			decryptPhoneNumber(e) {
				wx.login({
					success: res => {
						let {
							code
						} = res
						login({
							code: code
						}).then(result => {
							let {
								openid,
								unionid
							} = result.wxSessionInfo
							this.openId = openid
							this.unionId = unionid
							uni.setStorageSync('openId', openid)
							uni.setStorageSync('unionid', unionid)
							if (result.isBind) { // 已绑定手机号
								uni.setStorageSync('token', result.token)
								uni.setStorageSync('tenantId', result.tenantId)
								this.$store.commit('setTenantId', result.tenantId)
								this.$store.commit('setToken', result.token)
								this.getUserInfo()
							} else {
								getPhone({
									code: e.detail.code
								}).then(result => {
									const telphone = result.phone
									let params = {
										telphone: telphone,
										openId: this.openId || uni.getStorageSync(
											'openId'),
										unionId: this.unionId || uni.getStorageSync(
											'unionId')
									}
									loginMobile(params).then(result => {
										console.log('loginMobile', result)
										if (result.isBind) { // 已绑定手机号
											uni.setStorageSync('token', result.token)
											uni.setStorageSync('tenantId', result
												.tenantId)
											this.$store.commit('setTenantId', result
												.tenantId)
											this.$store.commit('setToken', result
												.token)
											this.getUserInfo()
										} else {
											//在pc端没有绑定医生
											//todo 提示什么
											uni.showToast({
												title: '请联系医生',
												icon: 'none'
											})
										}

									})

								})

							}

						})
					}
				})
			},
			login() {
				wx.login({
					success: res => {
						let {
							code
						} = res
						login({
							code: code
						}).then(result => {
							if (result.isBind) { // 已绑定手机号
								uni.setStorageSync('token', result.token)
								uni.setStorageSync('tenantId', result.tenantId)
								this.$store.commit('setToken', result.token)
								this.getUserInfo()
							}
							let {
								openid,
								unionid
							} = result.wxSessionInfo
							this.openId = openid
							this.unionId = unionid

						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	@import './style.scss';
</style>