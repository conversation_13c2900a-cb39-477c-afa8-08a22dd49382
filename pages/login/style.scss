.login-page {
  width: 100%;
  height: 100%;
  background-image: url("https://cbti.zhisongkeji.com/uniapp-static/loginbg.png");
  background-position: top left;
  background-size: 100%;
  background-repeat: no-repeat;
  .titlenavbar {
    font-weight: bold;
    font-size: 40rpx;
    color: #ffffff;
  }
  .loginForm {
    width: calc(100% - 144rpx);
    // background-color: #303133;
    padding: 100rpx 72rpx;
    margin: 100rpx auto 50rpx;
  }

  .hello {
    font-weight: bold;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: left;
	&-icon{
		width: 32rpx;
		height: 32rpx;
		margin-bottom: 15rpx;
		margin-left: 8upx;
	}
  }
  .item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72rpx;
    font-size: 14px;
    color: #fff;
    font-weight: 400;
    line-height: 20px;
    background: rgba(255, 255, 255, 0.32);
    border-radius: 38px;
    border: 2px solid #ffffff;
    backdrop-filter: blur(9px);
    padding: 0 10rpx;
    margin-top: 35px;
	
    > image {
      width: 24rpx;
      height: 24rpx;
      margin: 0 15rpx;
    }
	.showpassword{
		padding: 0 15px;
		> image {
		  width: 24rpx;
		  height: 24rpx;
		}
	}
    .uni-input {
      flex: 1;
      display: flex;
      align-items: center;

      .sug_p {
        color: #fff;
      }
    }
  }

  .loginButton {
    display: flex;
    width: 100%;
    height: 40px;
    background: #ffffff;
    border-radius: 20px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #375802;
    line-height: 20px;
    margin-top: 39px;
  }
  .agreementBottom {
    display: flex;
    align-items: center;
    margin-top: 12px;
    font-size: 13px;
    font-weight: 400;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 24rpx;
    button {
      -webkit-tap-highlight-color: transparent;
      background-color: transparent;
      border-radius: 5px;
      box-sizing: border-box;
      color: #fff;
      cursor: pointer;
      display: block;
      font-size: 18px;
      line-height: 2;
      margin-left: 0;
      margin-right: 0;
      overflow: hidden;
      padding-left: 0;
      padding-right: 0;
      position: relative;
      text-align: center;
      text-decoration: none;
    }
    button::after {
      border: none;
    }
    .actions {
      display: flex;
      flex-direction: row;
      font-weight: 400;
      color: #8f9399;
      line-height: 17px;
      align-items: center;

      .agree {
        margin-left: 4px;
      }
    }
    .agreement {
      color: #fff;
    }
  }

  .uni-forms {
    padding-top: 25px;
  }

  .uni-forms-item {
    align-items: center;

    .uni-forms-item__content {
      display: flex !important;
      align-items: center;

      .info {
        padding-left: 8px;
      }
    }
  }

  .uni-forms-item__content {
    display: flex;
    align-items: center;
  }

  .bottom-btn {
    display: flex;
    width: 100%;
    height: 50px;
    border-radius: 25px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
    margin-top: 30px;
    background: #128f99;

    .btn-content {
      margin-left: 12px;
      padding: 8px 15px;
      font-size: 16px;
      font-weight: bold;
      border-radius: 18px;
      background: #3377f5;
    }
  }

  .bottom-info {
    padding-top: 50px;
    padding-bottom: 30px;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    color: #95a6f3;
  }
  .loginWay {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 214rpx;
    &-label {
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      margin-bottom: 32rpx;
    }
    &-item {
    	display: flex;
    	align-items: center;
    	justify-content: center;
    	width: 72rpx;
    	height: 72rpx;
    	border-radius: 50%;
    	border: 1rpx solid rgba(255, 255, 255, 0.5);
    	 .account {
    		width: 42rpx;
    		height: 42rpx;
    	}
    	.wx{
    		width: 72rpx;
    		height: 72rpx;
    	}
    }
    .accountbg{
    	background: rgba(255, 255, 255, 0.15);
    }
  }
}
