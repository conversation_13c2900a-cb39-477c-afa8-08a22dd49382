<script>
	import {
		getDictCode
	} from '@/assets/api/index.js'
	export default {
		onLaunch: function() {
			console.log('App Launch')
			this.initDicCode()
			// 缓存中有 doctorId 说明是通过扫码进来的
			// if(uni.getStorageSync('doctorId')){
			// 	uni.reLaunch({
			// 		url:'/pages/register/personal'
			// 	})
			// }else{
			// 	uni.reLaunch({
			// 		url:'/pages/login/login'
			// 	})
			// }
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			initDicCode() {
				this.getDicCode('sex') //性别
				this.getDicCode('cultural') //文化
				this.getDicCode('marital_status') //婚姻状况 
			},
			getDicCode(dicCode) {
				getDictCode(dicCode).then(result => {
					if(dicCode == 'marital_status'){
						dicCode='maritalStatus'
					}
					uni.setStorageSync(dicCode, result)
					let dicMap = {}
					for (let item of result) {
						dicMap[item.value] = item.title
					}
					//dicSexMap
					uni.setStorageSync(`dic${dicCode}Map`, dicMap)
				})
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>