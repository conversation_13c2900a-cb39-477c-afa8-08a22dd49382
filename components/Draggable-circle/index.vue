<template>
	<view class="clock">
		<view class="clock-top">
			<view :class="['up-item', step === 1 && 'up-big-item']">
				<view class="item-title">
					<image class="title-icon" src="https://cbti.zhisongkeji.com/uniapp-static/gobed.png" mode="">
					</image>
					<text class="title-text">上床</text>
				</view>
				<text class="item-time">{{ gobedTimeShow }}</text>
				<text class="item-text">{{ getTimeText(gobedDeg) }}</text>
			</view>
			<view v-if="step === 2" :class="['up-item', step === 1 && 'up-big-item']">
				<view class="item-title">
					<image class="title-icon" src="https://cbti.zhisongkeji.com/uniapp-static/sleep.png" mode="">
					</image>
					<text class="title-text">入睡</text>
				</view>
				<text class="item-time">{{ sleepTimeShow }}</text>
				<text class="item-text">{{ getTimeText(sleepDeg) }}</text>
			</view>
			<view v-if="step === 2" :class="['up-item', step === 1 && 'up-big-item']">
				<view class="item-title">
					<image class="title-icon" src="https://cbti.zhisongkeji.com/uniapp-static/wake.png" mode=""></image>
					<text class="title-text">醒来</text>
				</view>
				<text class="item-time">{{ wakeTimeShow }}</text>
				<text class="item-text">{{ getTimeText(wakeDeg) }}</text>
			</view>
			<view :class="['up-item', step === 1 && 'up-big-item']">
				<view class="item-title">
					<image class="title-icon" src="https://cbti.zhisongkeji.com/uniapp-static/getUp.png" mode="">
					</image>
					<text class="title-text">起床</text>
				</view>
				<text class="item-time">{{ getUpTimeShow }}</text>
				<text class="item-text">{{ getTimeText(getUpDeg) }}</text>
			</view>
		</view>

		<view class="clock-inner">
			<view class="circle-out" id="sliderBox">
				<!-- 外圆环 第二步显示 -->
				<view v-if="step === 2" class="circle-big-progress" :style="{ background: circleBigBackground }"></view>
				<!-- 内圆环 -->
				<view class="circle-progress"
					:style="{ background: step === 1 ? circleBigBackground : circleBackground }"></view>
				<!-- 表盘背景 -->
				<image class="circle-bg" src="https://cbti.zhisongkeji.com/uniapp-static/clockFace.png" mode=""></image>
				<text class="circle-num" :class="[`circle-num-${item - 1}`]"
					v-for="item in 24">{{ (item - 1) % 2 ? '' : item - 1 }}</text>

				<!-- 上床 -->
				<view v-if="step === 2" class="circle-big-start-box" :style="`transform: rotate(${gobedDeg}deg);`">
					<image class="circle-start" :style="`transform: rotate(${-gobedDeg}deg);`"
						src="https://cbti.zhisongkeji.com/uniapp-static/clockGobed.png" mode=""></image>
				</view>
				<!-- 起床 -->
				<view v-if="step === 2" class="circle-big-end-box" :style="`transform: rotate(${getUpDeg}deg);`">
					<image class="circle-end" :style="`transform: rotate(${-getUpDeg}deg);`"
						src="https://cbti.zhisongkeji.com/uniapp-static/clockUp.png" mode=""></image>
				</view>

				<!-- 上床/熟睡按钮 可操作 -->
				<view class="circle-start-box" :style="`transform: rotate(${step === 1 ? gobedDeg : sleepDeg}deg);`">
					<image class="circle-start" @touchmove="touchStartMove" @touchend="touchStartEnd"
						:style="`transform: rotate(${-(step === 1 ? gobedDeg : sleepDeg)}deg);`"
						:src="`https://cbti.zhisongkeji.com/uniapp-static/${step === 1 ? 'clockGobed' : 'clockSleep'}.png`"
						mode=""></image>
				</view>
				<!-- 起床/睡醒按钮 可操作 -->
				<view class="circle-end-box" :style="`transform: rotate(${step === 1 ? getUpDeg : wakeDeg}deg);`">
					<image class="circle-end" @touchmove="touchEndMove" @touchend="touchEndEnd"
						:style="`transform: rotate(${-(step === 1 ? getUpDeg : wakeDeg)}deg);`"
						:src="`https://cbti.zhisongkeji.com/uniapp-static/${step === 1 ? 'clockUp' : 'clockWake'}.png`"
						mode=""></image>
				</view>
			</view>
		</view>
		<view :class="['tips',{'tipsTop':step === 2}]">
			{{step === 1 ? '请拖动图标选择上下床时间':'请拖动内圈图标选择睡眠时间'}}
		</view>
		<view class="clock-bottom">
			<view class="bottom-item" v-if="step === 2">
				<text class="item-title">睡眠时长</text>
				<text class="item-time">{{ sleepDuration }}</text>
			</view>
			<view :class="['bottom-item', step === 1 && 'flex-1']">
				<text class="item-title">卧床时长</text>
				<text class="item-time">{{ bedRestTime }}</text>
			</view>
		</view>
		<view class="tips" v-if="step === 1" @click="neverSleep()">
			整晚没睡？
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			step: { // 1-上床起床 2-熟睡睡醒
				type: Number,
				default: 1
			},
			gobedTime: { // 上床时间，转成数字
				type: Number,
				default: 8
			},
			sleepTime: { // 熟睡时间
				type: Number,
				default: 8.5
			},
			wakeTime: { // 睡醒时间
				type: Number,
				default: 14
			},
			getUpTime: { // 起床
				type: Number,
				default: 14.5
			}
		},
		data() {
			return {
				centerX: 0,
				centerY: 0,
				gobedDeg: 0, // 上床
				getUpDeg: 0, // 起床
				sleepDeg: 0, // 熟睡
				wakeDeg: 0, // 起床
			}
		},
		mounted() {
			setTimeout(() => {
				let view = uni.createSelectorQuery().in(this).select('#sliderBox');
				view.boundingClientRect(data => {
						this.centerX = data.left + data.width / 2;
						this.centerY = data.top + data.height / 2;
					})
					.exec();
				this.gobedDeg = this.gobedTime / 24 * 360
				this.getUpDeg = this.getUpTime / 24 * 360
				this.sleepDeg = this.sleepTime / 24 * 360
				this.wakeDeg = this.wakeTime / 24 * 360
			}, 1000)
		},
		computed: {
			circleBigBackground() {
				if ((this.gobedDeg >= 180 && this.getUpDeg <= 180 && this.getUpDeg > 0) ||
					(this.gobedDeg > 0 && this.gobedDeg < 180 && this.getUpDeg > 0 && this.getUpDeg < 180 && this
						.gobedDeg > this.getUpDeg) ||
					(this.gobedDeg > 180 && this.gobedDeg < 360 && this.getUpDeg > 180 && this.getUpDeg < 360 && this
						.gobedDeg > this.getUpDeg)) {
					return `conic-gradient(rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) 0, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.getUpDeg / 360}%, #F9F9FC ${100 * this.getUpDeg / 360}%, #F9F9FC ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'})`
				} else if (this.gobedDeg === 0) {
					return `conic-gradient(rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) 0, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.getUpDeg / 360}%, #F9F9FC ${100 * this.getUpDeg / 360}%, #F9F9FC`
				} else if (this.getUpDeg === 0) {
					return `conic-gradient(#F9F9FC 0, #F9F9FC ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'})`
				} else {
					return `conic-gradient(#F9F9FC 0, #F9F9FC ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.gobedDeg / 360}%, rgba(94, 92, 230, ${this.step === 1 ? '0.6' : '0.4'}) ${100 * this.getUpDeg / 360}%, #F9F9FC ${100 * this.getUpDeg / 360}%, #F9F9FC`
				}
			},
			circleBackground() {
				if ((this.sleepDeg >= 180 && this.wakeDeg <= 180 && this.wakeDeg > 0) ||
					(this.sleepDeg > 0 && this.sleepDeg < 180 && this.wakeDeg > 0 && this.wakeDeg < 180 && this.sleepDeg >
						this.wakeDeg) ||
					(this.sleepDeg > 180 && this.sleepDeg < 360 && this.wakeDeg > 180 && this.wakeDeg < 360 && this
						.sleepDeg > this.wakeDeg)) {
					return `conic-gradient(rgba(94, 92, 230, 0.6) 0, rgba(94, 92, 230, 0.6) ${100 * this.wakeDeg / 360}%, #F9F9FC ${100 * this.wakeDeg / 360}%, #F9F9FC ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6) ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6)`
				} else if (this.sleepDeg === 0) {
					return `conic-gradient(rgba(94, 92, 230, 0.6) 0, rgba(94, 92, 230, 0.6) ${100 * this.wakeDeg / 360}%, #F9F9FC ${100 * this.wakeDeg / 360}%, #F9F9FC`
				} else if (this.wakeDeg === 0) {
					return `conic-gradient(#F9F9FC 0, #F9F9FC ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6) ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6)`
				} else {
					return `conic-gradient(#F9F9FC 0, #F9F9FC ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6) ${100 * this.sleepDeg / 360}%, rgba(94, 92, 230, 0.6) ${100 * this.wakeDeg / 360}%, #F9F9FC ${100 * this.wakeDeg / 360}%, #F9F9FC`
				}
			},
			gobedTimeShow() {
				const hour = parseInt(this.gobedDeg / 15) > 9 ? parseInt(this.gobedDeg / 15) : '0' + parseInt(this
					.gobedDeg / 15)
				const minutes = Math.round((this.gobedDeg % 15) / 0.25) > 9 ? Math.round((this.gobedDeg % 15) / 0.25) :
					'0' + Math.round((this.gobedDeg % 15) / 0.25)
				return hour + ':' + minutes
			},
			getUpTimeShow() {
				const hour = parseInt(this.getUpDeg / 15) > 9 ? parseInt(this.getUpDeg / 15) : '0' + parseInt(this
					.getUpDeg / 15)
				const minutes = Math.round((this.getUpDeg % 15) / 0.25) > 9 ? Math.round((this.getUpDeg % 15) / 0.25) :
					'0' + Math.round((this.getUpDeg % 15) / 0.25)
				return hour + ':' + minutes
			},
			sleepTimeShow() {
				const hour = parseInt(this.sleepDeg / 15) > 9 ? parseInt(this.sleepDeg / 15) : '0' + parseInt(this
					.sleepDeg / 15)
				const minutes = Math.round((this.sleepDeg % 15) / 0.25) > 9 ? Math.round((this.sleepDeg % 15) / 0.25) :
					'0' + Math.round((this.sleepDeg % 15) / 0.25)
				return hour + ':' + minutes
			},
			wakeTimeShow() {
				const hour = parseInt(this.wakeDeg / 15) > 9 ? parseInt(this.wakeDeg / 15) : '0' + parseInt(this.wakeDeg /
					15)
				const minutes = Math.round((this.wakeDeg % 15) / 0.25) > 9 ? Math.round((this.wakeDeg % 15) / 0.25) : '0' +
					Math.round((this.wakeDeg % 15) / 0.25)
				return hour + ':' + minutes
			},
      
			// 睡眠时长
			sleepDuration() {
        const { hours, minutes } = this.timeDiff(this.wakeTimeShow, this.sleepTimeShow)
				return hours + '小时' + minutes + '分钟'
			},
			// 卧床时长
			bedRestTime() {
          const { hours, minutes } = this.timeDiff(this.getUpTimeShow, this.gobedTimeShow)
          return hours + '小时' + minutes + '分钟'
			}
		},
		methods: {
      // 计算时间差
      timeDiff(time1, time2) {
        // 将时间字符串转换为分钟数
        const getMinutes = (timeStr) => {
          const [hours, minutes] = timeStr.split(':').map(Number);
          return hours * 60 + minutes;
        };

        // 转换为分钟计算
        let minutes1 = getMinutes(time1);
        let minutes2 = getMinutes(time2);
        
        // 计算差值（如果为负则加24小时）
        let diffMinutes = minutes1 - minutes2;
        if (diffMinutes < 0) {
          diffMinutes += 24 * 60;
        }
        
        // 转换回时:分格式
        const hours = Math.floor(diffMinutes / 60);
        const minutes = diffMinutes % 60;
        
        // 格式化输出，保证两位数显示
        return {
          hours: hours,
          minutes: minutes
        };
      },

			neverSleep() {
				this.$emit('neverSleep')
			},
			getTimeText(deg) { // 18点前今天 18点后昨日
				return deg > 270 ? '昨日' : '今天'
			},
			// 开始按钮move
			touchStartMove(e) {
				if (this.step === 1) {
					this.handleTouchGobed(e)
				} else {
					this.handleTouchSleep(e)
				}
			},

			// 开始按钮end
			touchStartEnd(e) {
				if (this.step === 1) {
					this.handleTouchGobed(e)
				} else {
					this.handleTouchSleep(e)
				}
			},

			// 上床按钮
			handleTouchGobed(e) {
				let nowX = e.changedTouches[0].clientX;
				let nowY = e.changedTouches[0].clientY;
				let deg = 180 - Math.atan2(nowX - this.centerX, nowY - this.centerY) * 180 / Math.PI
				if (deg > 360) {
					deg = deg - 360
				} else if (deg === 0) {
					deg = 360
				}

				// 五分钟换算 5分钟是1.25deg
				deg = Math.round(deg / 1.25) * 1.25

				// 终点起点需相差16度-300度
				if ((deg >= 180 && this.getUpDeg <= 180) ||
					(deg > 0 && deg < 180 && this.getUpDeg > 0 && this.getUpDeg < 180 && deg > this.getUpDeg) ||
					(deg > 180 && deg < 360 && this.getUpDeg > 180 && this.getUpDeg < 360 && deg > this.getUpDeg)) { // 跨0点
					if ((this.getUpDeg + 360 - deg) < 16) { // 16度边缘
						this.gobedDeg = deg
						this.getUpDeg = (16 - (360 - deg) + 360) % 360 ? (16 - (360 - deg) + 360) % 360 : 360
					} else if ((this.getUpDeg + 360 - deg) > 300) { // 300度边缘
						this.gobedDeg = deg
						this.getUpDeg = (300 - (360 - deg) + 360) % 360 ? (300 - (360 - deg) + 360) % 360 : 360
					} else {
						this.gobedDeg = deg
					}
				} else { // 不跨0点
					if ((this.getUpDeg - deg) < 16) { // 16度边缘
						this.gobedDeg = deg
						this.getUpDeg = (deg + 16) % 360 ? (deg + 16) % 360 : 360
					} else if ((this.getUpDeg - deg) > 300) { // 300度边缘
						this.gobedDeg = deg
						this.getUpDeg = (deg + 300) % 360 ? (deg + 300) % 360 : 360
					} else {
						this.gobedDeg = deg
					}
				}
				this.$emit('changeTime', this.getData())
			},

			// 熟睡按钮
			handleTouchSleep(e) {
				let nowX = e.changedTouches[0].clientX;
				let nowY = e.changedTouches[0].clientY;
				let deg = 180 - Math.atan2(nowX - this.centerX, nowY - this.centerY) * 180 / Math.PI
				if (deg > 360) {
					deg = deg - 360
				} else if (deg === 0) {
					deg = 360
				}
							
				// 五分钟换算 5分钟是1.25deg
				deg = Math.round(deg / 1.25) * 1.25

				// 终点起点需相差16度-300度
				if ((this.gobedDeg >= 180 && this.getUpDeg <= 180) ||
					(this.gobedDeg > 0 && this.gobedDeg < 180 && this.getUpDeg > 0 && this.getUpDeg < 180 && this
						.gobedDeg > this.getUpDeg) ||
					(this.gobedDeg > 180 && this.gobedDeg < 360 && this.getUpDeg > 180 && this.getUpDeg < 360 && this
						.gobedDeg > this.getUpDeg)) { // 上床-起床 跨0点
					if ((deg >= 180 && this.wakeDeg <= 180) ||
						(deg > 0 && deg < 180 && this.wakeDeg > 0 && this.wakeDeg < 180 && deg > this.wakeDeg) ||
						(deg > 180 && deg < 360 && this.wakeDeg > 180 && this.wakeDeg < 360 && deg > this.wakeDeg)
					) { // 熟睡-睡醒 跨0点
						if ((this.wakeDeg + 360 - deg) % 360 < 16) { // 16度边缘
							const endDeg = (16 - (360 - deg) + 360) % 360 ? (16 - (360 - deg) + 360) % 360 : 360
							if (endDeg > 0 && endDeg <= this.getUpDeg) {
								this.wakeDeg = endDeg
								this.sleepDeg = deg
							}
						} else {
							if (deg >= this.gobedDeg && deg <= 360 && this.wakeDeg > 0 && this.wakeDeg <= this.getUpDeg) {
								this.sleepDeg = deg
							}
						}
					} else {
						if ((this.wakeDeg + 360 - deg) % 360 < 16) { // 16度边缘
							const endDeg = (16 - (360 - deg) + 360) % 360 ? (16 - (360 - deg) + 360) % 360 : 360
							if (endDeg >= this.gobedDeg && endDeg <= 360 && deg >= this.gobedDeg && deg <= 360) {
								this.wakeDeg = endDeg
								if (deg >= this.gobedDeg && deg < endDeg) {
									this.sleepDeg = deg
								}
							} else if (endDeg > 0 && endDeg <= this.getUpDeg && deg > 0 && deg <= this.getUpDeg - 16) {
								this.wakeDeg = endDeg
								if (deg > 0 && deg < endDeg) {
									this.sleepDeg = deg
								}
							}
						} else {
							if (deg >= this.gobedDeg && deg <= this.wakeDeg - 16 && this.wakeDeg >= this.gobedDeg + 16 &&
								this.wakeDeg <= 360) {
								this.sleepDeg = deg
							} else if (deg > 0 && deg <= this.wakeDeg - 16 && this.wakeDeg >= 16 && this.wakeDeg <= this
								.getUpDeg) {
								this.sleepDeg = deg
							}
						}
					}
				} else { // 上床-起床 不跨0点
					if ((this.wakeDeg - deg) < 16) { // 16度边缘
						const endDeg = (deg + 16) % 360 ? (deg + 16) % 360 : 360
						if (endDeg <= this.getUpDeg && endDeg >= this.gobedDeg - 16) {
							this.wakeDeg = endDeg
							this.sleepDeg = deg
						}
					} else {
						if (deg >= this.gobedDeg && deg <= this.getUpDeg - 16) {
							this.sleepDeg = deg
						}
					}
				}
				this.$emit('changeTime', this.getData())
			},

			touchEndMove(e) {
				if (this.step === 1) {
					this.handleTouchGetUp(e)
				} else {
					this.handleTouchWake(e)
				}
			},

			touchEndEnd(e) {
				if (this.step === 1) {
					this.handleTouchGetUp(e)
				} else {
					this.handleTouchWake(e)
				}
			},

			// 起床按钮
			handleTouchGetUp(e) {
				let nowX = e.changedTouches[0].clientX;
				let nowY = e.changedTouches[0].clientY;
				let deg = 180 - Math.atan2(nowX - this.centerX, nowY - this.centerY) * 180 / Math.PI
				if (deg > 360) {
					deg = deg - 360
				} else if (deg === 0) {
					deg = 360
				}

				// 五分钟换算 5分钟是1.25deg
				deg = Math.round(deg / 1.25) * 1.25

				// 终点起点需相差16度-300度
				if ((this.gobedDeg >= 180 && deg <= 180) ||
					(this.gobedDeg > 0 && this.gobedDeg < 180 && deg > 0 && deg < 180 && this.gobedDeg > deg) ||
					(this.gobedDeg > 180 && this.gobedDeg < 360 && deg > 180 && deg < 360 && this.gobedDeg > deg)) { // 跨0点
					if ((deg + 360 - this.gobedDeg) < 16) { // 16度边缘
						this.getUpDeg = deg
						this.gobedDeg = (360 + deg - 16 + 360) % 360 ? (360 + deg - 16 + 360) % 360 : 360
					} else if ((deg + 360 - this.gobedDeg) > 300) { // 300度边缘
						this.getUpDeg = deg
						this.gobedDeg = (360 + deg - 300 + 360) % 360 ? (360 + deg - 300 + 360) % 360 : 360
					} else {
						this.getUpDeg = deg
					}
				} else { // 不跨0点
					if ((deg - this.gobedDeg) < 16) { // 16度边缘
						this.getUpDeg = deg
						this.gobedDeg = (deg - 16 + 360) % 360 ? (deg - 16 + 360) % 360 : 360
					} else if ((deg - this.gobedDeg) > 300) { // 300度边缘
						this.getUpDeg = deg
						this.gobedDeg = (deg - 300 + 360) % 360 ? (deg - 300 + 360) % 360 : 360
					} else {
						this.getUpDeg = deg
					}
				}
				this.$emit('changeTime', this.getData())
			},

			// 睡醒按钮
			handleTouchWake(e) {
				let nowX = e.changedTouches[0].clientX;
				let nowY = e.changedTouches[0].clientY;
				let deg = 180 - Math.atan2(nowX - this.centerX, nowY - this.centerY) * 180 / Math.PI
				if (deg > 360) {
					deg = deg - 360
				} else if (deg === 0) {
					deg = 360
				}

				// 五分钟换算 5分钟是1.25deg
				deg = Math.round(deg / 1.25) * 1.25

				// 终点起点需相差16度-300度
				if ((this.gobedDeg >= 180 && this.getUpDeg <= 180) ||
					(this.gobedDeg > 0 && this.gobedDeg < 180 && this.getUpDeg > 0 && this.getUpDeg < 180 && this
						.gobedDeg > this.getUpDeg) ||
					(this.gobedDeg > 180 && this.gobedDeg < 360 && this.getUpDeg > 180 && this.getUpDeg < 360 && this
						.gobedDeg > this.getUpDeg)) { // 上床-起床 跨0点

					if ((this.sleepDeg >= 180 && deg <= 180) ||
						(this.sleepDeg > 0 && this.sleepDeg < 180 && deg > 0 && deg < 180 && this.sleepDeg > deg) ||
						(this.sleepDeg > 180 && this.sleepDeg < 360 && deg > 180 && deg < 360 && this.sleepDeg > deg)
					) { // 熟睡-睡醒 跨0点
						if ((deg + 360 - this.sleepDeg) % 360 < 16) { // 16度边缘
							const startDeg = (deg - 16 + 360) % 360 ? (deg - 16 + 360) % 360 : 360
							if (startDeg > this.gobedDeg && startDeg <= 360) {
								this.sleepDeg = endDeg
								this.wakeDeg = deg
							}
						} else {
							if (deg <= this.getUpDeg && deg > 0 && this.sleepDeg >= this.gobedDeg && this.sleepDeg <=
								360) {
								this.wakeDeg = deg
							}
						}
					} else { // 熟睡-睡醒 不跨0点
						if ((deg + 360 - this.sleepDeg) % 360 < 16) { // 16度边缘
							const startDeg = (deg - 16 + 360) % 360 ? (deg - 16 + 360) % 360 : 360
							if (startDeg >= this.gobedDeg && startDeg <= 360 && deg > this.gobedDeg && deg <= 360) {
								this.sleepDeg = startDeg
								if (deg > this.sleepDeg && deg <= 360) {
									this.wakeDeg = deg
								}
							} else if (startDeg > 0 && startDeg < this.getUpDeg && deg > 0 && deg <= this.getUpDeg) {
								this.sleepDeg = startDeg
								if (deg > startDeg && deg <= this.getUpDeg) {
									this.wakeDeg = deg
								}
							}
						} else {
							if (deg >= this.sleepDeg + 16 && deg <= 360 && this.sleepDeg >= this.gobedDeg && this
								.sleepDeg <= 344) {
								this.wakeDeg = deg
							} else if (deg >= this.sleepDeg + 16 && deg <= this.getUpDeg && this.sleepDeg > 0 && this
								.sleepDeg <= this.getUpDeg - 16) {
								this.wakeDeg = deg
							}
						}
					}
				} else { // 上床-起床 不跨0点
					if ((deg - this.sleepDeg) < 16) { // 16度边缘
						const startDeg = (deg - 16 + 360) % 360 ? (deg - 16 + 360) % 360 : 360
						if (startDeg >= this.gobedDeg && startDeg <= this.getUpDeg - 16) {
							this.sleepDeg = startDeg
							this.wakeDeg = deg
						}
					} else {
						if (deg >= this.gobedDeg + 16 && deg <= this.getUpDeg) {
							this.wakeDeg = deg
						}
					}
				}
				this.$emit('changeTime', this.getData())
			},

			// 返给父组件数据
			getData() {
				return {
					nightPrepareSleepDate: this.gobedTimeShow,
					nightAsleepDate: this.sleepTimeShow,
					wakeDate: this.wakeTimeShow,
					getUpDate: this.getUpTimeShow,
					// sleepDuration: this.sleepDuration,
					// bedRestTime: this.bedRestTime
				}
			},
		},
		watch: {
			step(val) {
				this.sleepDeg = this.gobedDeg
				this.wakeDeg = this.getUpDeg
			}
		}
	}
</script>

<style lang="scss" scoped>
	.clock {
		width: 100%;
		height: 100%;
		position: relative;

		.clock-top {
			padding: 40rpx 20rpx;
			display: flex;
			justify-content: space-between;

			.up-item {
				flex: 1;
				padding: 24rpx 0 20rpx 0;
				margin: 0 12rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				background: rgba(235, 235, 245, 0.3);
				border-radius: 16px;

				.item-title {
					display: inline-flex;
					align-items: center;

					.title-icon {
						width: 32rpx;
						height: 32rpx;
					}

					.title-text {
						padding-left: 8rpx;
						font-size: 24rpx;
						font-weight: bold;
						color: #595959;
					}
				}

				.item-time {
					padding: 16rpx 0 8rpx 0;
					font-size: 40rpx;
					line-height: 40rpx;
					font-weight: bold;
					color: #5E5CE6;
				}

				.item-text {
					font-size: 20rpx;
					line-height: 20rpx;
					color: #5E5CE6;
				}
			}
		}

		.clock-inner {
			width: 696rpx;
			height: 696rpx;
			margin: 0 auto;

			.circle-out {
				width: 696rpx;
				height: 696rpx;
				position: relative;
				background: #fff;
				border-radius: 50%;

				.circle-bg {
					position: absolute;
					top: 69rpx;
					left: 69rpx;
					width: 558rpx;
					height: 558rpx;
				}

				.circle-num {
					position: absolute;
					font-size: 24rpx;
					line-height: 24rpx;
					font-weight: bold;
					color: #999;
				}

				.circle-num-0 {
					top: 173rpx;
					left: 341rpx;
					color: #2C2C2E;
				}

				.circle-num-2 {
					top: 193rpx;
					left: 423rpx;
				}

				.circle-num-4 {
					top: 253rpx;
					left: 487rpx;
				}

				.circle-num-6 {
					top: 335rpx;
					left: 507rpx;
					color: #2C2C2E;
				}

				.circle-num-8 {
					top: 419rpx;
					left: 487rpx;
				}

				.circle-num-10 {
					top: 479rpx;
					left: 415rpx;
				}

				.circle-num-12 {
					top: 499rpx;
					left: 333rpx;
					color: #2C2C2E;
				}

				.circle-num-14 {
					top: 479rpx;
					left: 251rpx;
				}

				.circle-num-16 {
					top: 419rpx;
					left: 195rpx;
				}

				.circle-num-18 {
					top: 335rpx;
					left: 173rpx;
					color: #2C2C2E;
				}

				.circle-num-20 {
					top: 253rpx;
					left: 195rpx;
				}

				.circle-num-22 {
					top: 193rpx;
					left: 251rpx;
				}

				.circle-big-progress {
					position: absolute;
					top: 0;
					left: 0;
					width: 696rpx;
					height: 696rpx;
					border-radius: 50%;
					mask: radial-gradient(transparent, transparent 285rpx, #000 285rpx, #000 0);
				}

				.circle-progress {
					position: absolute;
					top: 69rpx;
					left: 69rpx;
					width: 558rpx;
					height: 558rpx;
					border-radius: 50%;
					mask: radial-gradient(transparent, transparent 216rpx, #000 216rpx, #000 0);
				}

				.circle-start-box {
					position: absolute;
					top: 69rpx;
					left: calc(50% - 32rpx);
					width: 64rpx;
					height: 279rpx;
					z-index: 1;
					transform-origin: bottom center;

					.circle-start {
						position: absolute;
						top: 0;
						left: 0;
						width: 64rpx;
						height: 64rpx;
					}
				}

				.circle-big-start-box {
					position: absolute;
					top: 0;
					left: calc(50% - 32rpx);
					width: 64rpx;
					height: 348rpx;
					z-index: 1;
					transform-origin: bottom center;

					.circle-start {
						position: absolute;
						top: 0;
						left: 0;
						width: 64rpx;
						height: 64rpx;
					}
				}

				.circle-end-box {
					position: absolute;
					top: 69rpx;
					left: calc(50% - 32rpx);
					width: 64rpx;
					height: 279rpx;
					z-index: 1;
					transform-origin: bottom center;

					.circle-end {
						position: absolute;
						top: 0;
						left: 0;
						width: 64rpx;
						height: 64rpx;
					}
				}

				.circle-big-end-box {
					position: absolute;
					top: 0;
					left: calc(50% - 32rpx);
					width: 64rpx;
					height: 348rpx;
					z-index: 1;
					transform-origin: bottom center;

					.circle-end {
						position: absolute;
						top: 0;
						left: 0;
						width: 64rpx;
						height: 64rpx;
					}
				}
			}
		}

		.clock-bottom {
			padding: 40rpx 32rpx;
			display: flex;

			.bottom-item {
				flex: 1;
				display: inline-flex;
				flex-direction: column;
				align-items: center;

				.item-title {
					font-size: 24rpx;
					line-height: 32rpx;
					padding-bottom: 16rpx;
					color: #8E8E93;
				}

				.item-time {
					font-size: 32rpx;
					line-height: 32rpx;
					font-weight: bold;
					color: #2C2C2E;
				}
			}
		}

		.tips {
			font-size: 14px;
			color: #5E5CE6;
			display: block;
			text-align: center;
		}
		.tipsTop{
			margin-top: 20rpx;
		}
	}
</style>