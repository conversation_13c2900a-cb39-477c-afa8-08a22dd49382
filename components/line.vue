<template>
  <view class="chartbox">
    <l-echart class="chartbox-content" ref="chartRef"></l-echart>
  </view>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps(['xAxis', 'stayBed', 'sleepTime', 'sleepEfficiencies']);
const chartRef = ref(null);

const option = {
  grid: {
    top: 50,
    left: 10,
    right: 10,
    bottom: 30
  },
  legend: {
    data: ['卧床时长', '睡眠时长', '睡眠效率'],
    top: '0%',
    clickable: false,
    selectedMode: false,
    itemWidth: 20,
    itemHeight: 2,
    icon: 'rect',
    textStyle: {
      color: 'rgba(107, 107, 107, 1)',
      fontSize: 16
    }
  },
  xAxis: {
    data: props.xAxis,
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(221, 221, 221, 1)',
        width: 2
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: 'rgba(107, 107, 107, 1)',
        fontSize: 12
      }
    }
  },
  yAxis: [{
    type: 'value',
    nameTextStyle: {
      color: 'rgba(107, 107, 107, 1)',
      padding: [0, 0, 0, 0],
      fontSize: 12
    },
    splitLine: {
      show: false,
      lineStyle: {
        type: 'dashed'
      }
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    axisLabel: {
      show: false,
      textStyle: {
        color: 'rgba(107, 107, 107, 1)',
        fontSize: 12
      }
    }
  }],
  series: [
    {
      name: '卧床时长',
      type: 'line',
      itemStyle: {
        normal: {
          color: '#FF9F0A',
          lineStyle: {
            color: '#FF9F0A',
            width: 4
          },
          areaStyle: {
            color: '#fff'
          }
        }
      },
      data: props.stayBed
    },
    {
      name: '睡眠时长',
      type: 'line',
      symbolSize: 6,
      itemStyle: {
        normal: {
          color: '#32ADE6',
          lineStyle: {
            color: '#32ADE6',
            width: 4
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#CCEFFF' }
            ])
          }
        }
      },
      data: props.sleepTime
    },
    {
      name: '睡眠效率',
      type: 'line',
      symbolSize: 6,
      itemStyle: {
        normal: {
          color: '#30D158',
          lineStyle: {
            color: '#30D158',
            width: 4
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#30D158' }
            ])
          }
        }
      },
      data: props.sleepEfficiencies
    }
  ]
};

onMounted(() => {
  setTimeout(async () => {
    if (!chartRef.value) return;
    const myChart = await chartRef.value.init(echarts);
    myChart.setOption(option);
  }, 300);
});

// 监听 props 的变化
watch(() => [props.xAxis, props.stayBed, props.sleepTime, props.sleepEfficiencies], () => {
  if (chartRef.value) {
    const myChart = chartRef.value.getEchartsInstance();
    myChart.setOption({
      xAxis: {
        data: props.xAxis
      },
      series: [
        {
          name: '卧床时长',
          data: props.stayBed
        },
        {
          name: '睡眠时长',
          data: props.sleepTime
        },
        {
          name: '睡眠效率',
          data: props.sleepEfficiencies
        }
      ]
    });
  }
}, { deep: true });
</script>

<style lang="scss">
.chartbox {
  width: 100%;
  height: 426rpx;

  &-content {
    width: 100%;
    height: 426rpx;
  }
}
</style>