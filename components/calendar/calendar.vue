<template>
	<!-- 左右滑动切换月份 -->
	<view @touchstart="handleTouchStart" @touchend="handleTouchEnd">
		<view class="titleBox" v-if="!state.showMonthOrWeek">
			<view class="titleBox-icon">
				<view class="titleBox-icon-content" @click="changeMonth('before')">
					上一月
				</view>
			</view>
			<view class="title">{{ currentYear }}年{{ currentMonth }}月</view>
			<view class="titleBox-icon afterBox">
				<view class="titleBox-icon-content" @click="changeMonth('after')"
					v-if="state.currentInitMonth !=  `${currentYear}年${currentMonth}月`">
					下一月
				</view>
			</view>
		</view>
		<view class="week">
			<view class="weekday" v-for="(item, index) in week" :key="index">{{ item }}</view>
		</view>
		<!-- 日历 -->
		<view class="calendarbBox">
			<view class="singleDay" v-for="(item, index) in state.currentMonthAllDate" :key="index"
				@click="selectedDate(item)">
				<text class="dayTextB" v-if="item.month != 'current'">{{ item.number }}</text>
				<text class="dayTextA"
					v-if="item.month == 'current' && state.currentToday == item.date">{{ item.number }}</text>
				<text class="dayText"
					v-if="item.month == 'current' && state.currentToday != item.date">{{ item.number }}</text>
				<view class="point" v-if="currentSchemeDayFinish && currentSchemeDayFinish.indexOf(item.date) > -1">
					<image class="point-icon" src="https://cbti.zhisongkeji.com/uniapp-static/finish.png" mode="">
					</image>
				</view>
				<!-- 当前日期选中的标志 -->
				<view :class="'selectedDayBGColor ' + (state.showMonthOrWeek ? 'arrow':'')"
					v-if="state.currentToday == item.date"></view>
				<!-- 当天有计划+当前日期选中的标志 -->
				<view :class="'pointA ' + (state.currentToday == item.date ? 'whiteline' :'')"
					v-if="diaryDates.indexOf(item.date) > -1"></view>
			</view>
		</view>
		<!-- 周时展示 -->
		<view class="daytask flex-col" v-if="state.showMonthOrWeek">
			<view class="daytask-label">
				睡眠方案
			</view>
			<view class="daytask-process">
				<view class="daytask-process-current" :style="{width:`${process}%`}">

				</view>
			</view>
			<view class="daytask-num">
				已完成{{process}}%
			</view>
			<view class="daytask-btn flex-row" v-if="process !== 100" @click="start()">
				<image class="iconImg" src="https://cbti.zhisongkeji.com/uniapp-static/start.png" mode=""></image>
				开始学习
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		reactive,
		toRefs,
		computed,
		onMounted,
		ref,
		watch
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		onLoad,
		onHide
	} from '@dcloudio/uni-app';
	import {
		formatDate
	} from '../../utils';
	// 进度
	// showType 周/月
	//endDate 初始化代替currentDate
	//currentSchemeDayFinish 当前方案已经完成的
	const props = defineProps(['process', 'showType', 'currentSchemeDayFinish', 'endDate', 'diaryDates'])
	const emits = defineEmits(['start', 'changeCalendar','changeMonth'])

	function start() {
		emits('start')
	}
	const store = useStore();
	const state = reactive({
		week: ['日', '一', '二', '三', '四', '五', '六'],
		currentYear: '',
		currentMonth: '',
		currentToday: '',
		// 0周日，1周一
		monthFirstDayCurrentWeek: '',
		monthFinallyDayCurrentWeek: '',
		//currentMonthAllDate里面是一个一个的对象 ，对象属性number当前日期，isPlan当天是否有计划，month是否是当前月里面的日期，因为要显示不同的样式。还以根据需要在添加其他属性。
		currentMonthAllDate: [],
		lastMonthDateNumber: 0,
		nextMonthDateNumber: 0,
		// showMonthOrWeek为true,代表显示周，false显示月
		showMonthOrWeek: true,
		currentTodayDate: '',
		initialX: '',
		currentMonthNum: '',
		currentInitMonth: formatDate(new Date(), 'YYYY年MM月'),
	});
	const {
		currentMonthNum,
		initialX,
		currentTodayDate,
		showMonthOrWeek,
		lastMonthDateNumber,
		nextMonthDateNumber,
		currentMonthAllDate,
		week,
		currentMonth,
		currentYear,
		currentToday,
		monthFirstDayCurrentWeek,
		monthFinallyDayCurrentWeek,
	} = toRefs(state);

	watch(() => props.showType,
		(newVal, oldVal) => {
			// 判断显示周/月
			state.showMonthOrWeek = props.showType == 'month' ? false : true
			state.currentTodayDate = props.endDate
			state.currentToday = props.endDate

			// state.currentTodayDate = '2024-12-04'
			// state.currentToday = '2024-12-04'
			
			// 逻辑
		}, {
			deep: true,
			immediate: true
		})
	
	// 今天凌晨
	/**
	 * 记录手指触碰初始位置
	 */
	const handleTouchStart = (event) => {
		state.initialX = event.changedTouches[0].clientX;
	};

	/**
	 * 左右滑动事件
	 */
	const handleTouchEnd = (event, index) => {
		if (state.showMonthOrWeek) return
		const currentX = event.changedTouches[0].clientX;
		if (currentX - state.initialX > 20) {
			//往右滑动，上个月
			state.currentTodayDate = state.currentMonth == 1 ? new Date(`${state.currentYear - 1}/12/1`) : new Date(
				`${state.currentYear}/${state.currentMonthNum - 1}/1`);
			getAllDatesOfCurrentMonth(state.currentTodayDate);
			return;
		}
		if (state.initialX - currentX > 20) {
			// 往左滑动，下个月
			state.currentTodayDate = state.currentMonth == 12 ? new Date(`${state.currentYear + 1}/1/1`) : new Date(
				`${state.currentYear}/${state.currentMonthNum + 1}/1`);
			getAllDatesOfCurrentMonth(state.currentTodayDate);
			return;
		}
	};
	const changeMonth = (type) => {
		if (state.showMonthOrWeek) return
		if (type == 'before') {
			//往右滑动，上个月
			state.currentTodayDate = state.currentMonth == 1 ? new Date(`${state.currentYear - 1}/12/1`) : new Date(
				`${state.currentYear}/${state.currentMonthNum - 1}/1`);
			getAllDatesOfCurrentMonth(state.currentTodayDate);
			return;
		} else {
			// 往左滑动，下个月
			state.currentTodayDate = state.currentMonth == 12 ? new Date(`${state.currentYear + 1}/1/1`) : new Date(
				`${state.currentYear}/${state.currentMonthNum + 1}/1`);
			getAllDatesOfCurrentMonth(state.currentTodayDate);
			return;
		}
	};
	/**
	 * 选中哪天
	 */
	const selectedDate = (item) => {
		let {date,month} = item
		//展示为周时不允许点击
		if (state.showMonthOrWeek || month != 'current') return
		// Wed Aug 07 2024 00:00:00 GMT+0800 (中国标准时间)
		state.currentToday = date;
		state.currentTodayDate = date;
		emits('changeCalendar', date)
		// getAllDatesOfCurrentMonth(state.currentTodayDate);
		// 下面去后端请求计划数据，并展示
	};

	/**
	 * 切换显示周还是月
	 */
	const changeShowWeekOrMonth = () => {
		state.showMonthOrWeek = !state.showMonthOrWeek;
		getAllDatesOfCurrentMonth(state.currentTodayDate);
	};

	/**
	 * 得到当前月份/当前周的所有日期，dateData某天日期
	 */
	const getAllDatesOfCurrentMonth = (dateData) => {
		state.currentMonthAllDate = [];
		const today = new Date(dateData);
		state.currentToday = formatDate(today, 'YYYY-MM-dd');
		state.currentYear = today.getFullYear();
		state.currentMonthNum = today.getMonth() + 1;
		emits('changeMonth', state.currentToday)


		if (today.getMonth() + 1 < 10) {
			state.currentMonth = '0' + (today.getMonth() + 1);
		} else {
			state.currentMonth = today.getMonth() + 1;
		}
		// 上个月总天数
		const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
		state.lastMonthDateNumber = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0).getDate();
		// 下个月总天数
		const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
		state.nextMonthDateNumber = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0).getDate();
		const dates = [];
		// 用if,else判断显示周还是月
		if (state.showMonthOrWeek) {
			// 显示当前选中日期所在周
			let day = today.getDay();
			let startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day);
			let endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 6);
			let currentMonthTwo = today.getMonth() + 1;
			for (let i = startDate; i <= endDate;) {
				let monthFlag = '';
				if (new Date(i).getMonth() + 1 == currentMonthTwo) {
					monthFlag = 'current';
				} else if (new Date(i).getMonth() + 1 > currentMonthTwo) {
					monthFlag = 'last';
				} else {
					monthFlag = 'next';
				}
				dates.push(new Date(i));
				state.currentMonthAllDate.push({
					number: i.getDate(),
					month: monthFlag,
					date: formatDate(new Date(i), 'YYYY-MM-dd')
				});
				i.setDate(i.getDate() + 1);
			}
		} else {
			// 显示当前选中日期所在月
			const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
			const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
			state.monthFirstDayCurrentWeek = firstDayOfMonth.getDay();
			state.monthFinallyDayCurrentWeek = lastDayOfMonth.getDay();
			// 补充上个月显示在本月的天数，例如5.1是周三，则周日周一周二显示上个月
			if (state.monthFirstDayCurrentWeek != 0) {
				// 判断上个月是不是上一年
				let isLastYearNumber = lastMonth.getMonth() + 1 == 12 ? 1 : 0;
				for (let i = 0; i < state.monthFirstDayCurrentWeek; i++) {
					state.currentMonthAllDate.push({
						number: state.lastMonthDateNumber - state.monthFirstDayCurrentWeek + 1,
						month: 'last',
						date: `${state.currentYear - isLastYearNumber}/${lastMonth.getMonth() + 1}/${state.lastMonthDateNumber - state.monthFirstDayCurrentWeek + 1}`
					});
					state.lastMonthDateNumber++;
				}
			}
			for (let i = firstDayOfMonth; i <= lastDayOfMonth;) {
				dates.push(new Date(i));
				state.currentMonthAllDate.push({
					number: dates.length,
					month: 'current',
					date: formatDate(new Date(i), 'YYYY-MM-dd')
				});
				i.setDate(i.getDate() + 1);
			}
			if (state.monthFinallyDayCurrentWeek != 6) {
				// 判断下个月是不是下一年
				let yearNumber = nextMonth.getMonth() + 1 == 1 ? 1 : 0;
				for (let i = 0; i < 6 - state.monthFinallyDayCurrentWeek; i++) {
					state.currentMonthAllDate.push({
						number: i + 1,
						month: 'next',
						date: `${state.currentYear + yearNumber}/${nextMonth.getMonth() + 1}/${i + 1}`
					});
				}
			}
		}
		return dates;

	};
	getAllDatesOfCurrentMonth(state.currentTodayDate);

	// 可删除，做了几个假数据，假装几天有计划的,isPlan为true代表当天有计划。
	// state.currentMonthAllDate[2].isPlan = true;
	// state.currentMonthAllDate[4].isPlan = true;

</script>

<style scoped lang="scss">
	@import './style.scss';
</style>