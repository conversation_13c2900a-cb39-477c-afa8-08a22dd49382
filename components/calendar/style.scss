.calendarbBox {
	width: 100%;
	// margin-left: 7.5rpx;
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
	.singleDay {
		width: 90rpx;
		height: 72rpx;
		line-height: 72rpx;
		color: #333;
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		position: relative;
		.dayText {
			position: relative;
			z-index: 9;
		}
		.dayTextA {
			position: relative;
			z-index: 9;
			color: #fff;
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
		}
		.dayTextB {
			position: relative;
			z-index: 9;
			color: #D2D2D2;
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
		}
		.point {
			width: 28rpx;
			height: 28rpx;
			position: absolute;
			top: 24rpx;
			left: 50%;
			z-index: 1;
		transform: translate(-46%, 0);
			&-icon {
				width: 100%;
				height: 100%;
			}
		}
		.selectedDayBGColor {
			width: 72rpx;
			height: 72rpx;
			background-color: #5e5ce6;
			border-radius: 50%;
			position: absolute;
			top: 0;
			left: 10rpx !important;
		}
		.arrow {
			width: 72rpx;
			height: 72rpx;
			background-color: #5e5ce6;
			border-radius: 50%;
			position: absolute;
			top: 0;
			left: 9rpx;
		}
		.arrow::after {
			content: '';
			display: block;
			position: absolute;
			left: calc(50% - 24rpx);
			bottom: -20rpx;
			border-left: 24rpx solid transparent;
			border-right: 24rpx solid transparent;
			border-bottom: 20rpx solid #8b8af6;
		}
		.pointA {
			width: 16rpx;
			height: 6rpx;
			background-color: #30d158;
			border-radius: 4rpx;
			position: absolute;
			top: 57rpx;
			left:50%;
			transform: translate(-50%, 0);
		}
		.whiteline{
			background-color: #fff;
		}
	}
}
.transitionTime {
	transition: transform 0.5s;
}
.arrowBox {
	width: 686rpx;
	height: 109rpx;
	line-height: 109rpx;
	position: relative;
	.arrowButtonRegion {
		width: 100rpx;
		height: 50rpx;
		line-height: 50rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: #00b488;
		border-radius: 10rpx;
		color: #fff;
		font-size: 28rpx;
		text-align: center;
	}
}
.titleBox {
	width: 100%;
	height: 80rpx;
	background: rgba(235, 235, 245, 0.3);
	border-radius: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16rpx;
	margin-bottom: 24rpx;
	// &-icon {
	// 	width: 32rpx;
	// 	height: 32rpx;
	// 	>image{
	// 	width: 32rpx;
	// 	height: 32rpx;
	// 	}
	// }
	&-icon {
		flex: 1;
		display: flex;
		&-content {
			height: 40rpx;
			font-size: 24upx;
			color: #6260e7;
			border-radius: 32upx;
			border: 2upx solid #acabf1;
			padding: 0 10px;
			background: #ececfe;
			display: flex;
			align-items: center;
		}
	}
	.afterBox {
		display: flex;
		justify-content: flex-end;
	}
	.title {
		flex: 2;
		font-weight: bold;
		font-size: 28rpx;
		color: #1c1c1e;
		text-align: center;
	}
}
.week {
	width: 100%;
	height: 39rpx;
	font-weight: 400;
	font-size: 20rpx;
	color: #949499;
	display: flex;
	justify-content: space-around;

	// .weekday{
	// }
}
.daytask {
	width: 100%;
	height: 248rpx;
	background-image: url('https://cbti.zhisongkeji.com/uniapp-static/bg.png');
	background-size: 100% 100%;
	padding: 32rpx;
	justify-content: space-between;
	margin-top: 20rpx;
	&-label {
		font-weight: bold;
		font-size: 32rpx;
		color: #ffffff;
	}
	&-process {
		width: 240rpx;
		height: 8rpx;
		background: rgba(0, 0, 0, 0.1);
		border-radius: 4rpx;
		&-current {
			height: 8rpx;
			background: #ffffff;
			border-radius: 4rpx;
		}
	}
	&-num {
		font-weight: 400;
		font-size: 24rpx;
		color: #ffffff;
	}
	&-btn {
		width: 196rpx;
		height: 64rpx;
		background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.72) 100%);
		box-shadow: 0px 4rpx 12rpx 0rpx rgba(255, 255, 255, 0.3);
		border-radius: 32rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #5e5ce6;
		align-items: center;
		justify-content: center;
	}
}
