<template>
	<view class="items-component">
		<view v-for="(item, index) of list" :key="index" class="item" :id="item.id">
			<view class="item-query" v-if="item.role == 'query'">
				<image class="item-query-avatar" src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png" mode=""></image>
				<view class="item-query-content">
					<view class="query">
						{{ item.message }}
					</view>
				</view>
			</view>
			<view class="item-answer" v-if="item.role == 'answer'">
				<!-- //todo 医生头像 -->
				<image  class="item-answer-avatar" v-if="doctor.avatar"  :src="`https://cbti.zhisongkeji.com/zhisong-cbti/sys/common/static/${doctor.avatar}`"  mode="widthFix"></image>
				<image class="item-answer-avatar" src="https://cbti.zhisongkeji.com/uniapp-static/user_icon_default.png" mode=""></image>
				<view class="item-answer-content">
					<view class="answer">
						{{ item.message }}
					</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				required: true,
				default: () => [],
			},
				
			// doctor:{
			// 	type: Object,
			// 	required: true,
			// 	default: () => {},
			// }
		},
		data() {
			return {
doctor:{}
			};
		},
		methods: {

		}
	};
</script>

<style lang='scss'>
	@import './style.scss';
</style>