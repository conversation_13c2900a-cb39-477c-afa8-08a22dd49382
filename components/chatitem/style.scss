.items-component {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 20px;

	.item {
		width: 100%;
		display: flex;
		flex-direction: column;
		
		&-query {
			display: flex;
			flex-direction: row-reverse;
			justify-content: end;
			margin-bottom: 32rpx;
			&-avatar {
				width: 64rpx;
				min-width: 64rpx;
				height: 58rpx;
				border-radius: 50%;
				margin-left: 16rpx;
			}
			&-content {
				.query {
					color: #ffffff;
					background: #9694ff;
					border-radius: 24rpx  0px 24rpx 24rpx;
					padding: 16rpx 24rpx;
					border: 2rpx solid #9694ff;
				}
			}
		}

		&-answer {
			display: flex;
			margin-bottom: 32rpx;
			&-avatar {
				width: 64rpx;
				min-width: 64rpx;
				height: 58rpx;
				border-radius: 50%;
				margin-right: 16rpx;
			}
			&-content {
				
				.answer {
					padding: 16rpx 24rpx;
					color: #181e26;
					background: #fff;
					border-radius: 24rpx 24rpx  24rpx 0px;
					border: 2rpx solid #e5e5ea;
				}
			}
		}
	}
}
