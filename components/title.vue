<template>
	<view class="labelComponent w-full flex-row">
		<label class="label">{{label}}</label>
		<label class="next flex-row" @click="goPage()">
			{{next}}
			<image class="next-icon" src="https://cbti.zhisongkeji.com/uniapp-static/next.png" mode=""></image>
		</label>
	</view>
</template>

<script setup>
	const props = defineProps(['label','next','path'])
	function goPage(){
		uni.navigateTo({
			url:props.path
		})
	}
</script>

<style lang="scss">
	.labelComponent {
		padding: 12px 0;
		justify-content: space-between;

		.label {
			font-weight: bold;
			font-size: 16px;
			color: #1c1c1e;
		}
		.next {
			font-weight: 400;
			font-size: 12px;
			color: #8e8e93;
			align-items: center;

			&-icon {
				width: 12px;
				height: 12px;
			}
		}
	}
</style>