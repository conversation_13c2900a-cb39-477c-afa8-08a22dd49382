<template>
	<view class="chartbox"><l-echart class="chartbox-content" ref="chartRef"></l-echart></view>
</template>

<script setup>
	import {
		reactive,
		toRefs,
		computed,
		onMounted,
		ref,
		watch
	} from 'vue';
	const props = defineProps(['point', 'efficiency'])
	import * as echarts from 'echarts'
	const chartRef = ref(null)
	// const point = 68
	// const efficiency = '64';
	const option = {
		series: [{
				type: 'pie',
				clockWise: false,
				startAngle: 90,
				radius: ['80%', '90%'],
				data: [100],
				itemStyle: {
					color: '#eee',
					normal: {
						label: {
							show: false
						}
					}

				},
				animation: false
			},
			{
				type: 'pie',
				clockWise: false,
				startAngle: 90,
				// endAngle: endAngle,
				labelLine: {
					show: false
				},
				radius: ['77%', '93%'],
				data: [{
						value: props.efficiency,
						label: {
							normal: {
								formatter: `{label|睡眠效率}\n{value|${props.efficiency}%}`,
								position: 'center',
								show: true,
								textStyle: {
									fontSize: '10',
									fontWeight: 'normal',
									color: '#5E5CE6',
									rich: {
										// label: {
										// 	fontSize: '10',
										// 	fontWeight: 'normal',
										// },
										value: {
											fontSize: '24',
											fontWeight: 'bold',
											color: '#5E5CE6',
										},
										unit: {
											fontSize: '10',
											color: '#757575'
										}
									}
								}
							}
						},
						itemStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1,
									[{
											offset: 0,
											color: '#9291FE',
										},
										{
											offset: 0.5,
											color: '#5E5CE6',
										},
										{
											offset: 1,
											color: '#415BF0',
										},
									],
									false
								),
								borderRadius: ['50%', '50%']
							}
						}
					},
					{
						value: 100 - props.efficiency,
						itemStyle: {
							normal: {
								color: 'transparent',
								borderCap: 'round'
							}
						}
					}
				]
			}
		]
	};

	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(async () => {
			if (!chartRef.value) return
			const myChart = await chartRef.value.init(echarts)
			myChart.setOption(option)
		}, 300)
	})

	watch(
		() => [props.efficiency],
		([newEfficiency]) => {
			if (!chartRef.value) return
			const optionValue = {
				series: [{
						type: 'pie',
						clockWise: false,
						startAngle: 90,
						radius: ['80%', '90%'],
						data: [100],
						itemStyle: {
							color: '#eee',
							normal: {
								label: {
									show: false
								}
							}

						},
						animation: false
					},
					{
						type: 'pie',
						clockWise: false,
						startAngle: 90,
						// endAngle: endAngle,
						labelLine: {
							show: false
						},
						radius: ['80%', '90%'],
						data: [{
								value: newEfficiency,
								label: {
									normal: {
										formatter: `{label|睡眠效率}\n{value|${newEfficiency}%}`,
										position: 'center',
										show: true,
										textStyle: {
											fontSize: '10',
											fontWeight: 'normal',
											color: '#5E5CE6',
											rich: {
												value: {
													fontSize: '24',
													fontWeight: 'bold',
													color: '#5E5CE6',
												},
												unit: {
													fontSize: '10',
													color: '#757575'
												}
											}
										}
									}
								},
								itemStyle: {
									normal: {
										color: new echarts.graphic.LinearGradient(
											0,
											0,
											0,
											1,
											[{
													offset: 0,
													color: '#9291FE',
												},
												{
													offset: 0.5,
													color: '#5E5CE6',
												},
												{
													offset: 1,
													color: '#415BF0',
												},
											],
											false
										),
										borderRadius: ['50%', '50%']
									}
								}
							},
							{
								value: 100 - newEfficiency,
								itemStyle: {
									normal: {
										color: 'transparent',
										borderCap: 'round'
									}
								}
							}
						]
					}
				]
			};
			chartRef.value.setOption(optionValue)
		}
	);
</script>

<style lang="scss">
	.chartbox {
		width: 100%;
		height: 95%;

		&-content {
			width: 100px;
			height: 100px;
		}
	}
</style>