<template>
	<view class="mycourseitem-component">
		<view :class="'mycourseitem ' + item.className" :key="item.courseId" v-for="item of list"
			@click="goInfo(item)">
			<view class="mycourseitem-name">
				<view class="cname">
					{{item.name}}
				</view>
				<view class="cinfo">
					<view class="cinfo-status">
						{{item.statusText}}
					</view>
					<image class="cinfo-next" src="https://cbti.zhisongkeji.com/uniapp-static/mycoursenext.png" mode=""></image>
				</view>
			</view>
			<view class="mycourseitem-date">
				开始时间：{{item.startDate}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "myCourseItem",
		props: {
			list: {
				type: Array,
				default: []
			},
			typecourses:{
				type:String,
				default: ''
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
			goInfo(item) {
				uni.navigateTo({
					url: '/pages/users/myCourse/info/info?id=' + item.id +'&typecourses='+this.typecourses
				})
				// 正在进行中
				// if(this.typecourses == 'ingcourses'){
				// 	uni.navigateTo({
				// 		url:'/pages/course/course'
				// 	})
				// }else if(this.typecourses == 'finishcourses'){
				// 	uni.navigateTo({
				// 		url: '/pages/users/myCourse/info/info?id=' + item.id
				// 	})
				// }
			}
		}
	}
</script>

<style lang="scss">
	.mycourseitem-component {
		display: flex;
		flex-direction: column;

		.mycourseitem {
			width: 100%;
			padding: 20rpx 30rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			margin-bottom: 26upx;

			&-name {
				width: 100%;
				display: flex;
				align-items: center;

				.cname {
					width: calc(100% - 142upx);
					font-size: 28upx;
					color: #333333;
					margin-bottom: 40rpx;
				}

				.cinfo {
					display: flex;
					align-items: center;

					&-status {
						width: 107upx;
						height: 41upx;
						text-align: center;
						font-size: 28upx;
						color: #fff;
						border-radius: 5upx;
					}

					&-next {
						width: 13upx;
						height: 22upx;
						margin-left: 22upx;
					}
				}

			}

			&-date {
				font-size: 24upx;
				color: #949494;
			}
		}

		.ing {
			background: linear-gradient(to right, #EBF1FF, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				background: #706DFF;

			}
		}

		.finish {
			background: linear-gradient(to right, #F1FFDB, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				background: #7AAE2D;

			}
		}

		.stop {
			background: linear-gradient(to right, #FFF1F0, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				background: #FF5D51;

			}
		}
	}
</style>