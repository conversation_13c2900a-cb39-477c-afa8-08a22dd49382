<template>
	<view class="container">
		<view class="top--panel">
			<view class="searchHeader">
				<input class="uni-input" placeholder="搜索药物名称" placeholder-class='sug_p' :value="tradeName"
					@confirm="handleConfrim" @blur="handleConfrim" />
			</view>
		</view>
		<!-- 滚动区域 -->
		<view class="scroll-panel" id="scroll-panel">
			<view class="list-box">
				<view class="left">
					<scroll-view scroll-y="true" :style="{ 'height':scrollHeight + 'px' }"
						:scroll-into-view="leftIntoView">
						<view class="item" v-for="(item,index) in leftArray" :key="index"
							:class="{ 'active':index==leftIndex }" :id="'left-'+index" :data-index="index"
							@tap="leftTap">{{item.name}}</view>
					</scroll-view>
				</view>
				<view class="main">
					<scroll-view scroll-y="true" :style="{ 'height':scrollHeight + 'px' }" @scroll="mainScroll"
						:scroll-into-view="scrollInto" scroll-with-animation="true">
						<view class="item main-item" v-for="(item,index) in mainArray" :key="index" :id="'item-'+index">
							<view class="title">
								<!-- <view>{{item.title}}</view> -->
							</view>
							<view class="drugs" v-for="(item2,index2) in item.list" :key="index2"
								@click="chooseDrugs(item.list[index2])">
								<!-- <image src="/static/logo.png" mode=""></image> -->
								<view>
									<view class="drugType">{{item.list[index2].tradeName}}({{item.list[index2].commonName}})</view>
									<view class="drugDescribe">{{item.list[index2].specification}}</view>
								</view>
							</view>
						</view>
						<view class="fill-last" :style="{ 'height':fillHeight + 'px' }"></view>
					</scroll-view>
				</view>
			</view>
		</view>
		<!-- 底部面板 -->
		<view class="bottom-panel">
			<!-- 底部面板，可添加所需要放在页面底部的内容代码。比如购物车栏目 -->
			<view v-if="addDrugList.length == 0" class="bottomNone"></view>
			<view v-else class="bottomContent">
				<view class="title">已选择药物</view>
				<view class="content">
					<scroll-view class="list" scroll-y="true">
						<view class="item" v-for="item in addDrugList" :key="item.id">

							<view class="drugName">{{item.medicineName}}</view>
							<view class="drugSize">{{item.commonName}}</view>
							<view class="timeDosage">
								<view class="eatTime">
									<image class="icon"
										src="https://cbti.zhisongkeji.com/uniapp-static/<EMAIL>" mode="">
									</image>
									{{item.useDate}}
								</view>
								<view class="dosage">
									<image class="icon" src="https://cbti.zhisongkeji.com/uniapp-static/dosage.png"
										mode=""></image>
									{{item.dosage}}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="addButton" @click="addDrug()">确定</view>
			</view>
		</view>
		<template>
			<view class="timePopup popup" :class="timeSpecClass" @touchmove.stop.prevent="stopPrevent"
				@click="timeToggleSpec">
				<!-- 遮罩层 -->
				<view class="mask"></view>
				<view class="layer attr-content" @click.stop="stopPrevent">
					<view class="alertHead">选择时间</view>
					<view class="picker">
						<view class="timePicker">
							<picker-view class="picker-view" @change="timeChange">
								<picker-view-column>
									<view class="item" v-for="(item,index) in hours" :key="index">{{item}}</view>
								</picker-view-column>
								<picker-view-column>
									<view class="item" v-for="(item,index) in minutes" :key="index">{{item}}</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
					<view class="chooseButton" @click="timeButtonClick()">确定</view>
				</view>
			</view>
		</template>
		<template>
			<view class="dosagePopup popup" :class="dosageSpecClass" @touchmove.stop.prevent="stopPrevent"
				@click="dosageToggleSpec">
				<!-- 遮罩层 -->
				<view class="mask"></view>
				<view class="layer attr-content" @click.stop="stopPrevent">
					<view class="alertHead">选择用量</view>
					<view class="picker">
						<view class="dosagePicker">
							<picker-view class="picker-view" @change="dosageChange">
								<picker-view-column>
									<view class="item" v-for="(item,index) in dosage" :key="index">{{item}}</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
					<view class="chooseButton" @click="dosageButtonClick()">确定</view>
				</view>
			</view>
		</template>
	</view>

</template>

<script>
	import {
		formatDate,
	} from '/utils/index.js'
	import {
		getMedicine,
		getMedicineCategory,
	} from '@/assets/api/index.js'
	export default {
		data() {
			const dosage = ["1/4片", "1/3片", "1/2片", "2/3片", "3/4片", "1片", "1.25片", "1.5片",
				"1.75片", "2片", "2.25片", "2.5片", "2.75片", "3片", "3.5片", "4片",
				"4.5片", "5片", "5.5片", "6片"
			];
			const hours = ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"];
			const minutes = ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"];

			for (let i = 10; i <= 23; i++) {
				hours.push(i.toString())
			}

			for (let i = 10; i <= 59; i++) {
				minutes.push(i.toString())
			}
			return {
				dosage,
				hours,
				minutes,
				currentTime: '',
				scrollHeight: '100vh',
				scrollTopSize: 0,
				fillHeight: 0, // 填充高度，用于最后一项低于滚动区域时使用
				leftArray: [],
				mainArray: [],
				topArr: [],
				leftIndex: 0,
				scrollInto: '',
				addDrugList: [],
				chooseItem: {},
				timeSpecClass: 'none',
				dosageSpecClass: 'none',
				drugName: "",
				drugDes: "",
				drugDosageEat: "1/4片",
				drugTimeEat: "00:00",
				tradeName: ''
			}
		},
		onLoad(parameter) {
			let {
				currentTime,
			} = parameter;
			this.currentTime = currentTime;
			// this.getTime(currentTime);
		},
		computed: {
			/* 计算左侧滚动位置定位 */
			leftIntoView() {
				return `left-${this.leftIndex > 3 ? (this.leftIndex-3):0}`;
			}
		},
		mounted() {

			/* 等待DOM挂载完成 */
			this.$nextTick(() => {
				/* 在非H5平台，nextTick回调后有概率获取到错误的元素高度，则添加200ms的延迟来减少BUG的产生 */
				setTimeout(() => {
					/* 等待滚动区域初始化完成 */
					this.initScrollView().then(() => {
						/* 获取列表数据，你的代码从此处开始 */
						/*加载列表数据*/
						this.loadCategoryList();
						this.loadDrugList();
					})
				}, 200);
			})

		},
		methods: {
			handleConfrim(event) {
				this.tradeName = event.detail.value
				this.loadDrugList()
			},
			loadCategoryList() {
				getMedicineCategory().then(result => {
					this.leftArray = result;
				})
			},
			loadDrugList() {
				getMedicine({
					tradeName: `*${this.tradeName}*`
				}).then(result => {
					this.mainArray = result;
					this.$nextTick(() => {
						this.getElementTop();
					});
				})
			},
			/* 初始化滚动区域 */
			initScrollView() {

				return new Promise((resolve, reject) => {
					const query = wx.createSelectorQuery().in(this)
					let view = query.select('#scroll-panel');
					view.boundingClientRect(res => {

						this.scrollTopSize = res.top;
						this.scrollHeight = res.height;
						this.$nextTick(() => {
							resolve();
						})
					}).exec();
				});
			},
			/* 获取元素顶部信息 */
			getElementTop() {
				new Promise((resolve, reject) => {
					const query = wx.createSelectorQuery().in(this)
					let view = query.selectAll('.main-item');
					view.boundingClientRect(data => {
						resolve(data);
					}).exec();
				}).then((res) => {
					let topArr = res.map((item) => {
						return item.top - this.scrollTopSize; /* 减去滚动容器距离顶部的距离 */
					});
					this.topArr = topArr;
					/* 获取最后一项的高度，设置填充高度。判断和填充时做了 +-20 的操作，是为了滚动时更好的定位 */
					let last = res[res.length - 1].height;
					if (last - 20 < this.scrollHeight) {
						this.fillHeight = this.scrollHeight - last + 20;
					}
				});
			},
			/* 主区域滚动监听 */
			mainScroll(e) {
				let top = e.detail.scrollTop;
				let index = 0;
				/* 查找当前滚动距离 */
				for (let i = (this.topArr.length - 1); i >= 0; i--) {
					/* 在部分安卓设备上，因手机逻辑分辨率与rpx单位计算不是整数，滚动距离与有误差，增加2px来完善该问题 */
					if ((top + 2) >= this.topArr[i]) {
						index = i;
						break;
					}
				}
				this.leftIndex = (index < 0 ? 0 : index);
			},
			/* 左侧导航点击 */
			leftTap(e) {
				let index = e.currentTarget.dataset.index;
				this.scrollInto = `item-${index}`;
			},
			// 选择药物
			chooseDrugs(item) {
				this.chooseItem = item;
				this.dosageToggleSpec();
			},
			// 点击确定,添加药物
			addDrug() {
				this.$emit('addDrug', this.addDrugList)
			},
			// 选择用量确定按钮
			dosageButtonClick() {
				this.dosageToggleSpec();
				this.timeToggleSpec();
			},
			// 选择时间确定按钮
			timeButtonClick() {
				this.timeToggleSpec();
				let drug = {
					recordDate: this.currentTime || formatDate(new Date(), 'YYYY-MM-dd hh:mm:ss'),
					medicineId: this.chooseItem.id,
					medicineName: this.chooseItem.tradeName,
					commonName: this.chooseItem.commonName,
					medicineSpecification: this.chooseItem.specification,
					useDate: this.drugTimeEat,
					dosage: this.drugDosageEat
				}
				this.addDrugList.push(drug);
			},
			// 移除已选药物按钮点击
			delButtonClick(item) {
				this.addDrugList.splice(this.addDrugList.indexOf(item), 1);
			},

			stopPrevent() {},
			dosageToggleSpec() {
				//用量底部弹窗弹出收回
				if (this.dosageSpecClass === 'show') {
					this.dosageSpecClass = 'hide';
					setTimeout(() => {
						this.dosageSpecClass = 'none';
					}, 250);
				} else if (this.dosageSpecClass === 'none') {
					this.dosageSpecClass = 'show';
				}
			},
			timeToggleSpec() {
				//时间底部弹窗弹出收回
				if (this.timeSpecClass === 'show') {
					this.timeSpecClass = 'hide';
					setTimeout(() => {
						this.timeSpecClass = 'none';
					}, 250);
				} else if (this.timeSpecClass === 'none') {
					this.timeSpecClass = 'show';
				}
			},
			timeChange: function(e) {
				const val = e.detail.value;
				var hour = this.hours[val[0]] ? this.hours[val[0]] : "00";
				var minute = this.minutes[val[1]] ? this.minutes[val[1]] : "00";
				this.drugTimeEat = hour + ":" + minute;
			},
			dosageChange: function(e) {
				const val = e.detail.value;
				this.drugDosageEat = this.dosage[val[0]];
			},
		}
	}
</script>

<style lang="scss">
	@import "./style.scss";
</style>