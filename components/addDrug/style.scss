/* 容器 */
.container {
	position: absolute;
	z-index: 10;
	width: 100%;
	height: 100vh;
	top:0;
	left: 0;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: flex-start;
	align-content: flex-start;
	background: #FBFBFB;
	
	&>view {
		width: 100%;
	}

	.scroll-panel {
		flex-grow: 1;
		height: 0;
		overflow: hidden;
		margin-top: 8px;
	}

	.bottom-panel {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);  
		padding-bottom: env(safe-area-inset-bottom);
		max-height: 276px;
		
		.title {
			display: flex;
			width: 100%;
			height: 44px;
			background-color: #FBFBFB;
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #303133;
			line-height: 20px;
			align-items: center;
			margin-left: 15px;
		}
		
		.addButton {
			display: flex;
			width: 100%;
			height: 49px;
			background: #7F8FE9;
			font-size: 16px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 22px;
			align-items: center;
			justify-content: center;
			margin-top: 8px;
		}
		
		.list {
			display: flex;
			width: 100%;
			height: 172px;
			background-color: #FFFFFF;
			.item {
				display: flex;
				height: 100px;
				flex-direction: column;
				padding: 0 16px;
				border-bottom: 1px solid #EAEEF5;

				.close {
					display: flex;
					position: absolute;
					min-width: 42px;
					min-height: 42px;
					right: 16px;
					align-items: center;
					justify-content: flex-end;
					
					.icon {
						width: 14px;
						height: 14px;
					}
				}
				.drugName {
					display: flex;
					flex-direction: row;
					margin-top: 12px;
					font-size: 14px;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #303133;
					line-height: 20px;
					align-items: center;
				}
				.drugSize {
					font-size: 12px;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #8F9399;
					line-height: 17px;
					margin-top: 2px;
				}
				.timeDosage {
					display: flex;
					width: 100%;
					justify-content: space-between;
					
					.eatTime {
						display: flex;
						font-size: 12px;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #7F8FE9;
						line-height: 17px;
						margin-top: 8px;
						align-items: center;
						
						.icon {
							width: 12px;
							height: 12px;
							margin-right: 3px;
						}
					}
					.dosage {
						display: flex;
						font-size: 12px;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #7F8FE9;
						line-height: 17px;
						margin-left: 5px;
						align-items: center;
						
						.icon {
							width: 12px;
							height: 12px;
							margin-right: 3px;
						}
					}
				}
			}
		}
	}
	.attr-content {
		display: flex;
		width: 100%;
		flex-direction: column;
		
		.alertHead {
			display: flex;
			width: 100%;
			height: 50px;
			border-bottom: 5px solid #F8F8F8;
			align-items: center;
			justify-content: center;
		}
		
		.picker {
			picker-view {
			    width: 100%;
			    height: 120px;
			    margin-top:20rpx;
			}
			
			.item {
			    line-height: 34px;
			    text-align: center;
			}
			border-bottom: 5px solid #F8F8F8;
		}
	
		.chooseButton {
			display: flex;
			width: 100%;
			height: 55px;
			align-items: center;
			justify-content: center;
		}
	}
	
	.popup {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 99;
		
		&.show {
			display: block;
			.mask{
				animation: showPopup 0.2s linear both;
			}
			.layer {
				animation: showLayer 0.2s linear both;
			}
		}
		&.hide {
			.mask{
				animation: hidePopup 0.2s linear both;
			}
			.layer {
				animation: hideLayer 0.2s linear both;
			}
		}
		&.none {
			display: none;
		}
		.mask{
			position: fixed;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			background-color: rgba(0, 0, 0, 0.3);
		}
		.layer {
			position: fixed;
			z-index: 99;
			bottom: 0;
			width: 100%;
			min-height: 200px;
			border-radius: 10upx 10upx 0 0;
			background-color: #fff;
		   .btn{
				height: 66upx;
				line-height: 66upx;
				border-radius: 100upx;
				background:#fff;
				font-size: 16upx;
				color: #fff;
				margin: 30upx auto 20upx;
			}
		}
		@keyframes showPopup {
			0% {
				opacity: 0;
			}
			100% {
				opacity: 1;
			}
		}
		@keyframes hidePopup {
			0% {
				opacity: 1;
			}
			100% {
				opacity: 0;
			}
		}
		@keyframes showLayer {
			0% {
				transform: translateY(120%);
			}
			100% {
				transform: translateY(0%);
			}
		}
		@keyframes hideLayer {
			0% {
				transform: translateY(0);
			}
			100% {
				transform: translateY(120%);
			}
		}
	}
}

.searchHeader {
	display: flex;
	height: 48px;
	padding: 7px 15px;
	background-color: #FFFFFF;
	
	.uni-input {
		width: 100%;
		height: 34px;
		background: #F5F5F5;
		border-radius: 100px;
		padding: 0 15px;
		
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		line-height: 17px;
		
		.sug_p {
			color: #C0C4CC;
		}
	}
}

.list-box {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: flex-start;
	align-content: flex-start;
	font-size: 28rpx;

	.left {
		// display: flex;
		width: 120px;
		background-color: #F5F5F5;
		box-sizing: border-box;
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #8F9399;
		line-height: 20px;

		.item {
			display: flex;
			padding: 15px;
			height: 50px;
			position: relative;
			align-items: center;
			flex-direction: row;

			&:not(:first-child) {
				margin-top: 1px;
				
			}

			&.active {
				color: #303133;
				background-color: #FFFFFF;
				
				&::before {
					content: '';
					display: block;
					width: 4px;
					height: 20px;
					border-left: #7F8FE9 solid 4px;
					position: absolute;
					left: 0;
					// transform: scaleY(0.5);
					/* 1px像素 */
				}
			}
		}

		.fill-last {
			height: 0;
			width: 100%;
			background: none;
		}
	}

	.main {
		background-color: #fff;
		width: 0;
		flex-grow: 1;
		box-sizing: border-box;
		padding-right: 30px;

		.title {
			line-height: 64rpx;
			font-size: 24rpx;
			font-weight: bold;
			color: #666;
			background-color: #fff;
			position: sticky;
			top: 0;
			z-index: 19;
		}

		.item {
			padding-bottom: 10rpx;
			padding-left: 15px;
		}

		.drugs {
			display: flex;
			height: 66px;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: flex-start;
			align-items: center;
			align-content: center;
			border-bottom: #EAEEF5 solid 1px;

			.drugType {
				font-size: 14px;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #303133;
				line-height: 20px;
			}
			.drugDescribe {
				font-size: 12px;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #8F9399;
				line-height: 17px;
			}
		}
	}
}