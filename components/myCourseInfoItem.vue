<template>
	<view class="mycourseitem-component">
		<template v-for="item of list" :key="item.id">
			<view :class="'mycourseitem ' +(item.status == 1 && infoType.indexOf(item.type) != -1 ? 'finish' : (typecourses == 'ingcourses' && item.status == 0 ? 'ingcoursesUnfinish' :'' )) "
				@click="goPage(item)">
				<view class="mycourseitem-name">
					<view class="cname">
						{{item.elementName}}
					</view>
					<view class="cinfo">
						<view class="cinfo-status">
							{{statusTextHash[item.type == 4 && item.status ==2 ? 1 : item.status]}}
						</view>
						<image class="cinfo-next" src="https://cbti.zhisongkeji.com/uniapp-static/mycoursenext.png"
							mode="">
						</image>
					</view>
				</view>
				<view class="mycourseitem-type">
					{{typeHash[item.type]}}
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		name: "myCourseItem",
		props: {
			list: {
				type: Array,
				default: []
			},
			typecourses:{
				type: String,
				default: 'finishcourses'
			}
		},
		data() {
			return {
				infoType: [1, 2, 3],
				statusTextHash: {
					0: '待完成',
					1: '已完成',
					2: '已暂停',
					3: '进行中'
				},
				classHash: {
					0: 'ing',
					1: 'finish',
					2: 'stop',
					3: 'ing',
				},
				typeHash: {
					1: '文章',
					2: '视频',
					3: '放松训练',
					4: '量表',
					5: '问卷',
					6: '引导页'
				}

			};
		},
		methods: {
			goPage(item) {
				this.$emit('goPage', item)
			},

		}
	}
</script>

<style lang="scss">
	.mycourseitem-component {
		display: flex;
		flex-direction: column;

		.mycourseitem {
			width: 100%;
			height: 176upx;
			padding: 22upx 35upx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			margin-bottom: 26upx;
			background: linear-gradient(to right, #D8D8D8, #F8F9FE);
			border-radius: 36upx;
			
			.mycourseitem-name .cinfo .cinfo-status {
				color: #D8D8D8;
			}

			&-name {
				width: 100%;
				display: flex;
				align-items: center;

				.cname {
					width: calc(100% - 142upx);
					font-size: 28upx;
					color: #333333;
				}

				.cinfo {
					display: flex;
					align-items: center;

					&-status {
						width: 107upx;
						height: 41upx;
						text-align: center;
						font-size: 28upx;
					}

					&-next {
						width: 13upx;
						height: 22upx;
						margin-left: 22upx;
					}
				}

			}

			&-type {
				font-size: 26upx;
				color: #333333;
			}
		}

		.ing {
			background: linear-gradient(to right, #EBF1FF, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				color: #706DFF;
			}
		}
		
		.ingcoursesUnfinish{
			background: linear-gradient(to right, #FFE3BC, #F8F9FE);
			border-radius: 36upx;
			
			.mycourseitem-name .cinfo .cinfo-status {
				color: #FFE3BC;
			}
		}

		.finish {
			background: linear-gradient(to right, #F1FFDB, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				color: #7AAE2D;

			}
		}

		.stop {
			background: linear-gradient(to right, #FFF1F0, #F8F9FE);
			border-radius: 36upx;

			.mycourseitem-name .cinfo .cinfo-status {
				color: #FF5D51;
			}
		}
		
	}
</style>