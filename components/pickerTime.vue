<template>
	<picker-view :value="time" @change="bindChange" class="picker-view">
		<picker-view-column :key="pickerIndex" v-for="(picker,pickerIndex) of pickerData">
			<view :class="'item item' + pickerIndex" v-for="(item,index) in picker" :key="`${pickerIndex}-${index}`">{{item}}</view>
		</picker-view-column>
	</picker-view>
</template>

<script>
	export default {
		name:"pickerTime",
		data() {
			return {
				
			};
		}
	}
</script>

<style>

</style>