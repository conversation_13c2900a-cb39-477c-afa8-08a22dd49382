<template>
	<view class="container">
		<!-- <view > -->
		<!-- <view class="list-box">
				<view class="left">
					<scroll-view scroll-y="true" :style="{ 'height':scrollHeight + 'px' }"
						:scroll-into-view="leftIntoView">
						<view class="item" v-for="(item,index) in leftArray" :key="index"
							:class="{ 'active':index==leftIndex }" :id="'left-'+index" :data-index="index"
							@tap="leftTap">{{item.title}}</view>
					</scroll-view>
				</view>
			</view> -->

		<!-- </view> -->
		<picker-view class="scroll-panel" id="scroll-panel" :indicator-style="indicatorStyle" :value="value"
			@change="leftTap">
			<picker-view-column>
				<view class="item" v-for="(item,index) in leftArray" :key="index">{{item.title}}</view>
			</picker-view-column>
			<picker-view-column>
				<view class="item" v-for="(item,index) in dosage" :key="index">{{item}}</view>
			</picker-view-column>
		</picker-view>
		<!-- 底部面板 -->
		<view class="bottom-panel">
			<scroll-view class="list" scroll-y="true">
				<view class="title">
				已选择	
				</view>
				<view class="item" v-for="item in addDrinkList" :key="item.id">
					<view class="drugName">{{item.drinkName}}</view>
					<view class="timeDosage">
						<view class="eatTime">
							<image class="icon"
								src="https://cbti.zhisongkeji.com/uniapp-static/<EMAIL>" mode="">
							</image>
							{{item.useDate}}
						</view>
						<view class="dosage">
							<image class="icon" src="https://cbti.zhisongkeji.com/uniapp-static/dosage.png"
								mode=""></image>
							{{item.dosage}}
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="addButton" @click="addDrug()">确定</view>
		</view>
		<template>
			<view class="timePopup popup" :class="timeSpecClass" @touchmove.stop.prevent="stopPrevent"
				@click="timeToggleSpec">
				<!-- 遮罩层 -->
				<view class="mask"></view>
				<view class="layer attr-content" @click.stop="stopPrevent">
					<view class="alertHead">选择时间</view>
					<view class="picker">
						<view class="timePicker">
							<picker-view class="picker-view" @change="timeChange">
								<picker-view-column>
									<view class="item" v-for="(item,index) in hours" :key="index">{{item}}</view>
								</picker-view-column>
								<picker-view-column>
									<view class="item" v-for="(item,index) in minutes" :key="index">{{item}}</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
					<view class="chooseButton" @click="timeButtonClick()">确定</view>
				</view>
			</view>
		</template>
		<template>
			<view class="dosagePopup popup" :class="dosageSpecClass" @touchmove.stop.prevent="stopPrevent"
				@click="dosageToggleSpec">
				<!-- 遮罩层 -->
				<view class="mask"></view>
				<view class="layer attr-content" @click.stop="stopPrevent">
					<view class="alertHead">选择用量</view>
					<view class="picker">
						<view class="dosagePicker">
							<picker-view class="picker-view" @change="dosageChange">
								<picker-view-column>
									<view class="item" v-for="(item,index) in dosage" :key="index">{{item}}</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
					<view class="chooseButton" @click="dosageButtonClick()">确定</view>
				</view>
			</view>
		</template>
	</view>

</template>

<script>
	import {
		getMedicine,
		getMedicineCategory,
		getDictCode
	} from '@/assets/api/index.js'
	export default {
		data() {
			const dosage = ["100ml", "200ml", "300ml", "400ml", "500ml", "600ml", "700ml", "800ml", "900ml", "1000ml",
				"1100ml", "1200ml", "1300ml", "1400ml", "1500ml", "1600ml", "1700ml", "1800ml", "1900ml", "2000ml"
			];
			const hours = ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"];
			const minutes = ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"];

			for (let i = 10; i <= 23; i++) {
				hours.push(i.toString())
			}

			for (let i = 10; i <= 59; i++) {
				minutes.push(i.toString())
			}
			return {
				value: [0, 1],
				dosage,
				hours,
				minutes,
				addDrinkList: [],
				currentTime: '',
				scrollHeight: '100vh',
				scrollTopSize: 0,
				fillHeight: 0, // 填充高度，用于最后一项低于滚动区域时使用
				leftArray: [],
				mainArray: [],
				topArr: [],
				leftIndex: 0,
				scrollInto: '',
				chooseItem: {},
				timeSpecClass: 'none',
				dosageSpecClass: 'none',
				drugName: "",
				drugDes: "",
				drugDosageEat: "100ml",
				drugTimeEat: "00:00",
			}
		},
		onLoad(parameter) {
			let {
				currentTime,
			} = parameter;
			this.currentTime = currentTime;
			// this.getTime(currentTime);
		},

		mounted() {
			this.loadCategoryList();
		},
		methods: {
			loadCategoryList() {
				getDictCode('drink_type').then(result => {
					this.leftArray = result;
				})
			},
			loadDrugList() {
				getMedicine().then(result => {
					this.mainArray = result;
					this.$nextTick(() => {
						// this.getElementTop();
					});
				})
			},
			/* 左侧导航点击 */
			leftTap(e) {
				this.value = e.detail.value
				this.timeToggleSpec();
			},
			// 选择药物
			chooseDrugs(item) {
				this.chooseItem = item;
				this.dosageToggleSpec();
			},
			// 点击确定,添加药物
			addDrug() {
				this.$emit('addDrink', this.addDrinkList)
			},
			// 选择用量确定按钮
			dosageButtonClick() {
				this.dosageToggleSpec();
				this.timeToggleSpec();
			},
			// 选择时间确定按钮
			timeButtonClick() {
				this.timeToggleSpec();
				this.drugDosageEat = this.dosage[this.value[1]]
				let drink = {
					recordDate: this.currentTime,
					drinkName: this.leftArray[this.value[0]].title,
					useDate: this.drugTimeEat,
					dosage: this.drugDosageEat
				}
				this.addDrinkList.push(drink);
			},
			// 移除已选药物按钮点击
			delButtonClick(item) {
				this.addDrinkList.splice(this.addDrinkList.indexOf(item), 1);
			},

			stopPrevent() {},
			dosageToggleSpec() {
				//用量底部弹窗弹出收回
				if (this.dosageSpecClass === 'show') {
					this.dosageSpecClass = 'hide';
					setTimeout(() => {
						this.dosageSpecClass = 'none';
					}, 250);
				} else if (this.dosageSpecClass === 'none') {
					this.dosageSpecClass = 'show';
				}
			},
			timeToggleSpec() {
				//时间底部弹窗弹出收回
				if (this.timeSpecClass === 'show') {
					this.timeSpecClass = 'hide';
					setTimeout(() => {
						this.timeSpecClass = 'none';
					}, 250);
				} else if (this.timeSpecClass === 'none') {
					this.timeSpecClass = 'show';
				}
			},
			timeChange: function(e) {
				const val = e.detail.value;
				var hour = this.hours[val[0]] ? this.hours[val[0]] : "00";
				var minute = this.minutes[val[1]] ? this.minutes[val[1]] : "00";
				this.drugTimeEat = hour + ":" + minute;
			},
			dosageChange: function(e) {
				const val = e.detail.value;
				this.drugDosageEat = this.dosage[val[0]];
			},
		}
	}
</script>

<style lang="scss">
	@import "./style.scss";
</style>