export function formatDate(dateStr, format) {
	 const date = new Date(dateStr);
	const map = {
		'Y': date.getFullYear().toString(), //年份
		'M': (date.getMonth() + 1).toString().padStart(2, '0'), //月份
		'd': date.getDate().toString().padStart(2, '0'), //日
		'h': date.getHours().toString().padStart(2, '0'), //小时
		'm': date.getMinutes().toString().padStart(2, '0'), //分钟
		's': date.getSeconds().toString().padStart(2, '0'), //秒
	};

	format = format.replace(/(Y+|M+|d+|h+|m+|s+)/g, (all) => {
		return map[all.charAt(0)];
	});

	return format;
}

export function getDayDifference(date) {
    const now = new Date();
    now.setHours(0, 0, 0, 0); // 将 now 的时间部分清零

    const futureDate = new Date(date);
    futureDate.setHours(0, 0, 0, 0); // 将 futureDate 的时间部分清零

    const diff = now - futureDate; // 计算时间差（毫秒）
    const days = diff / (1000 * 60 * 60 * 24); // 将毫秒转换为天数
    return Math.abs(Math.floor(days)); // 返回绝对值
}
export function getDay(day, today) {
    let doHandleMonth = (month) => {
        var m = month;
        if (month.toString().length == 1) {
            m = "0" + month;
        }
        return m;
    };

    // 如果没有传入 today，则使用当前日期
    var today = today || new Date();

    // 计算目标日期的毫秒值
    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;

    // 创建一个新的 Date 对象来表示目标日期，避免直接修改传入的 today 对象
    var targetDate = new Date(targetday_milliseconds);

    var tYear = targetDate.getFullYear();
    var tMonth = targetDate.getMonth();
    var tDate = targetDate.getDate();

    // 月份和日期的格式化处理
    tMonth = doHandleMonth(tMonth + 1);
    tDate = doHandleMonth(tDate);

    return tYear + "-" + tMonth + "-" + tDate;
}

export function isObjEmpty(obj) {
	return Object.keys(obj).length === 0;
}

//要在JavaScript中合并两个对象并取交集，即使用第一个对象的键，并且使用第二个对象的值
export function mergeAndIntersectObjects(obj1, obj2) {
	return Object.keys(obj1).reduce((acc, key) => {
		if (obj2.hasOwnProperty(key)) {
			acc[key] = obj2[key];
		}
		return acc;
	}, {});
}

// fnc 代表着你要执行的函数，delay代表着你希望执行函数的时间频率
export const throttle = (func, delay) => {
        // last 上一次是啥时候执行的，timer依然是记录定时器
        let last, timer;
        return () => {
            // 当前时间，隐式转换为数字类型
            // 每次用户敲击我都记录当前时间为now
            let now = +new Date();
            // 如果last也就是上次函数执行的时间
            // 当前时间并不满足时间间隔那么我们就进入if语句
            if (last && now < delay + last) {
            // 如果最后一次敲击,取消定时器
                clearTimeout(timer);
            // 定义一个延迟函数的执行，用来满足用户的最后一次敲击被执行
                timer = setTimeout(() => {
                    last = now;
                    func([键盘敲击值]);
                }, delay);
            }
            //如果用户没有点击过或者，已经隔了delay时间没有点击就执行函数
            else {
            // 记录当前函数执行的时间
                last = now;
                func();
            }
        }
    }
     // fnc 代表着你要执行的函数，delay代表着你希望多长时间后用户不进行操作了然后执行函数
export function debounce(fn, delay) {
        // 记录定时器的
        let timer = null;
        return function () {
        // 如果存在timer就表示有函数正在等待执行，
        //取消前面函数的等待，重新进行当前函数的等待
            if (timer) { clearInterval(timer) }
            timer = setTimeout(() => {
            fn(); }, 
            delay);
        }
    }
