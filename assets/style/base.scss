view {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

page {
	width: 100%;
	height: 100%;
}

.pointer {
	cursor: pointer;
}

.template {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 100;
	opacity: 0.5;
	background-size: 100% 100%;

	.img {
		width: 100%;
		height: 100%;
	}
}

.topBg {
	background: linear-gradient(180deg, #4e68ff 0%, rgba(219, 240, 255, 0) 50%);
}

.flex-col {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.flex-row {
	display: flex;
	align-items: center;
}

.w-full {
	width: 100%;
}
.w-btn {
	height: 32px;
	background: #ffffff;
	border-radius: 16px;
	border: 1px solid #5e5ce6;
	font-weight: 400;
	font-size: 14px;
	color: #5e5ce6;
	line-height: 32px;
	padding: 0 16px;
	display: flex;
	align-items: center;
	    white-space: nowrap;
}

.iconImg {
	width: 24rpx;
	height: 24rpx;
	margin-right: 8rpx;
}

.end {
	width: 100%;
	font-weight: 400;
	font-size: 24rpx;
	color: #b3b3d8;
	text-align: center;
	padding: 30rpx 0;
}
[class*='-pages'] {
	width: 100%;
	height: 100%;
	&.page-deal {
		padding-bottom: 100upx;
	}
}

::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}
