// 引入 request 文件
import request from '../request/index.js'

//获取当前月份睡眠日记的列表
export const getDiarys=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiary/list',
		method: 'get',
		data: params,
	})
}

 // 获取加密字符串
export const getAction =(params) => {
	return request({
		url: '/zhisong-cbti/sys/getEncryptedString',
		method: 'get',
		data: params,
	})
}
export const finishQuestionnaireInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/schemeQuestionnaire/finish',
		method: 'post',
		data: params,
	})
}
// 患者计划-获取问卷详情
export const getQuestionnaireInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/questionnaire/queryById',
		method: 'get',
		data: params,
	})
}
// 患者方案-计划-[课程（视频、文章、视频、引导页）]记录进度
export const recordProcess =(params) => {
	return request({
		url: '/zhisong-cbti/api/pa/paPatientStudyRecord/add',
		method: 'post',
		data: params,
	})
}

// 患者计划-通过计划ID获取方案视频详情
export const getCourseaudioInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/queryTrainBySchemeId',
		method: 'get',
		data: params,
	})
}
// 患者方案-计划-[放松训练]完成
export const finishCourseaudioInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/schemeTrain/progress',
		method: 'put',
		data: params,
	})
}

// 患者方案-计划-[课程（视频）]完成
export const finishCourseVideoInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/schemeSub/progress',
		method: 'put',
		data: params,
	})
}

// 患者方案-计划-[课程（文章、视频、引导页）]完成
export const finishCourseInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/schemeSub/finish?id='+ params,
		method: 'put',
		// data: params,
	})
}

// 患者计划-通过计划ID获取素材详情
export const getCourseInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/queryMaterialBySchemeId',
		method: 'get',
		data: params,
	})
}

//【首页】患者方案-用户当前的方案计划详情(2024-10新)
export const getCurrentSchemeGroupInfo =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/currentSchemeGroupInfo',
		method: 'get',
		data: params,
	})
}

//修改密码
export const editPassword =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/login/wxb4fa1015c94508f7/changePassword',
		method: 'get',
		data: params,
	})
}
//账号密码登录
export const loginAccount =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/login/wxb4fa1015c94508f7/login',
		method: 'get',
		data: params,
	})
}

//获取我的评测
export const getMeasureTask=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/measureTask/list',
		method: 'get',
		data: params,
	})
}

//写睡眠日记
export const createSleepDiary =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiary/add',
		method: 'post',
		data: params,
	})
}
// 字典值
export const getDictCode=(dictCode) => {
	return request({
		url: `/zhisong-cbti/sys/dict/getDictItems/${dictCode}`,
		method: 'get',
		// data: params,
	})
}

//根据code获取用户手机号
export const getPhone=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/wx/miniapp/getPhone',
		method: 'get',
		data: params,
	})
}

// 绑定手机号
export const loginMobile=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/wx/miniapp/bindPaPatient',
		method: 'post',
		data: params,
	})
}
// 小程序登录
export const login=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/wx/miniapp/login',
		method: 'get',
		data: params,
	})
}
//睡眠周报
export const getWeekSleep =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiary/sleepDiaryReportForWeek',
		method: 'post',
		data: params,
	})
}
// 获取计划详情
export const getPlanInfo=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/schemeDetail',
		method: 'get',
		data: params,
	})
}


// 获取医生列表
export const getDoctor=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient//doDoctor/myDoctorList',
		method: 'get',
		data: params,
	})
}


// 获取服药记录
export const getMedicineRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/medicine/medicineRecord',
		method: 'get',
		data: params,
	})
}


// 获取白天小睡记录
export const getSleepTime =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/sleepDiaryNap/listForToday',
		method: 'get',
		data: params,
	})
}

//新增放松训练记录
export const addRelaxRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiaryTrain/addList',
		method: 'post',
		data: params,
	})
}

//新增最困时间记录
export const addDrowsyRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiaryTired/addList',
		method: 'post',
		data: params,
	})
}

//新增饮品记录
export const addDrinkRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiaryDrink/addList',
		method: 'post',
		data: params,
	})
}

//新增小睡记录
export const addSleepRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/sleepDiaryNap/addList',
		method: 'post',
		data: params,
	})
}

// 新增服药记录
// medicineId 药物表id
// medicineName 药物表名字
// medicineSpecification 药品规格
// useDate 使用时间
// recordDate 服药日期
// dosage 用量

export const addMedicineRecord =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiaryMedicine/add',
		method: 'post',
		data: params,
	})
}

// 获取药品列表
export const getMedicine =(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/medicine/list',
		method: 'get',
		data: params,
	})
}
// 获取药品列表分类
export const getMedicineCategory=(params) => {
	return request({
		url: '/zhisong-cbti/api/patient/medicine/categoryList',
		method: 'get',
		data: params,
	})
}

// 训练列表
export const getTrain= (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/train/list',
		method: 'get',
		data: params,
	})
}

//训练分类列表
export const getCategoryTrain= (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/train/categoryList',
		method: 'get',
		data: params,
	})
}

//阅读详情新增访客
export const addBrowse = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient//article/addBrowse',
		method: 'get',
		data: params,
	})
}

// 睡眠日记单日
export const getSleepDiary = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/paSleepDiary/sleepDiaryDetail',
		method: 'post',
		data: params,
	})
}


//获取用户信息
export const getUser = (params) => {
	return request({
		// url: '/zhisong-cbti/api/patient/patient/queryById',
		url: '/zhisong-cbti/api/patient/patient/info',
		method: 'get',
		data: params,
	})
}

//获取是否是强制模式
export const getCompelStatus = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/getCompelStatus',
		method: 'get',
		data: params,
	})
}


//查询当天该患者的全部预约
export const getappointment = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/appointment/listAll',
		method: 'get',
		data: params,
	})
}
//查询该患者的全部预约
export const getAppointmentAll = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/appointment/list',
		method: 'get',
		data: params,
	})
}


// 新增预约
export const addappointment = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/appointment/add',
		method: 'post',
		data: params,
	})
}

// 新增留言
export const addleavemessage = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/dialogue/sendToDoctor',
		method: 'post',
		data: params,
	})
}

//获取留言列表
export const getleavemessage = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/dialogue/listDialogue',
		method: 'get',
		data: params,
	})
}

//获取阅读推荐详情
export const getReadInfo = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/article/queryById',
		method: 'get',
		data: params,
	})
}

//获取阅读推荐列表
export const getReads = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/article/list',
		method: 'get',
		data: params,
	})
}

// 心情日记
export const getFeelingList = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/moodDiary/list',
		method: 'get',
		data: params,
	})
}

// 心情日记详情
export const getfeeling = (params) => {
	return request({
		url: '/zhisong-cbti/pa/paMoodDiary/queryById',
		method: 'get',
		data: params,
	})
}

// 新增心情日记
export const addfeeling = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/moodDiary/add',
		method: 'post',
		data: params,
	})
}


// 编辑心情日记
export const editfeeling = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/moodDiary/edit',
		method: 'post',
		data: params,
	})
}


//患者方案-患者计划-强制模式获取正在执行的计划及详情（日期）
export const getExecuteDetail = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/scheme/getExecuteDetail',
		method: 'get',
		data: params,
	})
}

// 忧虑详情
export const getworried = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/anxious/queryById',
		method: 'get',
		data: params,
	})
}

// 编辑忧虑
export const editworried = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/anxious/edit',
		method: 'post',
		data: params,
	})
}

// 增加忧虑
export const addworried = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/anxious/add',
		method: 'post',
		data: params,
	})
}
// 忧虑列表
export const getWorriedList = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/anxious/list',
		method: 'get',
		data: params,
	})
}


// 医嘱任务列表
export const getMedicalOrderTask = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/doctorAdvice/list',
		method: 'get',
		data: params,
		// header: {} // 自定义
	})
}
//医嘱任务完成
export const finishMedicalOrderTask = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/doctorAdvice/finish',
		method: 'put',
		data: params,
	})
}
//量表任务列表
export const getPatientSchemeTask = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/measureTask/list',
		method: 'get',
		data: params,
	})
}

//量表信息
export const getMeasureInfo = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/measureTask/getMeasureInfo',
		method: 'get',
		data: params,
	})
}

//开始量表测评
export const startAnswer = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/answer/startAnswer',
		method: 'post',
		data: params,
	})
}

//开始量表测评
export const startQuestions = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/measureTask/startQuestions',
		method: 'get',
		data: params,
	})
}

//获取题目信息
export const loadAnswerData = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/answer/loadAnswerData',
		method: 'get',
		data: params,
	})
}

//量表答题-选项提交
export const addOption = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/result/addOption',
		method: 'post',
		data: params,
	})
}

//量表答题完成
export const uploadResult = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/result/uploadResult',
		method: 'get',
		data: params,
	})
}

//量表获取报告详情
export const getAssembleDataById = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/result/getAssembleDataById',
		method: 'get',
		data: params,
	})
}

//量表获取报告详情
export const getResultList = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/result/list',
		method: 'get',
		data: params,
	})
}

//首页的文章列表，睡眠处方，当日睡眠日记
export const getInfo = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/home/<USER>',
		method: 'get',
		data: params,
	})
}

//我的方案
export const getMyCourse = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/schemeGroup/list',
		method: 'get',
		data: params,
	})
}

//我的方案详情
export const getMyCourseInfo = (params) => {
	return request({
		url: '/zhisong-cbti/api/patient/schemeGroup/detail',
		method: 'get',
		data: params,
	})
}


