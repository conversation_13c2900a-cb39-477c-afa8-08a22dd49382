export function getGreeting() {
	const nowTime = new Date();
	const hour = nowTime.getHours();

	if (hour < 6) {
		return "凌晨好！"
	} else if (hour < 9) {
		return "早上好！"
	} else if (hour < 12) {
		return "上午好！"
	} else if (hour < 14) {
		return "中午好！"
	} else if (hour < 17) {
		return "下午好！"
	} else if (hour < 19) {
		return "傍晚好！"
	} else if (hour < 23) {
		return "晚上好！"
	} else {
		return "你好！"
	}
}
export function checkPhone(phoneNumber) {
	//正则表达式太老，不匹配193的手机号
  //const regex = /^(13[0-9]|14[5|7|9]|15[0-3|5-9]|16[2|5|6|7]|17[0-8]|18[0-9]|19[8|9])\d{8}$/;
  //2025-4-9 截止2024年的手机号正则表达式
  const regex = /^(?:\+?86)?1(?:3\d|4[0145689]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
  return regex.test(phoneNumber);
}