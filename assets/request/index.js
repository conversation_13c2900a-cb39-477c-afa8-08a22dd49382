const config = {
	//开发环境配置
	development: {
		// baseUrl: "http://127.0.0.1:8080",
		baseUrl: "https://cbti.zhisongkeji.com"
	},
	//生产环境配置
	production: {
		baseUrl: "https://cbti.zhisongkeji.com"
	}
}

import store from '../../store';

// 全局请求封装
const base_url = config[process.env.NODE_ENV].baseUrl
// 请求超出时间
const timeout = 5000

// 需要修改token，和根据实际修改请求头
const request = (params) => {
	let url = params.url;
	let method = params.method || "get";
	let data = params.data || {};
	// console.log('store.state.tenantId',store.state.tenantId)
	let header = {
		'Content-Type': 'application/json;charset=UTF-8',
		'X-Access-Token': store.state.token,
		'tenant-id': store.state.tenantId,
		...params.header
	};
	if (method == "post") {
		header = {
			'Content-Type': 'application/json',
			'X-Access-Token': store.state.token,
			'tenant-id': store.state.tenantId
		};
	}
	return new Promise((resolve, reject) => {
		uni.request({
			url: base_url + url,
			method: method,
			header: header,
			data: data,
			timeout,
			success(response) {
				const res = response
				// 根据返回的状态码做出对应的操作
				//获取成功
				if (res.statusCode == 200) {
					switch (res.data.code) {
						case 500:
							uni.showToast({
								title: res.data.message,
								duration: 2000,
								icon:'none'
							});
							break;
						default:
							resolve(res.data.result);
							break;
					}
				} else {
					switch (res.statusCode) {
						case 401:
							uni.showModal({
								title: "提示",
								content: "请登录",
								showCancel: false,
								success() {
								
									setTimeout(() => {
										if (uni.getStorageSync('doctorId')) {
											uni.reLaunch({
												url: '/pages/register/personal'
											})
										} else {
											uni.reLaunch({
												url: '/pages/login/login'
											})
										}
									}, 1000);
								},
							});
							break;
						case 404:
							wx.showToast({
								title: '请求地址不存在...',
								duration: 2000,
							})
							break;
						case 500:
							wx.showToast({
								title: res.data.message,
								duration: 2000,
							})

							setTimeout(() => {
								// uni.reLaunch({
								// 	url: '/pages/register/personal'
								// })
								if (uni.getStorageSync('doctorId')) {
									uni.reLaunch({
										url: '/pages/register/personal'
									})
								} else {
									uni.reLaunch({
										url: '/pages/login/login'
									})
								}
							}, 1000);
							break;

						default:
							wx.showToast({
								title: '请重试...',
								duration: 2000,
							})
							break;
					}
				}
			},
			fail(err) {
				console.log('err--', err)
				if (err.errMsg.indexOf('request:fail') !== -1) {
					wx.showToast({
						title: '网络异常',
						icon: "error",
						duration: 2000
					})
				} else {
					wx.showToast({
						title: '未知异常',
						duration: 2000
					})
				}
				reject(err);

			},
			// complete() {
			// 	// 不管成功还是失败都会执行
			// 	uni.hideLoading();
			// 	uni.hideToast();
			// }
		});
	}).catch(() => {});
};
export default request;