import {
	createStore
} from 'vuex';

const localUser = uni.getStorageSync('user') || {
	avatar: '',
	phone:'18253686860',
	statisticsDiary: 0, // 睡眠日记
	sleepAverage: '7小时2分', // 平均睡眠时长
	durationTraining: '222小时', // 训练时长
	"id": "1637654645069283329",
	"username": "18312345678",
	"realname": "张扬",
	"avatar": null,
	"birthday": "2000-02-03",
	"sex": 2,
	"email": null,
	"phone": "18312345678",
	"orgCode": "A01A01",
	"orgCodeTxt": null,
	"status": 1,
	"delFlag": 0,
	"workNo": null,
	"post": null,
	"telephone": null,
	"createBy": "yywys",
	"createTime": "2023-03-20 11:17:48",
	"updateBy": "yywys",
	"updateTime": "2023-05-04 11:03:31",
	"activitiSync": null,
	"userIdentity": 1,
	"departIds": null,
	"relTenantIds": "2",
	"clientId": null,
	"psMeasureUnitPriceList": null,
	"measureUnitPriceStatus": 0,
	"templateId": null,
	"roleId": "1436146582834745345",
	"openId": null,
	"unionId": null,
	"patientId": "1637654645090254850",
	"isShowIntroduction": 0,
	"signaturePath": null
}
const localName = uni.getStorageSync('username') || null
const localTenantId = uni.getStorageSync('tenantId') || ''
// const localToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************.Gbu5QO3tt93mrPoApEf5oohZUfk7JECf90U8JPx7BK4'
 const localToken =  uni.getStorageSync('token') 
 // console.log('localToken',localToken)
export default createStore({
	state: {
		user: localUser,
		username: localName,
		token: localToken,
		tenantId:localTenantId,
		navBarHeight:84
	},
	getters: {
		// state的计算属性
	},
	mutations: {
		// 更改state中状态的逻辑，同步操作
		setToken(state, token) {
			state.token = token
		},
		// 更改state中状态的逻辑，同步操作
		setTenantId(state, tenantId) {
			console.log('tenantId---',tenantId)
			state.tenantId = tenantId
		},
		setNavBarHeight(state,navBarHeight){
			state.navBarHeight = navBarHeight
		},
		setUser(state,user){
			state.user = user
		}
	},
	actions: {
		// 提交mutation，异步操作
	},

});